//
//  RealTradingManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class RealTradingManager: ObservableObject {
    @Published var realPortfolio: RealPortfolio?
    @Published var realHoldings: [RealHolding] = []
    @Published var realTransactions: [RealTransaction] = []
    @Published var accountInfo: AlpacaAccountInfo?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var isConnected = false
    @Published var pendingOrders: [RealOrder] = []
    
    private let alpacaService = AlpacaService.shared
    private let supabaseService = SupabaseService.shared
    private let subscriptionManager = SubscriptionManager()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Account Management
    
    func connectBrokerageAccount(apiKey: String, secretKey: String, isPaper: Bool = true) async {
        // Pro tier check
        guard subscriptionManager.canAccessFeature(.realInvestments) else {
            await MainActor.run {
                self.errorMessage = "Real trading requires Pro subscription. Upgrade to access live trading."
            }
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Configure Alpaca credentials
            alpacaService.configure(apiKey: apiKey, secretKey: secretKey, isPaper: isPaper)
            
            // Test connection by fetching account info
            let account = try await alpacaService.getAccountInfo()
            
            await MainActor.run {
                self.accountInfo = account
                self.isConnected = true
                self.isLoading = false
            }
            
            // Load initial portfolio data
            await loadRealPortfolio()
            
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to connect brokerage account: \(error.localizedDescription)"
                self.isLoading = false
                self.isConnected = false
            }
        }
    }
    
    func disconnectBrokerageAccount() async {
        await MainActor.run {
            self.isConnected = false
            self.accountInfo = nil
            self.realPortfolio = nil
            self.realHoldings.removeAll()
            self.realTransactions.removeAll()
            self.pendingOrders.removeAll()
        }
        
        alpacaService.disconnect()
    }
    
    // MARK: - Portfolio Management
    
    func loadRealPortfolio() async {
        guard isConnected else { return }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // Load account info
            let account = try await alpacaService.getAccountInfo()
            
            // Load positions
            let positions = try await alpacaService.getPositions()
            
            // Load recent orders
            let orders = try await alpacaService.getOrders(status: .all, limit: 50)
            
            // Convert to our models
            let holdings = positions.map { position in
                RealHolding(
                    symbol: position.symbol,
                    quantity: position.qty,
                    marketValue: position.marketValue,
                    costBasis: position.costBasis,
                    unrealizedPL: position.unrealizedPL,
                    unrealizedPLPercent: position.unrealizedPLPercent,
                    currentPrice: position.currentPrice,
                    lastUpdated: Date()
                )
            }
            
            let transactions = orders.compactMap { order -> RealTransaction? in
                guard order.status == "filled" else { return nil }
                return RealTransaction(
                    orderId: order.id,
                    symbol: order.symbol,
                    side: order.side,
                    quantity: order.qty,
                    price: order.filledAvgPrice ?? 0,
                    timestamp: order.filledAt ?? order.createdAt,
                    fees: 0 // Alpaca has commission-free trading
                )
            }
            
            let portfolio = RealPortfolio(
                accountId: account.id,
                totalValue: account.portfolioValue,
                buyingPower: account.buyingPower,
                cash: account.cash,
                dayChange: account.dayChange,
                dayChangePercent: account.dayChangePercent
            )
            
            await MainActor.run {
                self.accountInfo = account
                self.realPortfolio = portfolio
                self.realHoldings = holdings
                self.realTransactions = transactions
                self.pendingOrders = orders.filter { $0.status != "filled" && $0.status != "cancelled" }
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load portfolio: \(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }
    
    // MARK: - Trading Operations
    
    func placeMarketOrder(symbol: String, quantity: Double, side: OrderSide) async -> Bool {
        guard isConnected else {
            await MainActor.run {
                self.errorMessage = "Not connected to brokerage account"
            }
            return false
        }
        
        // Pro tier check
        guard subscriptionManager.canAccessFeature(.realInvestments) else {
            await MainActor.run {
                self.errorMessage = "Real trading requires Pro subscription"
            }
            return false
        }
        
        // Create trade order for safety validation
        let currentPrice = await getCurrentPrice(symbol: symbol)
        let tradeOrder = TradeOrder(
            symbol: symbol,
            side: side.rawValue,
            quantity: quantity,
            orderType: OrderType.market.rawValue,
            totalValue: quantity * currentPrice,
            estimatedFees: 0 // Alpaca is commission-free
        )

        // Validate with safety manager
        let validation = await TradingSafetyManager.shared.validateTradeOrder(tradeOrder)

        guard validation.isValid else {
            await MainActor.run {
                self.errorMessage = validation.issues.first?.description ?? "Trade validation failed"
            }
            return false
        }

        // Check if authentication is required
        if TradingSafetyManager.shared.requiresAuthentication(for: tradeOrder) {
            let authResult = await TradingSafetyManager.shared.authenticateUser()

            switch authResult {
            case .success:
                break // Continue with trade
            case .failed(let error):
                await MainActor.run {
                    self.errorMessage = "Authentication failed: \(error)"
                }
                return false
            case .cancelled:
                await MainActor.run {
                    self.errorMessage = "Trade cancelled by user"
                }
                return false
            case .fallbackToPasscode:
                // Handle passcode fallback
                break
            }
        }

        do {
            let order = try await alpacaService.placeOrder(
                symbol: symbol,
                qty: quantity,
                side: side,
                type: .market,
                timeInForce: .gtc
            )

            await MainActor.run {
                self.pendingOrders.append(order)
            }

            // Record trade with safety manager
            let executionResult = TradeExecutionResult(
                status: .pending,
                executionPrice: currentPrice,
                fees: 0,
                timestamp: Date(),
                message: nil
            )

            TradingSafetyManager.shared.recordTrade(tradeOrder, result: executionResult)

            // Refresh portfolio after order
            await loadRealPortfolio()

            return true

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to place order: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    func placeLimitOrder(symbol: String, quantity: Double, side: OrderSide, limitPrice: Double) async -> Bool {
        guard isConnected else {
            await MainActor.run {
                self.errorMessage = "Not connected to brokerage account"
            }
            return false
        }
        
        // Pro tier check
        guard subscriptionManager.canAccessFeature(.realInvestments) else {
            await MainActor.run {
                self.errorMessage = "Real trading requires Pro subscription"
            }
            return false
        }
        
        do {
            let order = try await alpacaService.placeLimitOrder(
                symbol: symbol,
                qty: quantity,
                side: side,
                limitPrice: limitPrice,
                timeInForce: .gtc
            )
            
            await MainActor.run {
                self.pendingOrders.append(order)
            }
            
            // Refresh portfolio after order
            await loadRealPortfolio()
            
            return true
            
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to place limit order: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    func cancelOrder(_ orderId: String) async -> Bool {
        guard isConnected else { return false }
        
        do {
            try await alpacaService.cancelOrder(orderId)
            
            await MainActor.run {
                self.pendingOrders.removeAll { $0.id == orderId }
            }
            
            return true
            
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to cancel order: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    // MARK: - Risk Management
    
    func validateOrder(symbol: String, quantity: Double, side: OrderSide, price: Double?) -> OrderValidationResult {
        guard let account = accountInfo else {
            return .invalid("Account not connected")
        }
        
        // Check buying power for buy orders
        if side == .buy {
            let estimatedCost = price != nil ? quantity * price! : quantity * 100 // Rough estimate for market orders
            
            if estimatedCost > account.buyingPower {
                return .invalid("Insufficient buying power. Available: $\(String(format: "%.2f", account.buyingPower))")
            }
        }
        
        // Check position for sell orders
        if side == .sell {
            let holding = realHoldings.first { $0.symbol == symbol }
            let availableShares = holding?.quantity ?? 0
            
            if quantity > availableShares {
                return .invalid("Insufficient shares. Available: \(String(format: "%.0f", availableShares))")
            }
        }
        
        // Check minimum order size
        if quantity < 1 {
            return .invalid("Minimum order size is 1 share")
        }
        
        // Check maximum order size (risk management)
        let maxOrderValue = account.portfolioValue * 0.1 // Max 10% of portfolio per order
        let estimatedOrderValue = price != nil ? quantity * price! : quantity * 100
        
        if estimatedOrderValue > maxOrderValue {
            return .warning("Order exceeds 10% of portfolio value. Consider reducing size.")
        }
        
        return .valid
    }
    
    // MARK: - Portfolio Analytics
    
    func getPortfolioPerformance() -> RealPortfolioPerformance? {
        guard let portfolio = realPortfolio else { return nil }
        
        let totalGainLoss = realHoldings.reduce(0) { $0 + $1.unrealizedPL }
        let totalGainLossPercent = portfolio.totalValue > 0 ? (totalGainLoss / portfolio.totalValue) * 100 : 0
        
        let bestPerformer = realHoldings.max { $0.unrealizedPLPercent < $1.unrealizedPLPercent }
        let worstPerformer = realHoldings.min { $0.unrealizedPLPercent < $1.unrealizedPLPercent }
        
        return RealPortfolioPerformance(
            totalValue: portfolio.totalValue,
            totalGainLoss: totalGainLoss,
            totalGainLossPercent: totalGainLossPercent,
            dayChange: portfolio.dayChange,
            dayChangePercent: portfolio.dayChangePercent,
            bestPerformer: bestPerformer,
            worstPerformer: worstPerformer,
            cashBalance: portfolio.cash,
            buyingPower: portfolio.buyingPower
        )
    }
    
    // MARK: - Compliance & Safety
    
    func checkTradingRestrictions(symbol: String) async -> TradingRestriction? {
        // Check if symbol is restricted
        let restrictedSymbols = ["GME", "AMC"] // Example restricted symbols
        if restrictedSymbols.contains(symbol) {
            return .restricted("This symbol has trading restrictions")
        }
        
        // Check market hours
        if !isMarketOpen() {
            return .afterHours("Market is closed. Order will be queued for next market open.")
        }
        
        // Check pattern day trading rules
        if await isPDTRestricted() {
            return .pdtRestricted("Pattern Day Trading restrictions apply")
        }
        
        return nil
    }
    
    private func isMarketOpen() -> Bool {
        let calendar = Calendar.current
        let now = Date()
        
        // Check if it's a weekday
        let weekday = calendar.component(.weekday, from: now)
        guard weekday >= 2 && weekday <= 6 else { return false } // Monday = 2, Friday = 6
        
        // Check market hours (9:30 AM - 4:00 PM ET)
        let formatter = DateFormatter()
        formatter.timeZone = TimeZone(identifier: "America/New_York")
        formatter.dateFormat = "HH:mm"
        
        let timeString = formatter.string(from: now)
        return timeString >= "09:30" && timeString <= "16:00"
    }
    
    private func isPDTRestricted() async -> Bool {
        guard let account = accountInfo else { return false }
        
        // PDT rules apply to accounts under $25,000
        if account.portfolioValue < 25000 {
            // Check recent day trades (would need to implement day trade counting)
            return false // Simplified for now
        }
        
        return false
    }
    
    // MARK: - Helper Methods
    
    func refreshData() async {
        await loadRealPortfolio()
    }
    
    func getHolding(for symbol: String) -> RealHolding? {
        return realHoldings.first { $0.symbol == symbol }
    }
    
    func getTotalInvested() -> Double {
        return realHoldings.reduce(0) { $0 + $1.costBasis }
    }
    
    func getTotalUnrealizedGainLoss() -> Double {
        return realHoldings.reduce(0) { $0 + $1.unrealizedPL }
    }

    // MARK: - Safety Helper Methods

    private func getCurrentPrice(symbol: String) async -> Double {
        // Get current market price for the symbol
        // This would integrate with your market data service
        // Use market service for quotes
        return 100.0 // Placeholder - implement with real market data
    }
}
