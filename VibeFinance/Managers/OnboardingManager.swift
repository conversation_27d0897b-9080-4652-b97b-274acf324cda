//
//  OnboardingManager.swift
//  WealthVibe - Enhanced Onboarding Management
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI
import Foundation

@MainActor
class OnboardingManager: ObservableObject {
    @Published var isOnboardingComplete = false
    @Published var currentOnboardingStep = 0
    @Published var userProfile = GenZUserProfile()
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let userManager: UserManager
    private let gamificationManager: GamificationManager
    
    init(userManager: UserManager, gamificationManager: GamificationManager) {
        self.userManager = userManager
        self.gamificationManager = gamificationManager
        checkOnboardingStatus()
    }
    
    // MARK: - Onboarding Status
    
    func checkOnboardingStatus() {
        // Check if user has completed enhanced onboarding
        // For now, we'll check if they have preferences set
        isOnboardingComplete = userManager.hasCompletedOnboarding
    }
    
    func completeOnboarding() async {
        isLoading = true
        errorMessage = nil

        // Save user profile data
        await saveUserProfile()

        // Grant starter rewards
        await grantStarterRewards()

        // Set up personalized experience
        await setupPersonalizedExperience()

        // Mark onboarding as complete
        await markOnboardingComplete()

        isOnboardingComplete = true
        isLoading = false
    }
    
    // MARK: - Profile Management
    
    private func saveUserProfile() async {
        // Convert GenZUserProfile to UserPreferences
        var preferences = UserPreferences()
        
        // Map personality traits to interests
        preferences.interests = userProfile.personalityTraits.map { trait in
            switch trait {
            case .techSavvy: return "Technology"
            case .creative: return "Creative Industries"
            case .social: return "Social Impact"
            case .analytical: return "Data & Analytics"
            case .adventurous: return "Emerging Markets"
            case .cautious: return "Conservative Investing"
            case .ambitious: return "Growth Stocks"
            case .trendy: return "Trending Stocks"
            }
        }
        
        // Map investment style to risk tolerance
        preferences.riskTolerance = mapInvestmentStyleToRiskTolerance(userProfile.investmentStyle)
        
        // Map financial goals
        preferences.goals = userProfile.financialGoals.map { $0.rawValue }
        
        // Set communication preferences (store in interests for now)
        preferences.interests.append("communication_\(userProfile.communicationStyle.rawValue)")
        preferences.interests.append("learning_\(userProfile.preferredLearningStyle.rawValue)")

        // Social preferences (store in goals for now)
        if userProfile.socialPreferences.shareAchievements {
            preferences.goals.append("social_sharing")
        }
        if userProfile.socialPreferences.joinSquads {
            preferences.goals.append("squad_participation")
        }
        
        // Save to user manager
        await userManager.updatePreferences(preferences)
    }
    
    private func mapInvestmentStyleToRiskTolerance(_ style: InvestmentStyle) -> RiskTolerance {
        switch style {
        case .conservative: return .conservative
        case .balanced: return .moderate
        case .aggressive: return .aggressive
        case .trendy, .tech: return .moderate
        case .ethical: return .moderate
        case .unknown: return .moderate
        }
    }
    
    // MARK: - Starter Rewards
    
    private func grantStarterRewards() async {
        // Grant virtual money
        await userManager.addVirtualBalance(10000.0)
        
        // Grant XP bonus
        await gamificationManager.addXP(100, source: "onboarding_bonus")
        
        // Unlock first achievement
        await gamificationManager.unlockAchievement("welcome_aboard")
        
        // Create starter portfolio if user wants to invest
        if userProfile.investmentStyle != .unknown {
            await createStarterPortfolio()
        }
    }
    
    private func createStarterPortfolio() async {
        // Create a sample portfolio based on investment style
        let starterStocks = getStarterStocks(for: userProfile.investmentStyle)
        
        // Add to user's watchlist
        for stock in starterStocks {
            await userManager.addToWatchlist(stock)
        }
    }
    
    private func getStarterStocks(for style: InvestmentStyle) -> [String] {
        switch style {
        case .conservative:
            return ["AAPL", "MSFT", "JNJ", "PG", "KO"]
        case .balanced:
            return ["AAPL", "GOOGL", "AMZN", "TSLA", "NVDA"]
        case .aggressive:
            return ["TSLA", "NVDA", "AMD", "PLTR", "ARKK"]
        case .trendy:
            return ["TSLA", "GME", "AMC", "DOGE", "SHIB"]
        case .ethical:
            return ["ESG", "ICLN", "PBW", "QCLN", "ERTH"]
        case .tech:
            return ["AAPL", "GOOGL", "MSFT", "NVDA", "META"]
        case .unknown:
            return ["AAPL", "MSFT", "GOOGL"]
        }
    }
    
    // MARK: - Personalized Experience Setup
    
    private func setupPersonalizedExperience() async {
        // Set up personalized feed preferences
        await setupFeedPreferences()
        
        // Configure quest recommendations
        await setupQuestPreferences()
        
        // Set up social features
        await setupSocialFeatures()
        
        // Configure notifications
        await setupNotificationPreferences()
    }
    
    private func setupFeedPreferences() async {
        var feedPreferences: [String] = []
        
        // Add content based on personality traits
        for trait in userProfile.personalityTraits {
            switch trait {
            case .analytical:
                feedPreferences.append("market_analysis")
                feedPreferences.append("financial_data")
            case .trendy:
                feedPreferences.append("trending_stocks")
                feedPreferences.append("social_sentiment")
            case .techSavvy:
                feedPreferences.append("tech_news")
                feedPreferences.append("crypto_updates")
            case .social:
                feedPreferences.append("community_insights")
                feedPreferences.append("squad_activities")
            default:
                break
            }
        }
        
        // Add content based on financial goals
        for goal in userProfile.financialGoals {
            switch goal {
            case .retirement:
                feedPreferences.append("retirement_planning")
            case .houseDown:
                feedPreferences.append("real_estate")
            case .business:
                feedPreferences.append("entrepreneurship")
            case .travel:
                feedPreferences.append("travel_budgeting")
            default:
                break
            }
        }
        
        await userManager.updateFeedPreferences(feedPreferences)
    }
    
    private func setupQuestPreferences() async {
        var questTypes: [String] = []
        
        // Based on experience level and learning style
        switch userProfile.preferredLearningStyle {
        case .visual:
            questTypes.append("chart_reading")
            questTypes.append("visual_analysis")
        case .interactive:
            questTypes.append("trading_simulation")
            questTypes.append("portfolio_building")
        case .social:
            questTypes.append("squad_challenges")
            questTypes.append("community_quests")
        case .bite_sized:
            questTypes.append("daily_tips")
            questTypes.append("quick_lessons")
        }
        
        await gamificationManager.setPreferredQuestTypes(questTypes)
    }
    
    private func setupSocialFeatures() async {
        if userProfile.socialPreferences.joinSquads {
            // Find and suggest squads based on interests
            await findRecommendedSquads()
        }
        
        if userProfile.socialPreferences.competitiveMode {
            // Enable competitive features
            await gamificationManager.enableCompetitiveMode()
        }
        
        if userProfile.socialPreferences.publicProfile {
            // Make profile discoverable
            await userManager.setProfileVisibility(true)
        }
    }
    
    private func setupNotificationPreferences() async {
        var notificationTypes: [String] = []
        
        // Based on communication style
        switch userProfile.communicationStyle {
        case .casual:
            notificationTypes.append("casual_reminders")
        case .professional:
            notificationTypes.append("formal_updates")
        case .funny:
            notificationTypes.append("humorous_tips")
        case .motivational:
            notificationTypes.append("motivational_quotes")
        }
        
        await userManager.updateNotificationPreferences(notificationTypes)
    }
    
    private func findRecommendedSquads() async {
        // This would typically call a service to find squads
        // For now, we'll simulate this
        let recommendedSquads = ["Tech Innovators", "Sustainable Investors", "Growth Hunters"]
        await userManager.setRecommendedSquads(recommendedSquads)
    }
    
    private func markOnboardingComplete() async {
        await userManager.setEnhancedOnboardingComplete(true)
    }
    
    // MARK: - Helper Methods
    
    func resetOnboarding() {
        currentOnboardingStep = 0
        userProfile = GenZUserProfile()
        isOnboardingComplete = false
        errorMessage = nil
    }
    
    func getPersonalityInsights() -> [String] {
        var insights: [String] = []
        
        for trait in userProfile.personalityTraits {
            switch trait {
            case .adventurous:
                insights.append("You're likely to enjoy exploring new investment opportunities")
            case .analytical:
                insights.append("You'll appreciate detailed market analysis and data-driven insights")
            case .creative:
                insights.append("You might be interested in creative industries and innovative companies")
            case .social:
                insights.append("You'll benefit from community-driven investment strategies")
            case .cautious:
                insights.append("You prefer stable, low-risk investment options")
            case .ambitious:
                insights.append("You're motivated by high-growth potential investments")
            case .techSavvy:
                insights.append("Technology stocks and crypto might align with your interests")
            case .trendy:
                insights.append("You're likely to follow market trends and social sentiment")
            }
        }
        
        return insights
    }
    
    func getRecommendedContent() -> [String] {
        var content: [String] = []
        
        // Based on investment style
        switch userProfile.investmentStyle {
        case .conservative:
            content.append("Blue-chip stock analysis")
            content.append("Dividend investing strategies")
        case .aggressive:
            content.append("Growth stock opportunities")
            content.append("Options trading basics")
        case .ethical:
            content.append("ESG investing guide")
            content.append("Sustainable finance trends")
        case .tech:
            content.append("Tech sector analysis")
            content.append("Innovation investment themes")
        default:
            content.append("Balanced portfolio strategies")
            content.append("Market fundamentals")
        }
        
        return content
    }
}

// MARK: - Extensions for UserManager
extension UserManager {
    var hasCompletedOnboarding: Bool {
        // Check if user has completed onboarding
        return user?.preferences != nil
    }

    func addVirtualBalance(_ amount: Double) async {
        // Implementation would add to user's virtual balance
        // For now, this is a placeholder
    }

    func addToWatchlist(_ symbol: String) async {
        // Implementation would add stock to user's watchlist
        // For now, this is a placeholder
    }

    func updateFeedPreferences(_ preferences: [String]) async {
        // Implementation would update user's feed preferences
        // For now, this is a placeholder
    }

    func setProfileVisibility(_ isPublic: Bool) async {
        // Implementation would update profile visibility
        // For now, this is a placeholder
    }

    func updateNotificationPreferences(_ types: [String]) async {
        // Implementation would update notification settings
        // For now, this is a placeholder
    }

    func setRecommendedSquads(_ squads: [String]) async {
        // Implementation would set recommended squads
        // For now, this is a placeholder
    }

    func setEnhancedOnboardingComplete(_ complete: Bool) async {
        // Implementation would mark onboarding as complete
        // For now, this is a placeholder
    }
}



// MARK: - Extensions for GamificationManager
extension GamificationManager {
    func addXP(_ amount: Int, source: String) async {
        // Implementation would add XP to user
        // For now, this is a placeholder
    }

    func unlockAchievement(_ achievementId: String) async {
        // Implementation would unlock achievement
        // For now, this is a placeholder
    }

    func setPreferredQuestTypes(_ types: [String]) async {
        // Implementation would set preferred quest types
        // For now, this is a placeholder
    }

    func enableCompetitiveMode() async {
        // Implementation would enable competitive features
        // For now, this is a placeholder
    }
}
