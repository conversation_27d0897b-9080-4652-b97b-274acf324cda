//
//  AuthManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class AuthManager: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var isDevelopmentMode = false
    @Published var useMockAuth = false

    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()

    // Mock user data for development
    private let mockUsers = [
        User(id: UUID(), email: "<EMAIL>", username: "DevUser"),
        User(id: UUID(), email: "<EMAIL>", username: "TestUser"),
        User(id: UUID(), email: "<EMAIL>", username: "DemoUser")
    ]

    init() {
        #if DEBUG
        isDevelopmentMode = DevelopmentConfig.isDevelopmentMode
        // Check if mock auth is enabled from UserDefaults or config
        useMockAuth = UserDefaults.standard.bool(forKey: "useMockAuth") || DevelopmentConfig.enableMockAuth
        DevelopmentConfig.printDevelopmentInfo()

        ProductionConfig.log("AuthManager initialized - Dev mode: \(isDevelopmentMode), Mock auth: \(useMockAuth)", category: "AUTH", level: .info)

        // Force authentication for debugging
        isAuthenticated = true
        currentUser = mockUsers.first

        // Auto-login in development mode for easier testing
        if isDevelopmentMode && DevelopmentConfig.enableMockAuth {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.quickDevLogin()
            }
        }
        #else
        isDevelopmentMode = false
        useMockAuth = false
        #endif

        // checkAuthStatus() // Temporarily disabled
    }

    // MARK: - Development Mode Methods

    func toggleMockAuth() {
        useMockAuth.toggle()
        UserDefaults.standard.set(useMockAuth, forKey: "useMockAuth")

        ProductionConfig.log("Mock auth toggled to: \(useMockAuth)", category: "AUTH", level: .info)

        if !useMockAuth && isAuthenticated {
            // If turning off mock auth while authenticated, sign out
            Task {
                await signOut()
            }
        }
    }

    func mockSignIn(userIndex: Int = 0) {
        guard isDevelopmentMode && useMockAuth else { return }

        isLoading = true
        errorMessage = nil

        // Simulate network delay
        Task { @MainActor in
            try? await Task.sleep(nanoseconds: 500_000_000)
            let selectedUser = self.mockUsers[min(userIndex, self.mockUsers.count - 1)]
            self.currentUser = selectedUser
            self.isAuthenticated = true
            self.isLoading = false
            self.errorMessage = nil
        }
    }

    func quickDevLogin() {
        guard isDevelopmentMode else { return }
        mockSignIn(userIndex: 0)
    }

    // MARK: - Authentication Methods

    func signUp(email: String, password: String, username: String) async {
        // Use mock authentication if enabled
        if isDevelopmentMode && useMockAuth {
            isLoading = true
            errorMessage = nil

            // Simulate network delay
            try? await Task.sleep(nanoseconds: 500_000_000)

            let mockUser = User(id: UUID(), email: email, username: username)
            await MainActor.run {
                self.currentUser = mockUser
                self.isAuthenticated = true
                self.isLoading = false
            }
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            // Create user account
            let authResult = try await supabaseService.signUp(email: email, password: password)

            // Create user profile
            let user = User(
                id: authResult.user.id,
                email: email,
                username: username
            )

            try await supabaseService.createUser(user)

            await MainActor.run {
                self.currentUser = user
                self.isAuthenticated = true
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func signIn(email: String, password: String) async {
        // Use mock authentication if enabled
        if isDevelopmentMode && useMockAuth {
            isLoading = true
            errorMessage = nil

            // Simulate network delay
            try? await Task.sleep(nanoseconds: 500_000_000)

            // Find mock user by email or create new one
            let mockUser = mockUsers.first { $0.email == email } ??
                          User(id: UUID(), email: email, username: email.components(separatedBy: "@").first ?? "user")

            await MainActor.run {
                self.currentUser = mockUser
                self.isAuthenticated = true
                self.isLoading = false
            }
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let authResult = try await supabaseService.signIn(email: email, password: password)

            // Try to get user profile, if it doesn't exist, create it
            do {
                let user = try await supabaseService.getUser(id: authResult.user.id)
                await MainActor.run {
                    self.currentUser = user
                    self.isAuthenticated = true
                    self.isLoading = false
                }
            } catch SupabaseError.userNotFound {
                // User profile doesn't exist, create it
                let user = User(
                    id: authResult.user.id,
                    email: authResult.user.email,
                    username: authResult.user.email.components(separatedBy: "@").first ?? "user"
                )

                try await supabaseService.createUser(user)

                await MainActor.run {
                    self.currentUser = user
                    self.isAuthenticated = true
                    self.isLoading = false
                }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func signOut() async {
        // Handle mock authentication
        if isDevelopmentMode && useMockAuth {
            await MainActor.run {
                self.currentUser = nil
                self.isAuthenticated = false
                self.isLoading = false
                self.errorMessage = nil
            }
            return
        }

        isLoading = true

        do {
            try await supabaseService.signOut()

            await MainActor.run {
                self.currentUser = nil
                self.isAuthenticated = false
                self.isLoading = false
                self.errorMessage = nil
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func resetPassword(email: String) async {
        isLoading = true
        errorMessage = nil

        do {
            try await supabaseService.resetPassword(email: email)

            await MainActor.run {
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func deleteAccount() async {
        guard let user = currentUser else { return }

        isLoading = true
        errorMessage = nil

        do {
            try await supabaseService.deleteUser(id: user.id)

            await MainActor.run {
                self.currentUser = nil
                self.isAuthenticated = false
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    // MARK: - Helper Methods

    private func checkAuthStatus() {
        Task {
            do {
                if let session = try await supabaseService.getCurrentSession() {
                    let user = try await supabaseService.getUser(id: session.user.id)

                    await MainActor.run {
                        self.currentUser = user
                        self.isAuthenticated = true
                    }
                }
            } catch {
                await MainActor.run {
                    self.isAuthenticated = false
                    self.currentUser = nil
                }
            }
        }
    }

    func clearError() {
        errorMessage = nil
    }

    // MARK: - Validation

    func validateEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }

    func validatePassword(_ password: String) -> (isValid: Bool, message: String?) {
        if password.count < 8 {
            return (false, "Password must be at least 8 characters long")
        }

        if !password.contains(where: { $0.isUppercase }) {
            return (false, "Password must contain at least one uppercase letter")
        }

        if !password.contains(where: { $0.isLowercase }) {
            return (false, "Password must contain at least one lowercase letter")
        }

        if !password.contains(where: { $0.isNumber }) {
            return (false, "Password must contain at least one number")
        }

        return (true, nil)
    }

    func validateUsername(_ username: String) -> (isValid: Bool, message: String?) {
        if username.count < 3 {
            return (false, "Username must be at least 3 characters long")
        }

        if username.count > 20 {
            return (false, "Username must be less than 20 characters")
        }

        let usernameRegex = "^[a-zA-Z0-9_]+$"
        let usernamePredicate = NSPredicate(format:"SELF MATCHES %@", usernameRegex)
        if !usernamePredicate.evaluate(with: username) {
            return (false, "Username can only contain letters, numbers, and underscores")
        }

        return (true, nil)
    }
}

// MARK: - Auth Errors
enum AuthError: LocalizedError {
    case invalidEmail
    case invalidPassword
    case invalidUsername
    case userNotFound
    case emailAlreadyExists
    case usernameAlreadyExists
    case networkError
    case unknownError

    var errorDescription: String? {
        switch self {
        case .invalidEmail:
            return "Please enter a valid email address"
        case .invalidPassword:
            return "Password must be at least 8 characters with uppercase, lowercase, and number"
        case .invalidUsername:
            return "Username must be 3-20 characters with letters, numbers, and underscores only"
        case .userNotFound:
            return "No account found with this email"
        case .emailAlreadyExists:
            return "An account with this email already exists"
        case .usernameAlreadyExists:
            return "This username is already taken"
        case .networkError:
            return "Network error. Please check your connection and try again"
        case .unknownError:
            return "An unexpected error occurred. Please try again"
        }
    }
}
