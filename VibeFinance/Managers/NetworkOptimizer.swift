//
//  NetworkOptimizer.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Network
import Combine

class NetworkOptimizer: ObservableObject {
    static let shared = NetworkOptimizer()
    
    @Published var networkStatus: NetworkStatus = .unknown
    @Published var connectionQuality: ConnectionQuality = .good
    
    private let monitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "network.monitor")
    private let requestQueue = DispatchQueue(label: "network.requests", qos: .utility, attributes: .concurrent)
    
    private var networkStatistics = NetworkStatistics()
    private var requestHistory: [NetworkRequest] = []
    private var activeRequests: Set<UUID> = []
    private var isDebugLoggingEnabled = false
    private var isBatterySavingMode = false
    private var isLowMemoryMode = false
    
    // Configuration
    private var maxConcurrentRequests: Int = 10
    private var requestTimeout: TimeInterval = 30.0
    private var retryAttempts: Int = 3
    private var retryDelay: TimeInterval = 1.0
    
    // Rate limiting
    private var requestCounts: [String: (count: Int, resetTime: Date)] = [:]
    private let rateLimitWindow: TimeInterval = 60.0 // 1 minute
    private let maxRequestsPerMinute: Int = 100
    
    private init() {
        setupNetworkMonitoring()
        setupRequestCleanup()
    }
    
    // MARK: - Network Monitoring
    
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path)
            }
        }
        monitor.start(queue: monitorQueue)
    }
    
    private func updateNetworkStatus(_ path: NWPath) {
        // Update network status
        switch path.status {
        case .satisfied:
            networkStatus = path.isExpensive ? .cellular : .wifi
        case .unsatisfied:
            networkStatus = .offline
        case .requiresConnection:
            networkStatus = .unknown
        @unknown default:
            networkStatus = .unknown
        }
        
        // Estimate connection quality
        estimateConnectionQuality(path)
        
        // Adjust optimization settings based on network
        adjustOptimizationSettings()
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Network status updated: \(networkStatus), quality: \(connectionQuality)", category: "NETWORK")
        }
    }
    
    private func estimateConnectionQuality(_ path: NWPath) {
        if path.status != .satisfied {
            connectionQuality = .poor
            return
        }
        
        // This is a simplified quality estimation
        // In a real implementation, you'd measure actual latency and throughput
        if path.isExpensive {
            connectionQuality = .fair // Cellular is generally slower
        } else {
            connectionQuality = .good // WiFi is generally faster
        }
    }
    
    private func adjustOptimizationSettings() {
        switch connectionQuality {
        case .poor:
            maxConcurrentRequests = 3
            requestTimeout = 60.0
            retryAttempts = 5
        case .fair:
            maxConcurrentRequests = 6
            requestTimeout = 45.0
            retryAttempts = 3
        case .good:
            maxConcurrentRequests = 10
            requestTimeout = 30.0
            retryAttempts = 2
        }
        
        if isBatterySavingMode {
            maxConcurrentRequests = max(1, maxConcurrentRequests / 2)
            requestTimeout *= 1.5
        }
    }
    
    // MARK: - Request Optimization
    
    func optimizedRequest<T: Codable>(
        url: URL,
        method: HTTPMethod = .GET,
        headers: [String: String] = [:],
        body: Data? = nil,
        responseType: T.Type,
        priority: RequestPriority = .normal,
        cachePolicy: CachePolicy = .default
    ) async throws -> T {
        
        // Check rate limiting
        try checkRateLimit(for: url.host ?? "unknown")
        
        // Wait for available slot if needed
        await waitForAvailableSlot(priority: priority)
        
        let requestId = UUID()
        let request = NetworkRequest(
            id: requestId,
            url: url,
            method: method,
            priority: priority,
            startTime: Date()
        )
        
        activeRequests.insert(requestId)
        requestHistory.append(request)
        
        defer {
            activeRequests.remove(requestId)
        }
        
        do {
            let result: T = try await performRequest(
                url: url,
                method: method,
                headers: headers,
                body: body,
                responseType: responseType,
                requestId: requestId
            )
            
            // Update statistics
            updateRequestStatistics(request: request, success: true)
            
            return result
            
        } catch {
            updateRequestStatistics(request: request, success: false, error: error)
            throw error
        }
    }
    
    private func performRequest<T: Codable>(
        url: URL,
        method: HTTPMethod,
        headers: [String: String],
        body: Data?,
        responseType: T.Type,
        requestId: UUID
    ) async throws -> T {
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = method.rawValue
        urlRequest.timeoutInterval = requestTimeout
        
        // Add headers
        for (key, value) in headers {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }
        
        // Add body if provided
        if let body = body {
            urlRequest.httpBody = body
        }
        
        // Add optimization headers
        addOptimizationHeaders(to: &urlRequest)
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Starting request: \(method.rawValue) \(url)", category: "NETWORK")
        }
        
        // Perform request with retry logic
        return try await performRequestWithRetry(urlRequest, responseType: responseType, requestId: requestId)
    }
    
    private func performRequestWithRetry<T: Codable>(
        _ request: URLRequest,
        responseType: T.Type,
        requestId: UUID,
        attempt: Int = 1
    ) async throws -> T {
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            // Check for rate limiting
            if httpResponse.statusCode == 429 {
                if attempt <= retryAttempts {
                    let delay = retryDelay * Double(attempt)
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                    return try await performRequestWithRetry(request, responseType: responseType, requestId: requestId, attempt: attempt + 1)
                } else {
                    throw NetworkError.rateLimited
                }
            }
            
            guard 200...299 ~= httpResponse.statusCode else {
                throw NetworkError.httpError(httpResponse.statusCode)
            }
            
            // Parse response
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let result = try decoder.decode(responseType, from: data)
            
            if isDebugLoggingEnabled {
                DevelopmentConfig.log("Request completed: \(request.url?.absoluteString ?? "unknown")", category: "NETWORK")
            }
            
            return result
            
        } catch {
            if attempt <= retryAttempts && shouldRetry(error: error) {
                let delay = retryDelay * Double(attempt)
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                return try await performRequestWithRetry(request, responseType: responseType, requestId: requestId, attempt: attempt + 1)
            } else {
                throw error
            }
        }
    }
    
    private func shouldRetry(error: Error) -> Bool {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .timeout, .connectionLost, .serverError:
                return true
            default:
                return false
            }
        }
        
        if let urlError = error as? URLError {
            switch urlError.code {
            case .timedOut, .networkConnectionLost, .notConnectedToInternet:
                return true
            default:
                return false
            }
        }
        
        return false
    }
    
    // MARK: - Request Management
    
    private func waitForAvailableSlot(priority: RequestPriority) async {
        while activeRequests.count >= maxConcurrentRequests {
            // Higher priority requests can bypass some limits
            if priority == .high && activeRequests.count < maxConcurrentRequests * 2 {
                break
            }
            
            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
        }
    }
    
    private func checkRateLimit(for host: String) throws {
        let now = Date()
        
        if let existing = requestCounts[host] {
            if now < existing.resetTime {
                if existing.count >= maxRequestsPerMinute {
                    throw NetworkError.rateLimited
                }
                requestCounts[host] = (existing.count + 1, existing.resetTime)
            } else {
                requestCounts[host] = (1, now.addingTimeInterval(rateLimitWindow))
            }
        } else {
            requestCounts[host] = (1, now.addingTimeInterval(rateLimitWindow))
        }
    }
    
    private func addOptimizationHeaders(to request: inout URLRequest) {
        // Add compression support
        request.setValue("gzip, deflate", forHTTPHeaderField: "Accept-Encoding")
        
        // Add cache control based on network quality
        switch connectionQuality {
        case .poor:
            request.setValue("max-age=300", forHTTPHeaderField: "Cache-Control")
        case .fair:
            request.setValue("max-age=180", forHTTPHeaderField: "Cache-Control")
        case .good:
            request.setValue("max-age=60", forHTTPHeaderField: "Cache-Control")
        }
        
        // Add user agent
        request.setValue("VibeFinance/1.0", forHTTPHeaderField: "User-Agent")
    }
    
    // MARK: - Performance Modes
    
    func enableBatterySavingMode() {
        isBatterySavingMode = true
        adjustOptimizationSettings()
        
        DevelopmentConfig.log("Battery saving mode enabled", category: "NETWORK")
    }
    
    func disableBatterySavingMode() {
        isBatterySavingMode = false
        adjustOptimizationSettings()
        
        DevelopmentConfig.log("Battery saving mode disabled", category: "NETWORK")
    }
    
    func enableLowMemoryMode() {
        isLowMemoryMode = true
        maxConcurrentRequests = max(1, maxConcurrentRequests / 2)
        
        DevelopmentConfig.log("Network low memory mode enabled", category: "NETWORK")
    }
    
    func disableLowMemoryMode() {
        isLowMemoryMode = false
        adjustOptimizationSettings()
        
        DevelopmentConfig.log("Network low memory mode disabled", category: "NETWORK")
    }
    
    func enableNetworkOptimization() {
        // Enable aggressive optimization for poor network conditions
        if connectionQuality == .poor {
            maxConcurrentRequests = 2
            requestTimeout = 90.0
            retryAttempts = 5
        }
        
        DevelopmentConfig.log("Network optimization enabled", category: "NETWORK")
    }
    
    func reduceConcurrentRequests() {
        maxConcurrentRequests = max(1, maxConcurrentRequests / 2)
        
        DevelopmentConfig.log("Reduced concurrent requests to \(maxConcurrentRequests)", category: "NETWORK")
    }
    
    func throttleBackgroundTasks() {
        // Reduce background network activity
        maxConcurrentRequests = max(1, maxConcurrentRequests / 3)
        requestTimeout *= 2
        
        DevelopmentConfig.log("Throttled background network tasks", category: "NETWORK")
    }
    
    // MARK: - Statistics and Monitoring
    
    private func updateRequestStatistics(request: NetworkRequest, success: Bool, error: Error? = nil) {
        let duration = Date().timeIntervalSince(request.startTime)
        
        networkStatistics.totalRequests += 1
        networkStatistics.totalLatency += duration
        
        if success {
            networkStatistics.successfulRequests += 1
        } else {
            networkStatistics.failedRequests += 1
        }
        
        // Update latency tracking
        networkStatistics.latencyHistory.append(duration)
        if networkStatistics.latencyHistory.count > 100 {
            networkStatistics.latencyHistory.removeFirst()
        }
    }
    
    func getAverageLatency() -> TimeInterval {
        guard networkStatistics.totalRequests > 0 else { return 0.0 }
        return networkStatistics.totalLatency / Double(networkStatistics.totalRequests)
    }
    
    func getStatistics() -> NetworkStatistics {
        networkStatistics.activeRequests = activeRequests.count
        networkStatistics.averageLatency = getAverageLatency()
        return networkStatistics
    }

    func resetStatistics() {
        networkStatistics = NetworkStatistics()
        requestHistory.removeAll()
        requestCounts.removeAll()
    }
    
    func getSuccessRate() -> Double {
        guard networkStatistics.totalRequests > 0 else { return 0.0 }
        return Double(networkStatistics.successfulRequests) / Double(networkStatistics.totalRequests)
    }
    
    // MARK: - Cleanup
    
    private func setupRequestCleanup() {
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            self?.cleanupOldRequests()
        }
    }
    
    private func cleanupOldRequests() {
        let cutoffTime = Date().addingTimeInterval(-3600) // 1 hour ago
        requestHistory.removeAll { $0.startTime < cutoffTime }
        
        // Clean up rate limit tracking
        let now = Date()
        requestCounts = requestCounts.filter { $0.value.resetTime > now }
    }
    
    // MARK: - Debug and Development
    
    func enableDebugLogging() {
        isDebugLoggingEnabled = true
    }
    
    func disableDebugLogging() {
        isDebugLoggingEnabled = false
    }
    
    func printStatistics() {
        guard DevelopmentConfig.isDevelopmentMode else { return }
        
        let stats = getStatistics()
        let successRate = getSuccessRate()
        
        print("""
        🌐 Network Statistics:
        Status: \(networkStatus)
        Quality: \(connectionQuality)
        Success Rate: \(String(format: "%.1f", successRate * 100))%
        Total Requests: \(stats.totalRequests)
        Successful: \(stats.successfulRequests)
        Failed: \(stats.failedRequests)
        Active: \(stats.activeRequests)
        Average Latency: \(String(format: "%.0f", stats.averageLatency * 1000))ms
        """)
    }
    
    deinit {
        monitor.cancel()
    }
}

// MARK: - Supporting Types

enum NetworkStatus: String {
    case wifi = "WiFi"
    case cellular = "Cellular"
    case offline = "Offline"
    case unknown = "Unknown"
}

enum ConnectionQuality {
    case poor
    case fair
    case good
}



enum RequestPriority {
    case low
    case normal
    case high
    case critical
}

enum CachePolicy {
    case `default`
    case noCache
    case cacheOnly
}

enum NetworkError: Error, LocalizedError {
    case invalidResponse
    case rateLimited
    case timeout
    case connectionLost
    case serverError
    case httpError(Int)
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse:
            return "Invalid response received"
        case .rateLimited:
            return "Rate limit exceeded"
        case .timeout:
            return "Request timed out"
        case .connectionLost:
            return "Network connection lost"
        case .serverError:
            return "Server error occurred"
        case .httpError(let code):
            return "HTTP error: \(code)"
        }
    }
}

struct NetworkRequest {
    let id: UUID
    let url: URL
    let method: HTTPMethod
    let priority: RequestPriority
    let startTime: Date
}

struct NetworkStatistics: Codable {
    var totalRequests: Int = 0
    var successfulRequests: Int = 0
    var failedRequests: Int = 0
    var activeRequests: Int = 0
    var totalLatency: TimeInterval = 0.0
    var averageLatency: TimeInterval = 0.0
    var latencyHistory: [TimeInterval] = []
    
    mutating func reset() {
        totalRequests = 0
        successfulRequests = 0
        failedRequests = 0
        activeRequests = 0
        totalLatency = 0.0
        averageLatency = 0.0
        latencyHistory.removeAll()
    }
}
