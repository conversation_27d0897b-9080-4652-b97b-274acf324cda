//
//  GamificationManager.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI
import Combine

@MainActor
class GamificationManager: ObservableObject {
    @Published var currentStreak: Int = 0
    @Published var dailyChallenges: [DailyChallenge] = []
    @Published var recentAchievements: [GamificationAchievement] = []
    @Published var allAchievements: [GamificationAchievement] = []
    @Published var availableThemes: [UnlockableTheme] = []
    @Published var premiumRewards: [PremiumReward] = []
    @Published var leaderboard: [GamificationLeaderboardEntry] = []
    @Published var userRank: Int = 0
    @Published var questsCompleted: Int = 0
    @Published var achievementsUnlocked: Int = 0
    @Published var daysActive: Int = 0
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupMockData()
    }
    
    // MARK: - Data Loading
    func loadUserData() async {
        isLoading = true
        errorMessage = nil
        
        // Load user gamification data
        await loadStreak()
        await loadDailyChallenges()
        await loadAchievements()
        await loadThemes()
        await loadLeaderboard()
        await loadUserStats()

        isLoading = false
    }
    
    private func loadStreak() async {
        // Calculate streak based on daily login data
        currentStreak = calculateCurrentStreak()
    }
    
    private func loadDailyChallenges() async {
        // Generate or load daily challenges
        dailyChallenges = generateDailyChallenges()
    }
    
    private func loadAchievements() async {
        // Load user achievements
        allAchievements = getAllAchievements()
        recentAchievements = Array(allAchievements.filter { $0.isUnlocked }.suffix(5))
        achievementsUnlocked = allAchievements.filter { $0.isUnlocked }.count
    }
    
    private func loadThemes() async {
        // Load unlockable themes
        availableThemes = getUnlockableThemes()
    }
    
    private func loadLeaderboard() async {
        // Load leaderboard data
        leaderboard = generateMockLeaderboard()
        userRank = 42 // Mock user rank
    }
    
    private func loadUserStats() async {
        // Load user statistics
        questsCompleted = 23
        daysActive = 15
    }
    
    // MARK: - Streak System
    private func calculateCurrentStreak() -> Int {
        // Mock implementation - in real app, calculate from login history
        return 7
    }
    
    func updateStreak() async {
        let today = Date()
        let lastLogin = UserDefaults.standard.object(forKey: "lastLoginDate") as? Date
        
        if let lastLogin = lastLogin {
            let calendar = Calendar.current
            let daysBetween = calendar.dateComponents([.day], from: lastLogin, to: today).day ?? 0
            
            if daysBetween == 1 {
                // Consecutive day - increment streak
                currentStreak += 1
            } else if daysBetween > 1 {
                // Streak broken - reset
                currentStreak = 1
            }
            // Same day - no change
        } else {
            // First login
            currentStreak = 1
        }
        
        UserDefaults.standard.set(today, forKey: "lastLoginDate")
        UserDefaults.standard.set(currentStreak, forKey: "currentStreak")
    }
    
    // MARK: - Daily Challenges
    private func generateDailyChallenges() -> [DailyChallenge] {
        return [
            DailyChallenge(
                id: UUID(),
                title: "Complete a Quest",
                description: "Finish any available quest to earn XP",
                xpReward: 25,
                isCompleted: false,
                type: .quest
            ),
            DailyChallenge(
                id: UUID(),
                title: "Check Market News",
                description: "Read 3 articles in the feed",
                xpReward: 15,
                isCompleted: true,
                type: .social
            ),
            DailyChallenge(
                id: UUID(),
                title: "Join Squad Chat",
                description: "Send a message in any squad",
                xpReward: 10,
                isCompleted: false,
                type: .social
            )
        ]
    }
    
    // MARK: - Achievement System
    private func getAllAchievements() -> [GamificationAchievement] {
        return [
            GamificationAchievement(
                id: "first_quest",
                title: "First Steps",
                description: "Complete your first quest",
                icon: "star.fill",
                category: .learning,
                xpReward: 50,
                rarity: .common,
                requirements: AchievementRequirements(type: .questsCompleted, target: 1, description: "Complete 1 quest"),
                isUnlocked: true,
                unlockedAt: Date().addingTimeInterval(-86400 * 2)
            ),
            GamificationAchievement(
                id: "week_streak",
                title: "Dedicated Learner",
                description: "Maintain a 7-day login streak",
                icon: "flame.fill",
                category: .milestones,
                xpReward: 100,
                rarity: .rare,
                requirements: AchievementRequirements(type: .daysActive, target: 7, description: "Stay active for 7 days"),
                isUnlocked: true,
                unlockedAt: Date().addingTimeInterval(-86400)
            ),
            GamificationAchievement(
                id: "social_butterfly",
                title: "Social Butterfly",
                description: "Join 3 different squads",
                icon: "person.3.fill",
                category: .social,
                xpReward: 75,
                rarity: .rare,
                requirements: AchievementRequirements(type: .squadsJoined, target: 3, description: "Join 3 squads"),
                isUnlocked: false,
                unlockedAt: nil
            ),
            GamificationAchievement(
                id: "investment_guru",
                title: "Investment Guru",
                description: "Make 50 successful investments",
                icon: "chart.line.uptrend.xyaxis",
                category: .investing,
                xpReward: 200,
                rarity: .legendary,
                requirements: AchievementRequirements(type: .investmentsMade, target: 50, description: "Make 50 investments"),
                isUnlocked: false,
                unlockedAt: nil
            )
        ]
    }
    
    // MARK: - Theme System
    private func getUnlockableThemes() -> [UnlockableTheme] {
        return [
            UnlockableTheme(
                id: "neon_cyber",
                name: "Neon Cyber",
                description: "Futuristic neon colors with cyberpunk vibes",
                previewColors: [.cyan, .purple, .pink],
                unlockRequirement: "Reach Level 10",
                isUnlocked: true,
                isPremium: false
            ),
            UnlockableTheme(
                id: "pastel_dreams",
                name: "Pastel Dreams",
                description: "Soft pastel colors for a dreamy experience",
                previewColors: [.pink, .blue, .purple],
                unlockRequirement: "Complete 25 Quests",
                isUnlocked: false,
                isPremium: false
            ),
            UnlockableTheme(
                id: "gold_rush",
                name: "Gold Rush",
                description: "Luxurious gold and black theme",
                previewColors: [.yellow, .orange, .black],
                unlockRequirement: "Pro Subscription",
                isUnlocked: false,
                isPremium: true
            ),
            UnlockableTheme(
                id: "ocean_depths",
                name: "Ocean Depths",
                description: "Deep blue ocean-inspired theme",
                previewColors: [.blue, .teal, .cyan],
                unlockRequirement: "30-Day Streak",
                isUnlocked: false,
                isPremium: false
            )
        ]
    }
    
    // MARK: - Premium Rewards
    private func getPremiumRewards() -> [PremiumReward] {
        return [
            PremiumReward(
                id: "double_xp",
                title: "Double XP Weekend",
                description: "Earn 2x XP for all activities this weekend",
                icon: "star.circle.fill",
                cost: 500,
                duration: "48 hours",
                type: .booster
            ),
            PremiumReward(
                id: "exclusive_badge",
                title: "VIP Member Badge",
                description: "Exclusive badge showing your VIP status",
                icon: "crown.fill",
                cost: 1000,
                duration: "Permanent",
                type: .cosmetic
            ),
            PremiumReward(
                id: "priority_support",
                title: "Priority Support",
                description: "Get priority customer support for 30 days",
                icon: "headphones.circle.fill",
                cost: 750,
                duration: "30 days",
                type: .utility
            )
        ]
    }
    
    // MARK: - Leaderboard
    private func generateMockLeaderboard() -> [GamificationLeaderboardEntry] {
        let names = ["Alex Chen", "Sarah Kim", "Mike Johnson", "Emma Davis", "You", "Chris Wilson", "Maya Patel", "David Brown"]
        let levels = [25, 23, 22, 21, 20, 19, 18, 17]
        let xps = [2500, 2300, 2200, 2100, 2000, 1900, 1800, 1700]
        
        return zip(zip(names, levels), xps).enumerated().map { index, data in
            let ((name, level), xp) = data
            return GamificationLeaderboardEntry(
                rank: index + 1,
                username: name,
                level: level,
                xp: xp,
                isCurrentUser: name == "You"
            )
        }
    }
    
    // MARK: - Mock Data Setup
    private func setupMockData() {
        currentStreak = 7
        questsCompleted = 23
        achievementsUnlocked = 2
        daysActive = 15
        userRank = 5
        
        dailyChallenges = generateDailyChallenges()
        allAchievements = getAllAchievements()
        recentAchievements = Array(allAchievements.filter { $0.isUnlocked }.suffix(5))
        availableThemes = getUnlockableThemes()
        premiumRewards = getPremiumRewards()
        leaderboard = generateMockLeaderboard()
    }
}

// MARK: - Supporting Models
struct DailyChallenge: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let xpReward: Int
    let isCompleted: Bool
    let type: ChallengeType
}

enum ChallengeType {
    case quest, social, trading, learning
}

struct UnlockableTheme: Identifiable {
    let id: String
    let name: String
    let description: String
    let previewColors: [Color]
    let unlockRequirement: String
    let isUnlocked: Bool
    let isPremium: Bool
}

struct PremiumReward: Identifiable {
    let id: String
    let title: String
    let description: String
    let icon: String
    let cost: Int
    let duration: String
    let type: RewardType
}

enum RewardType {
    case booster, cosmetic, utility
}

struct GamificationAchievement: Identifiable {
    let id: String
    let title: String
    let description: String
    let icon: String
    let category: AchievementCategory
    let xpReward: Int
    let rarity: AchievementRarity
    let requirements: AchievementRequirements
    let isUnlocked: Bool
    let unlockedAt: Date?
}

struct GamificationLeaderboardEntry: Identifiable {
    let id = UUID()
    let rank: Int
    let username: String
    let level: Int
    let xp: Int
    let isCurrentUser: Bool
}


