//
//  CustomizationManager.swift
//  VibeFinance - Theme & Customization Manager
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import Foundation
import SwiftUI
import Combine

@MainActor
class CustomizationManager: ObservableObject {
    @Published var availableThemes: [UnlockableAppTheme] = []
    @Published var userProfile: UserCustomizationProfile?
    @Published var activeTheme: UnlockableAppTheme?
    @Published var avatarItems: [AvatarCustomization] = []
    @Published var featuredShowcase: ThemeShowcase?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupMockThemes()
        setupMockAvatarItems()
    }
    
    // MARK: - Data Loading
    
    func loadUserCustomizations(for userID: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // In a real app, load from Supabase
            await loadMockUserProfile(for: userID)
            
            isLoading = false
        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
        }
    }
    
    private func loadMockUserProfile(for userID: UUID) async {
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        let unlockedThemeIDs = ["dark_default", "neon_cyber", "nature_forest", "retro_synthwave"]
        
        // Update themes with unlock status
        availableThemes = availableThemes.map { theme in
            UnlockableAppTheme(
                id: theme.id,
                name: theme.name,
                description: theme.description,
                category: theme.category,
                rarity: theme.rarity,
                unlockRequirements: theme.unlockRequirements,
                colors: theme.colors,
                effects: theme.effects,
                isUnlocked: unlockedThemeIDs.contains(theme.id),
                unlockedAt: unlockedThemeIDs.contains(theme.id) ? Date().addingTimeInterval(-Double.random(in: 86400...604800)) : nil,
                isPremium: theme.isPremium,
                previewImage: theme.previewImage
            )
        }
        
        // Set active theme
        activeTheme = availableThemes.first { $0.id == "neon_cyber" }
        
        // Create user profile
        userProfile = UserCustomizationProfile(
            userID: userID,
            activeTheme: "neon_cyber",
            unlockedThemes: unlockedThemeIDs,
            favoriteThemes: ["neon_cyber", "nature_forest"],
            avatarCustomizations: [
                "background": "gradient_purple",
                "frame": "neon_border",
                "badge": "verified_check"
            ],
            unlockedAvatarItems: ["gradient_purple", "neon_border", "verified_check", "sparkle_effect"],
            customizationLevel: 5,
            totalCustomizationsUnlocked: 12
        )
        
        // Set featured showcase
        featuredShowcase = ThemeShowcase(
            title: "🌟 Legendary Collection",
            description: "Exclusive themes for the most dedicated users",
            themes: availableThemes.filter { $0.rarity == .legendary },
            isLimited: true,
            expiresAt: Date().addingTimeInterval(604800),
            category: nil
        )
    }
    
    // MARK: - Theme Operations
    
    func unlockTheme(_ themeID: String, for userID: UUID) async -> Bool {
        guard let themeIndex = availableThemes.firstIndex(where: { $0.id == themeID }),
              !availableThemes[themeIndex].isUnlocked else { return false }
        
        let theme = availableThemes[themeIndex]
        
        // Check if requirements are met
        if await checkThemeRequirements(theme, for: userID) {
            // Unlock the theme
            let unlockedTheme = UnlockableAppTheme(
                id: theme.id,
                name: theme.name,
                description: theme.description,
                category: theme.category,
                rarity: theme.rarity,
                unlockRequirements: theme.unlockRequirements,
                colors: theme.colors,
                effects: theme.effects,
                isUnlocked: true,
                unlockedAt: Date(),
                isPremium: theme.isPremium,
                previewImage: theme.previewImage
            )
            
            availableThemes[themeIndex] = unlockedTheme
            
            // Update user profile
            if var profile = userProfile {
                var updatedUnlockedThemes = profile.unlockedThemes
                updatedUnlockedThemes.append(themeID)
                
                userProfile = UserCustomizationProfile(
                    userID: profile.userID,
                    activeTheme: profile.activeTheme,
                    unlockedThemes: updatedUnlockedThemes,
                    favoriteThemes: profile.favoriteThemes,
                    avatarCustomizations: profile.avatarCustomizations,
                    unlockedAvatarItems: profile.unlockedAvatarItems,
                    customizationLevel: profile.customizationLevel,
                    totalCustomizationsUnlocked: profile.totalCustomizationsUnlocked + 1
                )
            }
            
            // Show unlock celebration
            await showThemeUnlockCelebration(unlockedTheme)
            
            return true
        }
        
        return false
    }
    
    func activateTheme(_ themeID: String) async {
        guard let theme = availableThemes.first(where: { $0.id == themeID && $0.isUnlocked }) else { return }
        
        activeTheme = theme
        
        // Update user profile
        if var profile = userProfile {
            userProfile = UserCustomizationProfile(
                userID: profile.userID,
                activeTheme: themeID,
                unlockedThemes: profile.unlockedThemes,
                favoriteThemes: profile.favoriteThemes,
                avatarCustomizations: profile.avatarCustomizations,
                unlockedAvatarItems: profile.unlockedAvatarItems,
                customizationLevel: profile.customizationLevel,
                totalCustomizationsUnlocked: profile.totalCustomizationsUnlocked
            )
        }
        
        // Apply theme to app
        await applyThemeToApp(theme)
    }
    
    func toggleFavoriteTheme(_ themeID: String) async {
        guard var profile = userProfile else { return }
        
        var favoriteThemes = profile.favoriteThemes
        if favoriteThemes.contains(themeID) {
            favoriteThemes.removeAll { $0 == themeID }
        } else {
            favoriteThemes.append(themeID)
        }
        
        userProfile = UserCustomizationProfile(
            userID: profile.userID,
            activeTheme: profile.activeTheme,
            unlockedThemes: profile.unlockedThemes,
            favoriteThemes: favoriteThemes,
            avatarCustomizations: profile.avatarCustomizations,
            unlockedAvatarItems: profile.unlockedAvatarItems,
            customizationLevel: profile.customizationLevel,
            totalCustomizationsUnlocked: profile.totalCustomizationsUnlocked
        )
    }
    
    // MARK: - Avatar Customization
    
    func updateAvatarCustomization(category: AvatarCategory, itemID: String) async {
        guard var profile = userProfile,
              profile.unlockedAvatarItems.contains(itemID) else { return }
        
        var avatarCustomizations = profile.avatarCustomizations
        avatarCustomizations[category.rawValue] = itemID
        
        userProfile = UserCustomizationProfile(
            userID: profile.userID,
            activeTheme: profile.activeTheme,
            unlockedThemes: profile.unlockedThemes,
            favoriteThemes: profile.favoriteThemes,
            avatarCustomizations: avatarCustomizations,
            unlockedAvatarItems: profile.unlockedAvatarItems,
            customizationLevel: profile.customizationLevel,
            totalCustomizationsUnlocked: profile.totalCustomizationsUnlocked
        )
    }
    
    // MARK: - Helper Methods
    
    private func checkThemeRequirements(_ theme: UnlockableAppTheme, for userID: UUID) async -> Bool {
        // In a real app, check against user's actual stats
        // For now, simulate requirement checking
        return !theme.isPremium || theme.unlockRequirements.type != .premiumPurchase
    }
    
    private func showThemeUnlockCelebration(_ theme: UnlockableAppTheme) async {
        // Trigger celebration animation and notification
        print("🎨 Theme Unlocked: \(theme.name)")
    }
    
    private func applyThemeToApp(_ theme: UnlockableAppTheme) async {
        // Apply theme colors and effects to the app
        // This would integrate with the theme system
        print("🎨 Applied theme: \(theme.name)")
    }
    
    // MARK: - Filtering and Sorting
    
    func getThemes(by category: ThemeCategory) -> [UnlockableAppTheme] {
        return availableThemes.filter { $0.category == category }
    }
    
    func getThemes(by rarity: ThemeRarity) -> [UnlockableAppTheme] {
        return availableThemes.filter { $0.rarity == rarity }
    }
    
    func getUnlockedThemes() -> [UnlockableAppTheme] {
        return availableThemes.filter { $0.isUnlocked }
    }
    
    func getFavoriteThemes() -> [UnlockableAppTheme] {
        guard let profile = userProfile else { return [] }
        return availableThemes.filter { profile.favoriteThemes.contains($0.id) }
    }

    // MARK: - Mock Data Setup

    private func setupMockThemes() {
        availableThemes = [
            // Basic Themes
            UnlockableAppTheme(
                id: "dark_default",
                name: "Dark Vibes 🌙",
                description: "The classic dark theme for night owls",
                category: .classic,
                rarity: .basic,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .questsCompleted,
                    target: 0,
                    description: "Default theme",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                colors: UnlockableThemeColors(
                    primary: "#6366F1",
                    secondary: "#8B5CF6",
                    accent: "#F59E0B",
                    background: "#0F172A",
                    surface: "#1E293B",
                    onPrimary: "#FFFFFF",
                    onSecondary: "#FFFFFF",
                    onBackground: "#F8FAFC",
                    onSurface: "#F8FAFC",
                    success: "#10B981",
                    warning: "#F59E0B",
                    error: "#EF4444"
                ),
                effects: nil,
                isUnlocked: false,
                unlockedAt: nil,
                isPremium: false,
                previewImage: nil
            ),

            // Neon Themes
            UnlockableAppTheme(
                id: "neon_cyber",
                name: "Cyber Neon ⚡",
                description: "Electric vibes with neon accents",
                category: .neon,
                rarity: .premium,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .badgeUnlocked,
                    target: 1,
                    description: "Unlock any rare badge",
                    badgeRequired: "week_streak",
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                colors: UnlockableThemeColors(
                    primary: "#00FFFF",
                    secondary: "#FF00FF",
                    accent: "#FFFF00",
                    background: "#000011",
                    surface: "#001122",
                    onPrimary: "#000000",
                    onSecondary: "#000000",
                    onBackground: "#00FFFF",
                    onSurface: "#00FFFF",
                    success: "#00FF00",
                    warning: "#FFFF00",
                    error: "#FF0080"
                ),
                effects: ThemeEffects(
                    hasGradients: true,
                    hasAnimations: true,
                    hasParticles: false,
                    hasGlowEffects: true,
                    hasBlurEffects: false,
                    customAnimations: ["neon_pulse", "electric_spark"],
                    particleType: nil
                ),
                isUnlocked: false,
                unlockedAt: nil,
                isPremium: false,
                previewImage: nil
            ),

            // Nature Themes
            UnlockableAppTheme(
                id: "nature_forest",
                name: "Forest Zen 🌿",
                description: "Calming greens inspired by nature",
                category: .nature,
                rarity: .exclusive,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .streakDays,
                    target: 30,
                    description: "Maintain 30-day streak",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                colors: UnlockableThemeColors(
                    primary: "#059669",
                    secondary: "#065F46",
                    accent: "#F59E0B",
                    background: "#064E3B",
                    surface: "#065F46",
                    onPrimary: "#FFFFFF",
                    onSecondary: "#FFFFFF",
                    onBackground: "#ECFDF5",
                    onSurface: "#ECFDF5",
                    success: "#10B981",
                    warning: "#F59E0B",
                    error: "#EF4444"
                ),
                effects: ThemeEffects(
                    hasGradients: true,
                    hasAnimations: false,
                    hasParticles: true,
                    hasGlowEffects: false,
                    hasBlurEffects: true,
                    customAnimations: nil,
                    particleType: .leaves
                ),
                isUnlocked: false,
                unlockedAt: nil,
                isPremium: false,
                previewImage: nil
            ),

            // Space Themes
            UnlockableAppTheme(
                id: "space_galaxy",
                name: "Galaxy Explorer 🚀",
                description: "Journey through the cosmos",
                category: .space,
                rarity: .legendary,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .achievementUnlocked,
                    target: 1,
                    description: "Unlock legendary achievement",
                    badgeRequired: nil,
                    achievementRequired: "centurion",
                    levelRequired: nil
                ),
                colors: UnlockableThemeColors(
                    primary: "#7C3AED",
                    secondary: "#1E1B4B",
                    accent: "#F59E0B",
                    background: "#0C0A1E",
                    surface: "#1E1B4B",
                    onPrimary: "#FFFFFF",
                    onSecondary: "#FFFFFF",
                    onBackground: "#E0E7FF",
                    onSurface: "#E0E7FF",
                    success: "#10B981",
                    warning: "#F59E0B",
                    error: "#EF4444"
                ),
                effects: ThemeEffects(
                    hasGradients: true,
                    hasAnimations: true,
                    hasParticles: true,
                    hasGlowEffects: true,
                    hasBlurEffects: true,
                    customAnimations: ["star_twinkle", "galaxy_rotation"],
                    particleType: .stars
                ),
                isUnlocked: false,
                unlockedAt: nil,
                isPremium: false,
                previewImage: nil
            ),

            // Retro Themes
            UnlockableAppTheme(
                id: "retro_synthwave",
                name: "Synthwave Dreams 📼",
                description: "80s retro vibes with synthwave aesthetics",
                category: .retro,
                rarity: .exclusive,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .levelReached,
                    target: 1,
                    description: "Reach level 25",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: 25
                ),
                colors: UnlockableThemeColors(
                    primary: "#FF0080",
                    secondary: "#8000FF",
                    accent: "#00FFFF",
                    background: "#1A0033",
                    surface: "#330066",
                    onPrimary: "#FFFFFF",
                    onSecondary: "#FFFFFF",
                    onBackground: "#FF80C0",
                    onSurface: "#FF80C0",
                    success: "#00FF80",
                    warning: "#FFFF00",
                    error: "#FF4080"
                ),
                effects: ThemeEffects(
                    hasGradients: true,
                    hasAnimations: true,
                    hasParticles: false,
                    hasGlowEffects: true,
                    hasBlurEffects: false,
                    customAnimations: ["retro_scan", "synthwave_pulse"],
                    particleType: nil
                ),
                isUnlocked: false,
                unlockedAt: nil,
                isPremium: false,
                previewImage: nil
            ),

            // Mythic Theme
            UnlockableAppTheme(
                id: "mythic_rainbow",
                name: "Rainbow Mythic 🌈",
                description: "The ultimate theme for true legends",
                category: .gaming,
                rarity: .mythic,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .premiumPurchase,
                    target: 1,
                    description: "Premium exclusive",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                colors: UnlockableThemeColors(
                    primary: "#FF6B6B",
                    secondary: "#4ECDC4",
                    accent: "#FFE66D",
                    background: "#2C2C54",
                    surface: "#40407A",
                    onPrimary: "#FFFFFF",
                    onSecondary: "#FFFFFF",
                    onBackground: "#FFFFFF",
                    onSurface: "#FFFFFF",
                    success: "#00D2D3",
                    warning: "#FFE66D",
                    error: "#FF5252"
                ),
                effects: ThemeEffects(
                    hasGradients: true,
                    hasAnimations: true,
                    hasParticles: true,
                    hasGlowEffects: true,
                    hasBlurEffects: true,
                    customAnimations: ["rainbow_cycle", "mythic_aura", "legendary_sparkle"],
                    particleType: .sparkles
                ),
                isUnlocked: false,
                unlockedAt: nil,
                isPremium: true,
                previewImage: nil
            )
        ]
    }

    private func setupMockAvatarItems() {
        avatarItems = [
            // Background Items
            AvatarCustomization(
                id: "gradient_purple",
                name: "Purple Gradient",
                category: .background,
                rarity: .basic,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .questsCompleted,
                    target: 5,
                    description: "Complete 5 quests",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                assetName: "bg_gradient_purple",
                isUnlocked: false,
                isPremium: false
            ),
            AvatarCustomization(
                id: "neon_grid",
                name: "Neon Grid",
                category: .background,
                rarity: .premium,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .badgeUnlocked,
                    target: 1,
                    description: "Unlock neon theme",
                    badgeRequired: "neon_cyber",
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                assetName: "bg_neon_grid",
                isUnlocked: false,
                isPremium: false
            ),

            // Frame Items
            AvatarCustomization(
                id: "neon_border",
                name: "Neon Border",
                category: .frame,
                rarity: .premium,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .streakDays,
                    target: 7,
                    description: "7-day streak",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                assetName: "frame_neon_border",
                isUnlocked: false,
                isPremium: false
            ),
            AvatarCustomization(
                id: "legendary_crown",
                name: "Legendary Crown",
                category: .frame,
                rarity: .legendary,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .achievementUnlocked,
                    target: 1,
                    description: "Unlock legendary achievement",
                    badgeRequired: nil,
                    achievementRequired: "centurion",
                    levelRequired: nil
                ),
                assetName: "frame_legendary_crown",
                isUnlocked: false,
                isPremium: false
            ),

            // Badge Items
            AvatarCustomization(
                id: "verified_check",
                name: "Verified Check",
                category: .badge,
                rarity: .exclusive,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .socialMilestone,
                    target: 100,
                    description: "100 social interactions",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                assetName: "badge_verified_check",
                isUnlocked: false,
                isPremium: false
            ),

            // Effect Items
            AvatarCustomization(
                id: "sparkle_effect",
                name: "Sparkle Effect",
                category: .effect,
                rarity: .premium,
                unlockRequirements: ThemeUnlockRequirements(
                    type: .badgeUnlocked,
                    target: 3,
                    description: "Unlock 3 rare badges",
                    badgeRequired: nil,
                    achievementRequired: nil,
                    levelRequired: nil
                ),
                assetName: "effect_sparkle",
                isUnlocked: false,
                isPremium: false
            )
        ]
    }
}
