//
//  ChatManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine
import AVFoundation

@MainActor
class ChatManager: ObservableObject {
    @Published var chatSessions: [ChatSession] = []
    @Published var currentSession: ChatSession?
    @Published var isLoading = false
    @Published var isTyping = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private let aiService = GeminiAIService()
    private let speechSynthesizer = AVSpeechSynthesizer()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Session Management
    
    func loadChatSessions(for userID: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let sessions = try await supabaseService.fetch(
                ChatSession.self,
                from: "chat_sessions",
                where: "userID=eq.\(userID.uuidString)&order=updatedAt.desc"
            )
            
            await MainActor.run {
                self.chatSessions = sessions
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func createNewSession(for userID: UUID) async {
        let session = ChatSession(userID: userID)
        
        do {
            try await supabaseService.create(session, table: "chat_sessions")
            
            await MainActor.run {
                self.chatSessions.insert(session, at: 0)
                self.currentSession = session
            }
            
            // Send welcome message
            await sendWelcomeMessage(to: session)
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func selectSession(_ session: ChatSession) {
        currentSession = session
        markSessionAsRead(session)
    }
    
    func deleteSession(_ session: ChatSession) async {
        do {
            try await supabaseService.delete(
                from: "chat_sessions",
                where: "id=eq.\(session.id.uuidString)"
            )
            
            await MainActor.run {
                self.chatSessions.removeAll { $0.id == session.id }
                if self.currentSession?.id == session.id {
                    self.currentSession = nil
                }
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    // MARK: - Message Handling
    
    func sendMessage(_ content: String, type: MessageType = .text, context: ChatContext) async {
        guard let session = currentSession else { return }
        
        let userMessage = ChatMessage(
            sessionID: session.id,
            content: content,
            type: type,
            sender: .user
        )
        
        await addMessageToSession(userMessage, session: session)
        
        // Generate AI response
        await generateAIResponse(for: userMessage, context: context)
    }
    
    func sendVoiceMessage(_ audioURL: URL, duration: Double, context: ChatContext) async {
        guard let session = currentSession else { return }
        
        // Convert speech to text (placeholder - would use speech recognition)
        let transcribedText = "Voice message transcription"
        
        let metadata = MessageMetadata(voiceURL: audioURL.absoluteString, voiceDuration: duration)
        let voiceMessage = ChatMessage(
            sessionID: session.id,
            content: transcribedText,
            type: .voice,
            sender: .user,
            metadata: metadata
        )
        
        await addMessageToSession(voiceMessage, session: session)
        await generateAIResponse(for: voiceMessage, context: context)
    }
    
    private func generateAIResponse(for userMessage: ChatMessage, context: ChatContext) async {
        isTyping = true

        do {
            let response = try await aiService.generateChatResponse(
                message: userMessage.content,
                context: [
                    "User Level: \(context.currentLevel)",
                    "Subscription: \(context.subscriptionTier.displayName)",
                    "Risk Tolerance: \(context.userPreferences.riskTolerance.displayName)"
                ],
                personality: .financeBuddy
            )
            
            let aiMessage = ChatMessage(
                sessionID: userMessage.sessionID,
                content: response,
                type: .text,
                sender: .assistant,
                metadata: nil
            )
            
            await addMessageToSession(aiMessage, session: currentSession!)
            
            // Speak response if it's a voice conversation
            if userMessage.type == .voice {
                speakMessage(response)
            }
            
        } catch {
            let errorMessage = ChatMessage(
                sessionID: userMessage.sessionID,
                content: "Sorry, I'm having trouble right now. Can you try again? 😅",
                type: .text,
                sender: .assistant
            )
            
            await addMessageToSession(errorMessage, session: currentSession!)
        }
        
        isTyping = false
    }
    
    private func addMessageToSession(_ message: ChatMessage, session: ChatSession) async {
        var updatedSession = session
        updatedSession.addMessage(message)
        
        do {
            try await supabaseService.update(
                updatedSession,
                in: "chat_sessions",
                where: "id=eq.\(session.id.uuidString)"
            )
            
            await MainActor.run {
                if let index = self.chatSessions.firstIndex(where: { $0.id == session.id }) {
                    self.chatSessions[index] = updatedSession
                }
                if self.currentSession?.id == session.id {
                    self.currentSession = updatedSession
                }
            }
            
        } catch {
            print("Failed to save message: \(error)")
        }
    }
    
    private func sendWelcomeMessage(to session: ChatSession) async {
        let welcomeMessage = ChatMessage(
            sessionID: session.id,
            content: "Hey there! 👋 I'm your Vibe Buddy, here to help you navigate the world of finance in a fun and easy way! What's on your mind today?",
            type: .text,
            sender: .assistant,
            metadata: MessageMetadata(
                quickReplies: [
                    QuickReply(text: "Explain investing basics", action: .explainConcept),
                    QuickReply(text: "Show me trending stocks", action: .showInvestment),
                    QuickReply(text: "Help me budget", action: .getAdvice),
                    QuickReply(text: "Start a quest", action: .openQuest)
                ]
            )
        )
        
        await addMessageToSession(welcomeMessage, session: session)
    }
    
    // MARK: - Voice Features
    
    private func speakMessage(_ text: String) {
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.1
        
        speechSynthesizer.speak(utterance)
    }
    
    func stopSpeaking() {
        speechSynthesizer.stopSpeaking(at: .immediate)
    }
    
    // MARK: - Message Interactions
    
    func addReaction(_ emoji: String, to message: ChatMessage, userID: UUID) async {
        let reaction = MessageReaction(userID: userID, emoji: emoji)
        var updatedMessage = message
        updatedMessage.addReaction(reaction)
        
        // Update in session
        guard let session = currentSession,
              let messageIndex = session.messages.firstIndex(where: { $0.id == message.id }) else { return }
        
        var updatedSession = session
        updatedSession.messages[messageIndex] = updatedMessage
        
        do {
            try await supabaseService.update(
                updatedSession,
                in: "chat_sessions",
                where: "id=eq.\(session.id.uuidString)"
            )
            
            await MainActor.run {
                self.currentSession = updatedSession
                if let sessionIndex = self.chatSessions.firstIndex(where: { $0.id == session.id }) {
                    self.chatSessions[sessionIndex] = updatedSession
                }
            }
            
        } catch {
            print("Failed to add reaction: \(error)")
        }
    }
    
    func handleQuickReply(_ quickReply: QuickReply, context: ChatContext) async {
        switch quickReply.action {
        case .sendMessage:
            await sendMessage(quickReply.text, context: context)
        case .openQuest:
            // This would trigger navigation to quests view
            await sendMessage("Let me show you some awesome quests to level up your finance skills! 🎯", context: context)
        case .openSimulator:
            await sendMessage("Ready to practice investing with virtual money? Let's check out the simulator! 🎮", context: context)
        case .showInvestment:
            await sendMessage("Here are some hot investment opportunities based on your interests! 📈", context: context)
        case .explainConcept:
            await sendMessage("I'd love to explain that! What specific finance concept would you like to learn about?", context: context)
        case .getAdvice:
            await sendMessage("I'm here to help! What financial advice are you looking for today?", context: context)
        default:
            await sendMessage(quickReply.text, context: context)
        }
    }
    
    // MARK: - Helper Methods
    
    private func getConversationHistory() -> [ChatMessage] {
        return currentSession?.messages.suffix(10).map { $0 } ?? []
    }
    
    private func markSessionAsRead(_ session: ChatSession) {
        Task {
            var updatedSession = session
            updatedSession.markAllAsRead()
            
            do {
                try await supabaseService.update(
                    updatedSession,
                    in: "chat_sessions",
                    where: "id=eq.\(session.id.uuidString)"
                )
                
                await MainActor.run {
                    if let index = self.chatSessions.firstIndex(where: { $0.id == session.id }) {
                        self.chatSessions[index] = updatedSession
                    }
                }
                
            } catch {
                print("Failed to mark session as read: \(error)")
            }
        }
    }
    
    func getTotalUnreadCount() -> Int {
        return chatSessions.reduce(0) { $0 + $1.unreadCount }
    }
    
    func clearError() {
        errorMessage = nil
    }
    
    func refreshSessions(for userID: UUID) async {
        chatSessions.removeAll()
        currentSession = nil
        await loadChatSessions(for: userID)
    }
}

// MARK: - AI Response Model (using DataModels.AIResponse)
