//
//  SocialManager.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI
import Combine

@MainActor
class SocialManager: ObservableObject {
    @Published var onlineUsers: Int = 0
    @Published var activeSquads: Int = 0
    @Published var unreadNotifications: Int = 0
    @Published var followersCount: Int = 0
    @Published var socialRank: Int = 0
    @Published var socialStreak: Int = 0
    
    @Published var trendingSquads: [Squad] = []
    @Published var recommendedSquads: [Squad] = []
    @Published var featuredSquads: [Squad] = []
    @Published var squadCategories: [SquadCategory] = []
    
    @Published var recentActivities: [SocialActivity] = []
    @Published var socialAchievements: [SocialAchievement] = []
    @Published var squadLeaderboards: [SquadLeaderboard] = []
    @Published var socialRankings: [SocialRanking] = []
    @Published var socialChallenges: [SocialChallenge] = []
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupMockData()
        startRealTimeUpdates()
    }
    
    // MARK: - Data Loading
    func loadSocialData() async {
        isLoading = true
        errorMessage = nil
        
        await loadUserStats()
        await loadTrendingSquads()
        await loadRecommendations()
        await loadActivities()
        await loadLeaderboards()
        await loadSocialChallenges()

        isLoading = false
    }
    
    private func loadUserStats() async {
        // Load user social statistics
        followersCount = 127
        socialRank = 23
        socialStreak = 12
        unreadNotifications = 3
    }
    
    private func loadTrendingSquads() async {
        // Load trending squads based on activity and performance
        trendingSquads = generateTrendingSquads()
    }
    
    private func loadRecommendations() async {
        // Load AI-powered squad recommendations
        recommendedSquads = generateRecommendedSquads()
    }
    
    private func loadActivities() async {
        // Load recent social activities
        recentActivities = generateRecentActivities()
    }
    
    private func loadLeaderboards() async {
        // Load squad and social leaderboards
        squadLeaderboards = generateSquadLeaderboards()
        socialRankings = generateSocialRankings()
    }
    
    private func loadSocialChallenges() async {
        // Load active social challenges
        socialChallenges = generateSocialChallenges()
    }
    
    func refreshRecommendations() async {
        await loadRecommendations()
    }
    
    // MARK: - Real-Time Updates
    private func startRealTimeUpdates() {
        // Simulate real-time updates
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { _ in
                Task {
                    await self.updateLiveStats()
                }
            }
            .store(in: &cancellables)
    }
    
    private func updateLiveStats() async {
        // Update live statistics
        onlineUsers = Int.random(in: 150...300)
        activeSquads = Int.random(in: 8...15)
        
        // Occasionally update notifications
        if Bool.random() {
            unreadNotifications += Int.random(in: 0...2)
        }
    }
    
    // MARK: - Mock Data Generation
    private func setupMockData() {
        onlineUsers = 234
        activeSquads = 12
        unreadNotifications = 3
        followersCount = 127
        socialRank = 23
        socialStreak = 12
        
        trendingSquads = generateTrendingSquads()
        recommendedSquads = generateRecommendedSquads()
        featuredSquads = generateFeaturedSquads()
        squadCategories = generateSquadCategories()
        recentActivities = generateRecentActivities()
        socialAchievements = generateSocialAchievements()
        squadLeaderboards = generateSquadLeaderboards()
        socialRankings = generateSocialRankings()
        socialChallenges = generateSocialChallenges()
    }
    
    private func generateTrendingSquads() -> [Squad] {
        return [
            Squad(name: "🚀 Tech Titans", description: "Investing in the future of technology", emoji: "🚀", creatorID: UUID()),
            Squad(name: "🌱 Green Energy", description: "Sustainable investing for a better tomorrow", emoji: "🌱", creatorID: UUID()),
            Squad(name: "💎 Diamond Hands", description: "Long-term value investing", emoji: "💎", creatorID: UUID()),
            Squad(name: "⚡ Quick Gains", description: "Fast-paced trading strategies", emoji: "⚡", creatorID: UUID())
        ]
    }
    
    private func generateRecommendedSquads() -> [Squad] {
        return [
            Squad(name: "📱 Mobile Revolution", description: "Investing in mobile technology", emoji: "📱", creatorID: UUID()),
            Squad(name: "🏥 Healthcare Heroes", description: "Medical and biotech investments", emoji: "🏥", creatorID: UUID()),
            Squad(name: "🎮 Gaming Galaxy", description: "Gaming and entertainment stocks", emoji: "🎮", creatorID: UUID()),
            Squad(name: "🏠 Real Estate Rebels", description: "Property and REIT investments", emoji: "🏠", creatorID: UUID()),
            Squad(name: "🔋 Battery Builders", description: "Energy storage and EV batteries", emoji: "🔋", creatorID: UUID())
        ]
    }
    
    private func generateFeaturedSquads() -> [Squad] {
        return [
            Squad(name: "🌟 Featured Elite", description: "Top performing squad this month", emoji: "🌟", creatorID: UUID()),
            Squad(name: "🎯 Precision Picks", description: "Carefully selected investments", emoji: "🎯", creatorID: UUID())
        ]
    }
    
    private func generateSquadCategories() -> [SquadCategory] {
        return [
            SquadCategory(id: UUID(), name: "Technology", icon: "laptopcomputer", color: .blue, squadCount: 45),
            SquadCategory(id: UUID(), name: "Healthcare", icon: "cross.fill", color: .red, squadCount: 32),
            SquadCategory(id: UUID(), name: "Finance", icon: "dollarsign.circle", color: .green, squadCount: 28),
            SquadCategory(id: UUID(), name: "Energy", icon: "bolt.fill", color: .yellow, squadCount: 23),
            SquadCategory(id: UUID(), name: "Consumer", icon: "cart.fill", color: .orange, squadCount: 19)
        ]
    }
    
    private func generateRecentActivities() -> [SocialActivity] {
        return [
            SocialActivity(
                id: UUID(),
                type: .squadJoin,
                user: "Alex Chen",
                action: "joined",
                target: "Tech Titans squad",
                timestamp: Date().addingTimeInterval(-300),
                icon: "person.badge.plus"
            ),
            SocialActivity(
                id: UUID(),
                type: .investment,
                user: "Sarah Kim",
                action: "invested $500 in",
                target: "AAPL",
                timestamp: Date().addingTimeInterval(-600),
                icon: "chart.line.uptrend.xyaxis"
            ),
            SocialActivity(
                id: UUID(),
                type: .achievement,
                user: "Mike Johnson",
                action: "earned",
                target: "Diamond Hands achievement",
                timestamp: Date().addingTimeInterval(-900),
                icon: "trophy.fill"
            ),
            SocialActivity(
                id: UUID(),
                type: .squadCreate,
                user: "Emma Davis",
                action: "created",
                target: "Crypto Crusaders squad",
                timestamp: Date().addingTimeInterval(-1200),
                icon: "plus.circle.fill"
            )
        ]
    }
    
    private func generateSocialAchievements() -> [SocialAchievement] {
        return [
            SocialAchievement(
                id: UUID(),
                title: "Squad Leader",
                description: "Created your first squad",
                icon: "crown.fill",
                color: .orange,
                isUnlocked: true,
                unlockedAt: Date().addingTimeInterval(-86400)
            ),
            SocialAchievement(
                id: UUID(),
                title: "Social Butterfly",
                description: "Join 5 different squads",
                icon: "person.3.fill",
                color: .purple,
                isUnlocked: true,
                unlockedAt: Date().addingTimeInterval(-172800)
            ),
            SocialAchievement(
                id: UUID(),
                title: "Influencer",
                description: "Get 100 followers",
                icon: "heart.fill",
                color: .pink,
                isUnlocked: false,
                unlockedAt: nil
            )
        ]
    }
    
    private func generateSquadLeaderboards() -> [SquadLeaderboard] {
        return [
            SquadLeaderboard(
                id: UUID(),
                title: "Top Performing Squads",
                period: "This Week",
                squads: [
                    SquadRanking(rank: 1, squad: "Tech Titans", performance: "+12.5%", members: 18),
                    SquadRanking(rank: 2, squad: "Green Energy", performance: "+9.8%", members: 15),
                    SquadRanking(rank: 3, squad: "Diamond Hands", performance: "+7.2%", members: 22)
                ]
            )
        ]
    }
    
    private func generateSocialRankings() -> [SocialRanking] {
        return [
            SocialRanking(
                id: UUID(),
                rank: 1,
                username: "InvestorPro",
                score: 2450,
                badge: "🏆",
                change: "+5"
            ),
            SocialRanking(
                id: UUID(),
                rank: 2,
                username: "MarketMaster",
                score: 2380,
                badge: "🥈",
                change: "-1"
            ),
            SocialRanking(
                id: UUID(),
                rank: 3,
                username: "StockSage",
                score: 2320,
                badge: "🥉",
                change: "+2"
            )
        ]
    }
    
    private func generateSocialChallenges() -> [SocialChallenge] {
        return [
            SocialChallenge(
                id: UUID(),
                title: "Squad Recruitment",
                description: "Invite 3 friends to join your squad",
                reward: "50 XP + Exclusive Badge",
                progress: 1,
                target: 3,
                deadline: Date().addingTimeInterval(604800), // 1 week
                participants: 234
            ),
            SocialChallenge(
                id: UUID(),
                title: "Social Investor",
                description: "Make 5 collaborative investments",
                reward: "100 XP + Theme Unlock",
                progress: 2,
                target: 5,
                deadline: Date().addingTimeInterval(1209600), // 2 weeks
                participants: 156
            )
        ]
    }
}

// MARK: - Supporting Models
struct SquadCategory: Identifiable {
    let id: UUID
    let name: String
    let icon: String
    let color: Color
    let squadCount: Int
}

struct SocialActivity: Identifiable {
    let id: UUID
    let type: ActivityType
    let user: String
    let action: String
    let target: String
    let timestamp: Date
    let icon: String
}

enum ActivityType {
    case squadJoin, squadCreate, investment, achievement, trade, follow
}

struct SocialAchievement: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let icon: String
    let color: Color
    let isUnlocked: Bool
    let unlockedAt: Date?
}

struct SquadLeaderboard: Identifiable {
    let id: UUID
    let title: String
    let period: String
    let squads: [SquadRanking]
}

struct SquadRanking: Identifiable {
    let id = UUID()
    let rank: Int
    let squad: String
    let performance: String
    let members: Int
}

struct SocialRanking: Identifiable {
    let id: UUID
    let rank: Int
    let username: String
    let score: Int
    let badge: String
    let change: String
}

struct SocialChallenge: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let reward: String
    let progress: Int
    let target: Int
    let deadline: Date
    let participants: Int
    
    var progressPercentage: Double {
        Double(progress) / Double(target)
    }
    
    var isCompleted: Bool {
        progress >= target
    }
}
