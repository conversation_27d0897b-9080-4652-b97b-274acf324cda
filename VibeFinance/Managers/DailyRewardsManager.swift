//
//  DailyRewardsManager.swift
//  VibeFinance - Daily Rewards Management
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI
import Foundation

@MainActor
class DailyRewardsManager: ObservableObject {
    @Published var rewards: [DailyReward] = []
    @Published var userStats: UserRewardStats = UserRewardStats(
        currentStreak: 7,
        longestStreak: 15,
        totalRewards: 1250,
        freezeTokens: 3,
        level: 12,
        xp: 2400,
        nextLevelXp: 3000
    )
    @Published var lastCheckIn: Date?
    
    var currentDay: Int {
        userStats.currentStreak + 1
    }
    
    var todaysReward: DailyReward? {
        rewards.first { $0.day == currentDay }
    }
    
    init() {
        loadRewards()
        loadUserProgress()
    }
    
    private func loadRewards() {
        rewards = [
            DailyReward(
                id: "1",
                day: 1,
                type: .coins,
                amount: 50,
                title: "50 Coins",
                description: "Start your journey",
                rarity: .common,
                icon: "dollarsign.circle.fill",
                claimed: true,
                locked: false
            ),
            DailyReward(
                id: "2",
                day: 2,
                type: .coins,
                amount: 75,
                title: "75 Coins",
                description: "Building momentum",
                rarity: .common,
                icon: "dollarsign.circle.fill",
                claimed: true,
                locked: false
            ),
            DailyReward(
                id: "3",
                day: 3,
                type: .boost,
                amount: 1,
                title: "XP Boost",
                description: "2x XP for 24h",
                rarity: .rare,
                icon: "bolt.fill",
                claimed: true,
                locked: false
            ),
            DailyReward(
                id: "4",
                day: 4,
                type: .coins,
                amount: 100,
                title: "100 Coins",
                description: "Keep it up!",
                rarity: .common,
                icon: "dollarsign.circle.fill",
                claimed: true,
                locked: false
            ),
            DailyReward(
                id: "5",
                day: 5,
                type: .special,
                amount: 1,
                title: "Freeze Token",
                description: "Protect your streak",
                rarity: .epic,
                icon: "snowflake",
                claimed: true,
                locked: false
            ),
            DailyReward(
                id: "6",
                day: 6,
                type: .coins,
                amount: 150,
                title: "150 Coins",
                description: "Almost there!",
                rarity: .rare,
                icon: "dollarsign.circle.fill",
                claimed: true,
                locked: false
            ),
            DailyReward(
                id: "7",
                day: 7,
                type: .premium,
                amount: 1,
                title: "Premium Badge",
                description: "Weekly milestone",
                rarity: .legendary,
                icon: "crown.fill",
                claimed: true,
                locked: false
            ),
            DailyReward(
                id: "8",
                day: 8,
                type: .coins,
                amount: 200,
                title: "200 Coins",
                description: "Today's reward",
                rarity: .rare,
                icon: "dollarsign.circle.fill",
                claimed: false,
                locked: false
            ),
            DailyReward(
                id: "9",
                day: 9,
                type: .boost,
                amount: 1,
                title: "Coin Multiplier",
                description: "3x coins for 12h",
                rarity: .epic,
                icon: "star.fill",
                claimed: false,
                locked: true
            ),
            DailyReward(
                id: "10",
                day: 10,
                type: .special,
                amount: 1,
                title: "Mystery Box",
                description: "Random premium reward",
                rarity: .legendary,
                icon: "gift.fill",
                claimed: false,
                locked: true
            ),
            DailyReward(
                id: "11",
                day: 11,
                type: .coins,
                amount: 300,
                title: "300 Coins",
                description: "Big bonus!",
                rarity: .epic,
                icon: "dollarsign.circle.fill",
                claimed: false,
                locked: true
            ),
            DailyReward(
                id: "12",
                day: 12,
                type: .premium,
                amount: 1,
                title: "Exclusive Theme",
                description: "Unlock special theme",
                rarity: .legendary,
                icon: "paintbrush.fill",
                claimed: false,
                locked: true
            ),
            DailyReward(
                id: "13",
                day: 13,
                type: .special,
                amount: 2,
                title: "Double Freeze",
                description: "2 Freeze Tokens",
                rarity: .epic,
                icon: "snowflake",
                claimed: false,
                locked: true
            ),
            DailyReward(
                id: "14",
                day: 14,
                type: .coins,
                amount: 500,
                title: "500 Coins",
                description: "Two weeks strong!",
                rarity: .legendary,
                icon: "dollarsign.circle.fill",
                claimed: false,
                locked: true
            ),
            DailyReward(
                id: "15",
                day: 15,
                type: .premium,
                amount: 1,
                title: "VIP Status",
                description: "Unlock premium features",
                rarity: .legendary,
                icon: "star.circle.fill",
                claimed: false,
                locked: true
            )
        ]
    }
    
    private func loadUserProgress() {
        // Load from UserDefaults
        if let data = UserDefaults.standard.data(forKey: "userRewardStats"),
           let stats = try? JSONDecoder().decode(UserRewardStats.self, from: data) {
            userStats = stats
        }
        
        lastCheckIn = UserDefaults.standard.object(forKey: "lastCheckIn") as? Date
        
        // Check if streak should be broken
        checkStreakStatus()
    }
    
    private func saveUserProgress() {
        if let data = try? JSONEncoder().encode(userStats) {
            UserDefaults.standard.set(data, forKey: "userRewardStats")
        }
        
        if let lastCheckIn = lastCheckIn {
            UserDefaults.standard.set(lastCheckIn, forKey: "lastCheckIn")
        }
    }
    
    private func checkStreakStatus() {
        guard let lastCheckIn = lastCheckIn else { return }
        
        let calendar = Calendar.current
        let now = Date()
        
        // If more than 24 hours have passed, break the streak
        if !calendar.isDate(lastCheckIn, inSameDayAs: now) {
            let daysDifference = calendar.dateComponents([.day], from: lastCheckIn, to: now).day ?? 0
            
            if daysDifference > 1 {
                // Streak broken
                userStats.currentStreak = 0
                resetRewards()
            }
        }
    }
    
    private func resetRewards() {
        for i in 0..<rewards.count {
            rewards[i].claimed = false
            rewards[i].locked = rewards[i].day > 1
        }
    }
    
    // MARK: - Public Methods
    func claimReward(_ rewardId: String) {
        guard let index = rewards.firstIndex(where: { $0.id == rewardId }) else { return }
        
        let reward = rewards[index]
        guard !reward.claimed && !reward.locked else { return }
        
        // Mark as claimed
        rewards[index].claimed = true
        
        // Update user stats
        switch reward.type {
        case .coins:
            userStats.totalRewards += reward.amount
        case .boost:
            // Apply boost (would integrate with boost system)
            break
        case .premium:
            // Unlock premium features
            break
        case .special:
            if reward.title.contains("Freeze") {
                userStats.freezeTokens += reward.amount
            }
        }
        
        // Add XP
        userStats.xp += 50
        
        // Check for level up
        if userStats.xp >= userStats.nextLevelXp {
            levelUp()
        }
        
        // Update streak if this is today's reward
        if reward.day == currentDay {
            userStats.currentStreak += 1
            if userStats.currentStreak > userStats.longestStreak {
                userStats.longestStreak = userStats.currentStreak
            }
            lastCheckIn = Date()
            
            // Unlock next reward
            if let nextReward = rewards.first(where: { $0.day == currentDay + 1 }) {
                if let nextIndex = rewards.firstIndex(where: { $0.id == nextReward.id }) {
                    rewards[nextIndex].locked = false
                }
            }
        }
        
        saveUserProgress()
    }
    
    func useFreezeToken() {
        guard userStats.freezeTokens > 0 else { return }
        
        userStats.freezeTokens -= 1
        // Extend streak protection (would implement streak protection logic)
        
        saveUserProgress()
    }
    
    private func levelUp() {
        userStats.level += 1
        userStats.xp = 0
        userStats.nextLevelXp = userStats.level * 250 // Scaling XP requirement
        
        // Grant level up rewards
        userStats.totalRewards += userStats.level * 10
        userStats.freezeTokens += 1
    }
    
    func resetProgress() {
        userStats = UserRewardStats(
            currentStreak: 0,
            longestStreak: 0,
            totalRewards: 0,
            freezeTokens: 1,
            level: 1,
            xp: 0,
            nextLevelXp: 250
        )
        lastCheckIn = nil
        resetRewards()
        saveUserProgress()
    }
}
