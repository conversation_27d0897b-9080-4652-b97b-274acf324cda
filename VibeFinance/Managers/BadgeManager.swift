//
//  BadgeManager.swift
//  VibeFinance - Advanced Badge Collection System Manager
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import Foundation
import SwiftUI
import Combine

@MainActor
class BadgeManager: ObservableObject {
    @Published var userBadges: [Badge] = []
    @Published var allBadges: [Badge] = []
    @Published var badgeCollection: BadgeCollection?
    @Published var recentlyUnlocked: [Badge] = []
    @Published var featuredShowcase: BadgeShowcase?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupMockBadges()
    }
    
    // MARK: - Data Loading
    
    func loadUserBadges(for userID: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // In a real app, load from Supabase
            // For now, use mock data
            await loadMockUserBadges(for: userID)
            
            isLoading = false
        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
        }
    }
    
    private func loadMockUserBadges(for userID: UUID) async {
        // Simulate network delay
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        let unlockedBadgeIDs = ["first_quest", "week_streak", "social_butterfly", "trading_rookie", "perfect_score"]
        
        userBadges = allBadges.map { badge in
            var updatedBadge = badge
            if unlockedBadgeIDs.contains(badge.id) {
                updatedBadge = Badge(
                    id: badge.id,
                    name: badge.name,
                    description: badge.description,
                    icon: badge.icon,
                    category: badge.category,
                    rarity: badge.rarity,
                    requirements: badge.requirements,
                    rewards: badge.rewards,
                    isUnlocked: true,
                    unlockedAt: Date().addingTimeInterval(-Double.random(in: 86400...604800)),
                    progress: nil
                )
            } else {
                // Add progress for locked badges
                let progress = BadgeProgress(
                    current: Int.random(in: 0...badge.requirements.target),
                    target: badge.requirements.target
                )
                updatedBadge = Badge(
                    id: badge.id,
                    name: badge.name,
                    description: badge.description,
                    icon: badge.icon,
                    category: badge.category,
                    rarity: badge.rarity,
                    requirements: badge.requirements,
                    rewards: badge.rewards,
                    isUnlocked: false,
                    unlockedAt: nil,
                    progress: progress
                )
            }
            return updatedBadge
        }
        
        // Create badge collection
        badgeCollection = BadgeCollection(
            userID: userID,
            badges: userBadges,
            totalBadges: allBadges.count,
            unlockedBadges: userBadges.filter { $0.isUnlocked }.count,
            favoriteBadge: "week_streak",
            displayBadges: ["first_quest", "week_streak", "social_butterfly"]
        )
        
        // Set recently unlocked
        recentlyUnlocked = userBadges.filter { $0.isUnlocked }.suffix(3).map { $0 }
        
        // Set featured showcase
        featuredShowcase = BadgeShowcase(
            title: "🔥 Gen Z Finance Masters",
            description: "Limited time badges for the most dedicated finance learners!",
            badges: userBadges.filter { $0.category == .special },
            isLimited: true,
            expiresAt: Date().addingTimeInterval(604800) // 1 week
        )
    }
    
    // MARK: - Badge Operations
    
    func unlockBadge(_ badgeID: String, for userID: UUID) async -> Bool {
        guard let badgeIndex = userBadges.firstIndex(where: { $0.id == badgeID }),
              !userBadges[badgeIndex].isUnlocked else { return false }
        
        let badge = userBadges[badgeIndex]
        
        // Check if requirements are met
        if await checkBadgeRequirements(badge, for: userID) {
            // Unlock the badge
            let unlockedBadge = Badge(
                id: badge.id,
                name: badge.name,
                description: badge.description,
                icon: badge.icon,
                category: badge.category,
                rarity: badge.rarity,
                requirements: badge.requirements,
                rewards: badge.rewards,
                isUnlocked: true,
                unlockedAt: Date(),
                progress: nil
            )
            
            userBadges[badgeIndex] = unlockedBadge
            recentlyUnlocked.insert(unlockedBadge, at: 0)
            
            // Update collection
            if var collection = badgeCollection {
                collection = BadgeCollection(
                    userID: collection.userID,
                    badges: userBadges,
                    totalBadges: collection.totalBadges,
                    unlockedBadges: collection.unlockedBadges + 1,
                    favoriteBadge: collection.favoriteBadge,
                    displayBadges: collection.displayBadges
                )
                badgeCollection = collection
            }
            
            // Show celebration animation
            await showBadgeUnlockCelebration(unlockedBadge)
            
            return true
        }
        
        return false
    }
    
    private func checkBadgeRequirements(_ badge: Badge, for userID: UUID) async -> Bool {
        // In a real app, check against user's actual stats
        // For now, simulate requirement checking
        return true
    }
    
    private func showBadgeUnlockCelebration(_ badge: Badge) async {
        // Trigger celebration animation and notification
        // This would integrate with the notification system
        print("🎉 Badge Unlocked: \(badge.name)")
    }
    
    func updateDisplayBadges(_ badgeIDs: [String]) async {
        guard var collection = badgeCollection else { return }
        
        collection = BadgeCollection(
            userID: collection.userID,
            badges: collection.badges,
            totalBadges: collection.totalBadges,
            unlockedBadges: collection.unlockedBadges,
            favoriteBadge: collection.favoriteBadge,
            displayBadges: Array(badgeIDs.prefix(3))
        )
        
        badgeCollection = collection
    }
    
    func setFavoriteBadge(_ badgeID: String) async {
        guard var collection = badgeCollection else { return }
        
        collection = BadgeCollection(
            userID: collection.userID,
            badges: collection.badges,
            totalBadges: collection.totalBadges,
            unlockedBadges: collection.unlockedBadges,
            favoriteBadge: badgeID,
            displayBadges: collection.displayBadges
        )
        
        badgeCollection = collection
    }
    
    // MARK: - Filtering and Sorting
    
    func getBadges(by category: BadgeCategory) -> [Badge] {
        return userBadges.filter { $0.category == category }
    }
    
    func getBadges(by rarity: BadgeRarity) -> [Badge] {
        return userBadges.filter { $0.rarity == rarity }
    }
    
    func getUnlockedBadges() -> [Badge] {
        return userBadges.filter { $0.isUnlocked }
    }
    
    func getLockedBadges() -> [Badge] {
        return userBadges.filter { !$0.isUnlocked }
    }

    // MARK: - Mock Data Setup

    private func setupMockBadges() {
        allBadges = [
            // Learning Badges
            Badge(
                id: "first_quest",
                name: "First Steps 👶",
                description: "Complete your very first quest",
                icon: "star.fill",
                category: .learning,
                rarity: .common,
                requirements: BadgeRequirements(type: .questsCompleted, target: 1, description: "Complete 1 quest", additionalConditions: nil),
                rewards: BadgeRewards(xp: 50, coins: 100, unlockableThemes: nil, specialPerks: nil, title: "Beginner"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),
            Badge(
                id: "knowledge_seeker",
                name: "Knowledge Seeker 🧠",
                description: "Complete 10 learning quests",
                icon: "brain.head.profile",
                category: .learning,
                rarity: .uncommon,
                requirements: BadgeRequirements(type: .questsCompleted, target: 10, description: "Complete 10 quests", additionalConditions: nil),
                rewards: BadgeRewards(xp: 200, coins: 500, unlockableThemes: ["neon"], specialPerks: ["Double XP weekends"], title: "Scholar"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),
            Badge(
                id: "perfect_score",
                name: "Perfect Score 💯",
                description: "Get 100% on 5 quests in a row",
                icon: "target",
                category: .learning,
                rarity: .rare,
                requirements: BadgeRequirements(type: .perfectScores, target: 5, description: "Perfect scores on 5 consecutive quests", additionalConditions: nil),
                rewards: BadgeRewards(xp: 500, coins: 1000, unlockableThemes: ["gold"], specialPerks: ["Perfect Score Streak Multiplier"], title: "Perfectionist"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),

            // Trading Badges
            Badge(
                id: "trading_rookie",
                name: "Trading Rookie 📊",
                description: "Make your first simulated trade",
                icon: "chart.line.uptrend.xyaxis",
                category: .trading,
                rarity: .common,
                requirements: BadgeRequirements(type: .tradingVolume, target: 1, description: "Complete 1 trade", additionalConditions: nil),
                rewards: BadgeRewards(xp: 75, coins: 200, unlockableThemes: nil, specialPerks: nil, title: "Trader"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),
            Badge(
                id: "profit_master",
                name: "Profit Master 💰",
                description: "Achieve 20% portfolio growth",
                icon: "dollarsign.circle.fill",
                category: .trading,
                rarity: .epic,
                requirements: BadgeRequirements(type: .portfolioValue, target: 20, description: "20% portfolio growth", additionalConditions: nil),
                rewards: BadgeRewards(xp: 1000, coins: 2500, unlockableThemes: ["diamond"], specialPerks: ["VIP Trading Insights"], title: "Profit Master"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),

            // Social Badges
            Badge(
                id: "social_butterfly",
                name: "Social Butterfly 🦋",
                description: "Join 3 different squads",
                icon: "person.3.fill",
                category: .social,
                rarity: .uncommon,
                requirements: BadgeRequirements(type: .socialInteractions, target: 3, description: "Join 3 squads", additionalConditions: nil),
                rewards: BadgeRewards(xp: 150, coins: 300, unlockableThemes: nil, specialPerks: ["Squad Chat Priority"], title: "Social"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),
            Badge(
                id: "influencer",
                name: "Finance Influencer 🌟",
                description: "Get 100 likes on your posts",
                icon: "heart.fill",
                category: .social,
                rarity: .rare,
                requirements: BadgeRequirements(type: .socialInteractions, target: 100, description: "100 post likes", additionalConditions: nil),
                rewards: BadgeRewards(xp: 750, coins: 1500, unlockableThemes: ["rainbow"], specialPerks: ["Verified Badge", "Featured Posts"], title: "Influencer"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),

            // Milestone Badges
            Badge(
                id: "week_streak",
                name: "Dedicated Learner 🔥",
                description: "Maintain a 7-day login streak",
                icon: "flame.fill",
                category: .milestones,
                rarity: .rare,
                requirements: BadgeRequirements(type: .streakDays, target: 7, description: "7-day login streak", additionalConditions: nil),
                rewards: BadgeRewards(xp: 300, coins: 750, unlockableThemes: ["fire"], specialPerks: ["Streak Protection"], title: "Dedicated"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),
            Badge(
                id: "centurion",
                name: "Centurion 💪",
                description: "Complete 100 total quests",
                icon: "100.circle.fill",
                category: .milestones,
                rarity: .legendary,
                requirements: BadgeRequirements(type: .questsCompleted, target: 100, description: "Complete 100 quests", additionalConditions: nil),
                rewards: BadgeRewards(xp: 2000, coins: 5000, unlockableThemes: ["legendary"], specialPerks: ["Legendary Status", "Exclusive Content"], title: "Centurion"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            ),

            // Special Badges
            Badge(
                id: "gen_z_master",
                name: "Gen Z Finance Master 👑",
                description: "Unlock all Gen Z exclusive features",
                icon: "crown.fill",
                category: .special,
                rarity: .mythic,
                requirements: BadgeRequirements(type: .achievementsUnlocked, target: 50, description: "Unlock 50 achievements", additionalConditions: ["Complete Gen Z onboarding", "Join exclusive squad"]),
                rewards: BadgeRewards(xp: 5000, coins: 10000, unlockableThemes: ["mythic"], specialPerks: ["Mythic Status", "Early Access", "Custom Avatar"], title: "Gen Z Master"),
                isUnlocked: false,
                unlockedAt: nil,
                progress: nil
            )
        ]
    }
}
