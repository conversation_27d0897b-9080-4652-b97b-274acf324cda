//
//  NotificationManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import UserNotifications
import Combine
import UIKit

@MainActor
class NotificationManager: NSObject, ObservableObject {
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    @Published var isEnabled = false
    @Published var errorMessage: String?
    
    private let notificationCenter = UNUserNotificationCenter.current()
    
    override init() {
        super.init()
        notificationCenter.delegate = self
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    
    func requestAuthorization() async {
        do {
            let granted = try await notificationCenter.requestAuthorization(
                options: [.alert, .badge, .sound, .provisional]
            )
            
            await MainActor.run {
                self.isEnabled = granted
                self.checkAuthorizationStatus()
            }
            
            if granted {
                await registerCategories()
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to request notification permission: \(error.localizedDescription)"
            }
        }
    }
    
    func checkAuthorizationStatus() {
        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.authorizationStatus = settings.authorizationStatus
                self.isEnabled = settings.authorizationStatus == .authorized
            }
        }
    }
    
    private func registerCategories() async {
        let questCategory = UNNotificationCategory(
            identifier: "QUEST_REMINDER",
            actions: [
                UNNotificationAction(
                    identifier: "START_QUEST",
                    title: "Start Quest",
                    options: [.foreground]
                ),
                UNNotificationAction(
                    identifier: "REMIND_LATER",
                    title: "Remind Later",
                    options: []
                )
            ],
            intentIdentifiers: [],
            options: []
        )
        
        let squadCategory = UNNotificationCategory(
            identifier: "SQUAD_UPDATE",
            actions: [
                UNNotificationAction(
                    identifier: "VIEW_SQUAD",
                    title: "View Squad",
                    options: [.foreground]
                ),
                UNNotificationAction(
                    identifier: "VOTE_NOW",
                    title: "Vote Now",
                    options: [.foreground]
                )
            ],
            intentIdentifiers: [],
            options: []
        )
        
        let marketCategory = UNNotificationCategory(
            identifier: "MARKET_ALERT",
            actions: [
                UNNotificationAction(
                    identifier: "VIEW_PORTFOLIO",
                    title: "View Portfolio",
                    options: [.foreground]
                ),
                UNNotificationAction(
                    identifier: "TRADE_NOW",
                    title: "Trade Now",
                    options: [.foreground]
                )
            ],
            intentIdentifiers: [],
            options: []
        )
        
        notificationCenter.setNotificationCategories([questCategory, squadCategory, marketCategory])
    }
    
    // MARK: - Quest Notifications
    
    func scheduleQuestReminder(quest: Quest, delay: TimeInterval = 3600) async {
        guard isEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "🎯 Daily Quest Available!"
        content.body = "Complete '\(quest.title)' and earn \(quest.xpReward) XP"
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "QUEST_REMINDER"
        content.userInfo = [
            "type": "quest_reminder",
            "quest_id": quest.id.uuidString,
            "quest_title": quest.title
        ]
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: delay, repeats: false)
        let request = UNNotificationRequest(
            identifier: "quest_\(quest.id.uuidString)",
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to schedule quest reminder: \(error.localizedDescription)"
            }
        }
    }
    
    func scheduleDailyQuestReminder() async {
        guard isEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "🌅 Good Morning!"
        content.body = "Your daily quest is ready. Start your financial journey today!"
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "QUEST_REMINDER"
        content.userInfo = ["type": "daily_quest_reminder"]
        
        // Schedule for 9 AM daily
        var dateComponents = DateComponents()
        dateComponents.hour = 9
        dateComponents.minute = 0
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
        let request = UNNotificationRequest(
            identifier: "daily_quest_reminder",
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to schedule daily quest reminder: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Squad Notifications
    
    func scheduleSquadUpdate(squad: Squad, message: String, type: SquadNotificationType) async {
        guard isEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "SQUAD_UPDATE"
        content.userInfo = [
            "type": "squad_update",
            "squad_id": squad.id.uuidString,
            "squad_name": squad.name,
            "notification_type": type.rawValue
        ]
        
        switch type {
        case .newProposal:
            content.title = "💡 New Investment Proposal"
            content.body = "\(squad.name): \(message)"
        case .votingEnded:
            content.title = "🗳️ Voting Complete"
            content.body = "\(squad.name): \(message)"
        case .newMember:
            content.title = "👋 New Squad Member"
            content.body = "\(message) joined \(squad.name)"
        case .chatMessage:
            content.title = "\(squad.emoji) \(squad.name)"
            content.body = message
        }
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "squad_\(squad.id.uuidString)_\(Date().timeIntervalSince1970)",
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to schedule squad notification: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Market Notifications
    
    func scheduleMarketAlert(symbol: String, price: Double, change: Double, type: MarketAlertType) async {
        guard isEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.sound = .default
        content.badge = 1
        content.categoryIdentifier = "MARKET_ALERT"
        content.userInfo = [
            "type": "market_alert",
            "symbol": symbol,
            "price": price,
            "change": change,
            "alert_type": type.rawValue
        ]
        
        switch type {
        case .priceTarget:
            content.title = "🎯 Price Target Hit!"
            content.body = "\(symbol) reached $\(String(format: "%.2f", price))"
        case .significantMove:
            let direction = change >= 0 ? "up" : "down"
            content.title = "📈 Significant Move"
            content.body = "\(symbol) is \(direction) \(String(format: "%.1f", abs(change)))% today"
        case .portfolioAlert:
            content.title = "💼 Portfolio Update"
            content.body = "Your portfolio is \(change >= 0 ? "up" : "down") \(String(format: "%.1f", abs(change)))% today"
        case .newsAlert:
            content.title = "📰 Market News"
            content.body = "Important news about \(symbol)"
        }
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "market_\(symbol)_\(Date().timeIntervalSince1970)",
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to schedule market alert: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Achievement Notifications
    
    func scheduleAchievementUnlocked(achievement: Achievement) async {
        guard isEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "🏆 Achievement Unlocked!"
        content.body = "\(achievement.title) - \(achievement.description)"
        content.sound = .default
        content.badge = 1
        content.userInfo = [
            "type": "achievement_unlocked",
            "achievement_id": achievement.id,
            "achievement_title": achievement.title
        ]
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "achievement_\(achievement.id)",
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to schedule achievement notification: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Level Up Notifications
    
    func scheduleLevelUpNotification(newLevel: Int, xpEarned: Int) async {
        guard isEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "🎉 Level Up!"
        content.body = "Congratulations! You've reached level \(newLevel)"
        content.sound = .default
        content.badge = 1
        content.userInfo = [
            "type": "level_up",
            "new_level": newLevel,
            "xp_earned": xpEarned
        ]
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(
            identifier: "level_up_\(newLevel)",
            content: content,
            trigger: trigger
        )
        
        do {
            try await notificationCenter.add(request)
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to schedule level up notification: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Utility Methods
    
    func cancelNotification(identifier: String) {
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [identifier])
    }
    
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
    }
    
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await notificationCenter.pendingNotificationRequests()
    }
    
    func clearBadge() {
        if #available(iOS 16.0, *) {
            UNUserNotificationCenter.current().setBadgeCount(0) { error in
                if let error = error {
                    print("Failed to clear badge: \(error)")
                }
            }
        } else {
            UIApplication.shared.applicationIconBadgeNumber = 0
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {
    nonisolated func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }

    nonisolated func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        Task { @MainActor in
            switch response.actionIdentifier {
            case "START_QUEST":
                handleQuestAction(userInfo: userInfo)
            case "VIEW_SQUAD":
                handleSquadAction(userInfo: userInfo)
            case "VIEW_PORTFOLIO":
                handlePortfolioAction(userInfo: userInfo)
            case "TRADE_NOW":
                handleTradeAction(userInfo: userInfo)
            case UNNotificationDefaultActionIdentifier:
                handleDefaultAction(userInfo: userInfo)
            default:
                break
            }
        }
        
        completionHandler()
    }
    
    private func handleQuestAction(userInfo: [AnyHashable: Any]) {
        // Navigate to quest detail
        if let questId = userInfo["quest_id"] as? String {
            NotificationCenter.default.post(
                name: .navigateToQuest,
                object: nil,
                userInfo: ["quest_id": questId]
            )
        }
    }
    
    private func handleSquadAction(userInfo: [AnyHashable: Any]) {
        // Navigate to squad
        if let squadId = userInfo["squad_id"] as? String {
            NotificationCenter.default.post(
                name: .navigateToSquad,
                object: nil,
                userInfo: ["squad_id": squadId]
            )
        }
    }
    
    private func handlePortfolioAction(userInfo: [AnyHashable: Any]) {
        // Navigate to portfolio
        NotificationCenter.default.post(name: .navigateToPortfolio, object: nil)
    }
    
    private func handleTradeAction(userInfo: [AnyHashable: Any]) {
        // Navigate to trading
        if let symbol = userInfo["symbol"] as? String {
            NotificationCenter.default.post(
                name: .navigateToTrade,
                object: nil,
                userInfo: ["symbol": symbol]
            )
        }
    }
    
    private func handleDefaultAction(userInfo: [AnyHashable: Any]) {
        // Handle default tap action based on notification type
        guard let type = userInfo["type"] as? String else { return }
        
        switch type {
        case "quest_reminder", "daily_quest_reminder":
            NotificationCenter.default.post(name: .navigateToQuests, object: nil)
        case "squad_update":
            handleSquadAction(userInfo: userInfo)
        case "market_alert":
            handlePortfolioAction(userInfo: userInfo)
        case "achievement_unlocked":
            NotificationCenter.default.post(name: .navigateToProfile, object: nil)
        case "level_up":
            NotificationCenter.default.post(name: .navigateToProfile, object: nil)
        default:
            break
        }
    }
}

// MARK: - Supporting Types
enum SquadNotificationType: String {
    case newProposal = "new_proposal"
    case votingEnded = "voting_ended"
    case newMember = "new_member"
    case chatMessage = "chat_message"
}

enum MarketAlertType: String {
    case priceTarget = "price_target"
    case significantMove = "significant_move"
    case portfolioAlert = "portfolio_alert"
    case newsAlert = "news_alert"
}

// MARK: - Notification Names
extension Notification.Name {
    static let navigateToQuest = Notification.Name("navigateToQuest")
    static let navigateToQuests = Notification.Name("navigateToQuests")
    static let navigateToSquad = Notification.Name("navigateToSquad")
    static let navigateToPortfolio = Notification.Name("navigateToPortfolio")
    static let navigateToTrade = Notification.Name("navigateToTrade")
    static let navigateToProfile = Notification.Name("navigateToProfile")
}
