import SwiftUI
import Foundation

// MARK: - Advanced Theme Manager with Unlockable Themes
@MainActor
class ThemeManager: ObservableObject {
    @Published var availableThemes: [AppTheme] = []
    @Published var currentTheme: AppTheme?
    @Published var unlockedThemes: Set<String> = []

    var unlockedCount: Int {
        availableThemes.filter { $0.isUnlocked }.count
    }

    var totalThemes: Int {
        availableThemes.count
    }

    init() {
        loadDefaultThemes()
        loadUserProgress()
    }

    private func loadDefaultThemes() {
        availableThemes = [
            AppTheme(
                id: "classic",
                name: "Classic Green",
                description: "The OG money vibes 💚",
                colors: ThemeColors(
                    primary: "#10b981",
                    secondary: "#065f46",
                    accent: "#34d399",
                    background: "#f0fdf4",
                    foreground: "#064e3b"
                ),
                gradient: [Color.green.opacity(0.8), Color.emerald],
                icon: "dollarsign.circle.fill",
                isUnlocked: true,
                unlockCondition: "Default theme",
                rarity: .common,
                price: nil
            ),

            AppTheme(
                id: "neon",
                name: "Neon Dreams",
                description: "Cyberpunk financial future 🌆",
                colors: ThemeColors(
                    primary: "#8b5cf6",
                    secondary: "#3730a3",
                    accent: "#a78bfa",
                    background: "#0f0f23",
                    foreground: "#e0e7ff"
                ),
                gradient: [Color.purple, Color.pink],
                icon: "bolt.fill",
                isUnlocked: false,
                unlockCondition: "Reach level 5",
                rarity: .rare,
                price: 500
            ),

            AppTheme(
                id: "sunset",
                name: "Sunset Vibes",
                description: "Golden hour aesthetics 🌅",
                colors: ThemeColors(
                    primary: "#f59e0b",
                    secondary: "#92400e",
                    accent: "#fbbf24",
                    background: "#fffbeb",
                    foreground: "#78350f"
                ),
                gradient: [Color.orange, Color.red],
                icon: "sun.max.fill",
                isUnlocked: false,
                unlockCondition: "Save $1000 in virtual portfolio",
                rarity: .epic,
                price: 1000
            ),

            AppTheme(
                id: "royal",
                name: "Royal Purple",
                description: "For the financial royalty 👑",
                colors: ThemeColors(
                    primary: "#7c3aed",
                    secondary: "#4c1d95",
                    accent: "#a855f7",
                    background: "#faf5ff",
                    foreground: "#581c87"
                ),
                gradient: [Color.purple, Color.indigo],
                icon: "crown.fill",
                isUnlocked: false,
                unlockCondition: "Complete 50 transactions",
                rarity: .legendary,
                price: 2000
            ),

            AppTheme(
                id: "diamond",
                name: "Diamond Elite",
                description: "Ultimate flex mode 💎",
                colors: ThemeColors(
                    primary: "#06b6d4",
                    secondary: "#0e7490",
                    accent: "#22d3ee",
                    background: "#f0fdff",
                    foreground: "#164e63"
                ),
                gradient: [Color.cyan, Color.blue],
                icon: "diamond.fill",
                isUnlocked: false,
                unlockCondition: "Reach VIP status",
                rarity: .legendary,
                price: 5000
            )
        ]

        // Set default theme
        if currentTheme == nil {
            currentTheme = availableThemes.first { $0.id == "classic" }
        }

        // Update unlock status
        updateThemeUnlockStatus()
    }

    private func loadUserProgress() {
        // Load from UserDefaults or Core Data
        if let savedUnlocked = UserDefaults.standard.object(forKey: "unlockedThemes") as? [String] {
            unlockedThemes = Set(savedUnlocked)
        } else {
            unlockedThemes = ["classic"] // Default unlocked theme
        }

        if let savedThemeId = UserDefaults.standard.string(forKey: "currentTheme") {
            currentTheme = availableThemes.first { $0.id == savedThemeId }
        }

        updateThemeUnlockStatus()
    }

    private func updateThemeUnlockStatus() {
        for i in 0..<availableThemes.count {
            availableThemes[i].isUnlocked = unlockedThemes.contains(availableThemes[i].id)
        }
    }

    func setCurrentTheme(_ theme: AppTheme) {
        guard theme.isUnlocked else { return }

        currentTheme = theme
        UserDefaults.standard.set(theme.id, forKey: "currentTheme")

        // Apply theme globally
        applyThemeGlobally(theme)
    }

    func unlockTheme(_ themeId: String) {
        unlockedThemes.insert(themeId)
        UserDefaults.standard.set(Array(unlockedThemes), forKey: "unlockedThemes")
        updateThemeUnlockStatus()
    }

    private func applyThemeGlobally(_ theme: AppTheme) {
        // Update app-wide theme settings
        NotificationCenter.default.post(
            name: NSNotification.Name("ThemeChanged"),
            object: theme
        )
    }

    func checkUnlockConditions(userLevel: Int, portfolioValue: Double, transactionCount: Int) {
        // Check and auto-unlock themes based on user progress
        for theme in availableThemes {
            if !theme.isUnlocked {
                var shouldUnlock = false

                switch theme.id {
                case "neon":
                    shouldUnlock = userLevel >= 5
                case "sunset":
                    shouldUnlock = portfolioValue >= 11000 // $1000 gain from $10K start
                case "royal":
                    shouldUnlock = transactionCount >= 50
                case "diamond":
                    shouldUnlock = userLevel >= 10 // VIP status
                default:
                    break
                }

                if shouldUnlock {
                    unlockTheme(theme.id)
                }
            }
        }
    }
}

// MARK: - New Theme Models for Customization
struct AppTheme: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let colors: ThemeColors
    let gradientColors: [String] // Store as hex strings for Codable
    let icon: String
    var isUnlocked: Bool
    let unlockCondition: String
    let rarity: ThemeRarity
    let price: Int?

    var gradient: [Color] {
        gradientColors.map { Color(hex: $0) }
    }

    enum ThemeRarity: String, CaseIterable, Codable {
        case common = "Common"
        case rare = "Rare"
        case epic = "Epic"
        case legendary = "Legendary"

        var color: Color {
            switch self {
            case .common: return .gray
            case .rare: return .blue
            case .epic: return .purple
            case .legendary: return .yellow
            }
        }
    }
}

struct ThemeColors: Codable {
    let primary: String
    let secondary: String
    let accent: String
    let background: String
    let foreground: String

    static let dark = ThemeColors(
        primary: "#8b5cf6",
        secondary: "#6366f1",
        accent: "#a855f7",
        background: "#0f0f23",
        foreground: "#ffffff"
    )


    var primaryColor: Color { Color(hex: primary) }
    var secondaryColor: Color { Color(hex: secondary) }
    var accentColor: Color { Color(hex: accent) }
    var backgroundColor: Color { Color(hex: background) }
    var foregroundColor: Color { Color(hex: foreground) }
}

// MARK: - Legacy Glassmorphic Theme Colors (Keep for compatibility)
struct LegacyThemeColors {
    let primary: Color
    let secondary: Color
    let accent: Color
    let background: LinearGradient
    let surface: LinearGradient
    let cardBackground: LinearGradient
    let onPrimary: Color
    let onSecondary: Color
    let onBackground: Color
    let onSurface: Color
    let success: Color
    let warning: Color
    let error: Color
    let glassMaterial: Material

    // Beautiful Dark Theme with Glassmorphic Design
    static let dark = LegacyThemeColors(
        primary: Color(red: 0.3, green: 0.2, blue: 0.6),
        secondary: Color(red: 0.2, green: 0.3, blue: 0.7),
        accent: Color(red: 1.0, green: 0.84, blue: 0.0), // Warren Buffett Gold
        background: LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.15),
                Color(red: 0.1, green: 0.05, blue: 0.2),
                Color(red: 0.05, green: 0.1, blue: 0.25),
                Color(red: 0.02, green: 0.02, blue: 0.1)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        surface: LinearGradient(
            colors: [
                Color.white.opacity(0.15),
                Color.white.opacity(0.1),
                Color(red: 0.3, green: 0.2, blue: 0.6).opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        cardBackground: LinearGradient(
            colors: [
                Color.white.opacity(0.2),
                Color.white.opacity(0.1),
                Color(red: 0.2, green: 0.3, blue: 0.7).opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        onPrimary: Color.white,
        onSecondary: Color.white,
        onBackground: Color.white,
        onSurface: Color.white.opacity(0.9),
        success: Color(red: 0.3, green: 0.9, blue: 0.5),
        warning: Color(red: 1.0, green: 0.7, blue: 0.2),
        error: Color(red: 1.0, green: 0.3, blue: 0.4),
        glassMaterial: .ultraThinMaterial
    )
}

// MARK: - Glassmorphic Card Styles (Dark Mode Only)
struct GlassmorphicCard: ViewModifier {
    let theme: LegacyThemeColors
    let cornerRadius: CGFloat
    let shadowRadius: CGFloat

    init(theme: LegacyThemeColors, cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) {
        self.theme = theme
        self.cornerRadius = cornerRadius
        self.shadowRadius = shadowRadius
    }

    func body(content: Content) -> some View {
        content
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(theme.cardBackground)
                    .background(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(theme.glassMaterial)
                    )
                    .shadow(
                        color: Color.black.opacity(0.3),
                        radius: shadowRadius,
                        x: 0,
                        y: 4
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.3),
                                Color.white.opacity(0.15),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
}

extension View {
    func glassmorphicCard(theme: LegacyThemeColors, cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) -> some View {
        modifier(GlassmorphicCard(theme: theme, cornerRadius: cornerRadius, shadowRadius: shadowRadius))
    }
}

// MARK: - Theme Environment Key
struct ThemeEnvironmentKey: EnvironmentKey {
    static let defaultValue: LegacyThemeColors = LegacyThemeColors.dark
}

extension EnvironmentValues {
    var theme: LegacyThemeColors {
        get { self[ThemeEnvironmentKey.self] }
        set { self[ThemeEnvironmentKey.self] = newValue }
    }
}

// MARK: - Theme Modifier (Dark Mode Only)
struct ThemedView: ViewModifier {
    @ObservedObject var themeManager: ThemeManager

    func body(content: Content) -> some View {
        content
            .environment(\.theme, LegacyThemeColors.dark)
            .preferredColorScheme(.dark)
    }
}

extension View {
    func themed(_ themeManager: ThemeManager) -> some View {
        modifier(ThemedView(themeManager: themeManager))
    }
}

// MARK: - Color Extension for Hex Support
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
