import SwiftUI
import Combine

// MARK: - Theme Manager
@MainActor
class ThemeManager: ObservableObject {
    @Published var currentTheme: AppTheme = .dark
    @Published var useSystemTheme: Bool = false
    @Published var colors: ThemeColors = ThemeColors.dark

    private let userDefaults = UserDefaults.standard
    private let themeKey = "selectedTheme"
    private let systemThemeKey = "useSystemTheme"

    init() {
        loadThemePreference()
        updateColors()
    }
    
    func setTheme(_ theme: AppTheme) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentTheme = theme
            useSystemTheme = false
            updateColors()
            saveThemePreference()
        }
    }
    
    func toggleSystemTheme() {
        useSystemTheme.toggle()
        if useSystemTheme {
            updateThemeFromSystem()
        }
        saveThemePreference()
    }
    
    func updateThemeFromSystem() {
        guard useSystemTheme else { return }

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            currentTheme = window.traitCollection.userInterfaceStyle == .dark ? .dark : .light
            updateColors()
        }
    }

    private func updateColors() {
        colors = currentTheme == .light ? ThemeColors.light : ThemeColors.dark
    }
    
    private func loadThemePreference() {
        useSystemTheme = userDefaults.bool(forKey: systemThemeKey)
        
        if useSystemTheme {
            updateThemeFromSystem()
        } else {
            let themeRawValue = userDefaults.string(forKey: themeKey) ?? AppTheme.dark.rawValue
            currentTheme = AppTheme(rawValue: themeRawValue) ?? .dark
        }
    }
    
    private func saveThemePreference() {
        userDefaults.set(currentTheme.rawValue, forKey: themeKey)
        userDefaults.set(useSystemTheme, forKey: systemThemeKey)
    }
}

// MARK: - App Theme Enum
enum AppTheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    
    var displayName: String {
        switch self {
        case .light: return "Light"
        case .dark: return "Dark"
        }
    }
    
    var colorScheme: ColorScheme {
        switch self {
        case .light: return .light
        case .dark: return .dark
        }
    }
}

// MARK: - Glassmorphic Theme Colors
struct ThemeColors {
    let primary: Color
    let secondary: Color
    let accent: Color
    let background: LinearGradient
    let surface: LinearGradient
    let cardBackground: LinearGradient
    let onPrimary: Color
    let onSecondary: Color
    let onBackground: Color
    let onSurface: Color
    let success: Color
    let warning: Color
    let error: Color
    let glassMaterial: Material

    // Beautiful Light Theme with Glassmorphic Design
    static let light = ThemeColors(
        primary: Color(red: 0.2, green: 0.4, blue: 0.8),
        secondary: Color(red: 0.5, green: 0.3, blue: 0.8),
        accent: Color(red: 1.0, green: 0.84, blue: 0.0), // Warren Buffett Gold
        background: LinearGradient(
            colors: [
                Color(red: 0.98, green: 0.99, blue: 1.0),
                Color(red: 0.95, green: 0.97, blue: 1.0),
                Color(red: 0.92, green: 0.95, blue: 0.98),
                Color(red: 0.96, green: 0.98, blue: 1.0)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        surface: LinearGradient(
            colors: [
                Color.white.opacity(0.9),
                Color.white.opacity(0.8),
                Color(red: 0.98, green: 0.99, blue: 1.0).opacity(0.85)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        cardBackground: LinearGradient(
            colors: [
                Color.white.opacity(0.95),
                Color.white.opacity(0.85),
                Color(red: 0.96, green: 0.98, blue: 1.0).opacity(0.9)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        onPrimary: Color.white,
        onSecondary: Color.white,
        onBackground: Color(red: 0.1, green: 0.1, blue: 0.2), // Dark text for light background
        onSurface: Color(red: 0.15, green: 0.15, blue: 0.25), // Dark text for light surfaces
        success: Color(red: 0.0, green: 0.6, blue: 0.2),
        warning: Color(red: 0.9, green: 0.5, blue: 0.0),
        error: Color(red: 0.8, green: 0.1, blue: 0.2),
        glassMaterial: .ultraThinMaterial
    )

    // Beautiful Dark Theme with Glassmorphic Design
    static let dark = ThemeColors(
        primary: Color(red: 0.3, green: 0.2, blue: 0.6),
        secondary: Color(red: 0.2, green: 0.3, blue: 0.7),
        accent: Color(red: 1.0, green: 0.84, blue: 0.0), // Warren Buffett Gold
        background: LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.15),
                Color(red: 0.1, green: 0.05, blue: 0.2),
                Color(red: 0.05, green: 0.1, blue: 0.25),
                Color(red: 0.02, green: 0.02, blue: 0.1)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        surface: LinearGradient(
            colors: [
                Color.white.opacity(0.15),
                Color.white.opacity(0.1),
                Color(red: 0.3, green: 0.2, blue: 0.6).opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        cardBackground: LinearGradient(
            colors: [
                Color.white.opacity(0.2),
                Color.white.opacity(0.1),
                Color(red: 0.2, green: 0.3, blue: 0.7).opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        onPrimary: Color.white,
        onSecondary: Color.white,
        onBackground: Color.white,
        onSurface: Color.white.opacity(0.9),
        success: Color(red: 0.3, green: 0.9, blue: 0.5),
        warning: Color(red: 1.0, green: 0.7, blue: 0.2),
        error: Color(red: 1.0, green: 0.3, blue: 0.4),
        glassMaterial: .ultraThinMaterial
    )
}

// MARK: - Glassmorphic Card Styles
struct GlassmorphicCard: ViewModifier {
    let theme: ThemeColors
    let cornerRadius: CGFloat
    let shadowRadius: CGFloat

    init(theme: ThemeColors, cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) {
        self.theme = theme
        self.cornerRadius = cornerRadius
        self.shadowRadius = shadowRadius
    }

    func body(content: Content) -> some View {
        content
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(theme.cardBackground)
                    .background(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(.ultraThinMaterial)
                    )
                    .shadow(
                        color: Color.black.opacity(0.1),
                        radius: shadowRadius,
                        x: 0,
                        y: 4
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.3),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
}

extension View {
    func glassmorphicCard(theme: ThemeColors, cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) -> some View {
        modifier(GlassmorphicCard(theme: theme, cornerRadius: cornerRadius, shadowRadius: shadowRadius))
    }
}



// MARK: - Theme Environment Key
struct ThemeEnvironmentKey: EnvironmentKey {
    static let defaultValue: ThemeColors = ThemeColors.dark
}

extension EnvironmentValues {
    var theme: ThemeColors {
        get { self[ThemeEnvironmentKey.self] }
        set { self[ThemeEnvironmentKey.self] = newValue }
    }
}

// MARK: - Theme Modifier
struct ThemedView: ViewModifier {
    @ObservedObject var themeManager: ThemeManager
    
    func body(content: Content) -> some View {
        content
            .environment(\.theme, themeManager.currentTheme == .dark ? ThemeColors.dark : ThemeColors.light)
            .preferredColorScheme(themeManager.currentTheme.colorScheme)
    }
}

extension View {
    func themed(_ themeManager: ThemeManager) -> some View {
        modifier(ThemedView(themeManager: themeManager))
    }
}
