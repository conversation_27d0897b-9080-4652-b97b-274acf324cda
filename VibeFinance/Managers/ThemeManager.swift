import SwiftUI
import Combine

// MARK: - Theme Manager (Dark Mode Only)
@MainActor
class ThemeManager: ObservableObject {
    @Published var currentTheme: AppTheme = .dark
    @Published var colors: ThemeColors = ThemeColors.dark

    init() {
        // Always use dark mode - no more light mode complexity
        currentTheme = .dark
        colors = ThemeColors.dark
    }

    // Keep these methods for compatibility but they won't change the theme
    func setTheme(_ theme: AppTheme) {
        // Force dark mode always
        currentTheme = .dark
        colors = ThemeColors.dark
    }

    func toggleSystemTheme() {
        // No-op: Always stay in dark mode
    }

    func updateThemeFromSystem() {
        // No-op: Always stay in dark mode
    }
}

// MARK: - App Theme Enum (Dark Mode Only)
enum AppTheme: String, CaseIterable {
    case dark = "dark"

    var displayName: String {
        return "Dark"
    }

    var colorScheme: ColorScheme {
        return .dark
    }
}

// MARK: - Glassmorphic Theme Colors
struct ThemeColors {
    let primary: Color
    let secondary: Color
    let accent: Color
    let background: LinearGradient
    let surface: LinearGradient
    let cardBackground: LinearGradient
    let onPrimary: Color
    let onSecondary: Color
    let onBackground: Color
    let onSurface: Color
    let success: Color
    let warning: Color
    let error: Color
    let glassMaterial: Material



    // Beautiful Dark Theme with Glassmorphic Design
    static let dark = ThemeColors(
        primary: Color(red: 0.3, green: 0.2, blue: 0.6),
        secondary: Color(red: 0.2, green: 0.3, blue: 0.7),
        accent: Color(red: 1.0, green: 0.84, blue: 0.0), // Warren Buffett Gold
        background: LinearGradient(
            colors: [
                Color(red: 0.05, green: 0.05, blue: 0.15),
                Color(red: 0.1, green: 0.05, blue: 0.2),
                Color(red: 0.05, green: 0.1, blue: 0.25),
                Color(red: 0.02, green: 0.02, blue: 0.1)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        surface: LinearGradient(
            colors: [
                Color.white.opacity(0.15),
                Color.white.opacity(0.1),
                Color(red: 0.3, green: 0.2, blue: 0.6).opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        cardBackground: LinearGradient(
            colors: [
                Color.white.opacity(0.2),
                Color.white.opacity(0.1),
                Color(red: 0.2, green: 0.3, blue: 0.7).opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        ),
        onPrimary: Color.white,
        onSecondary: Color.white,
        onBackground: Color.white,
        onSurface: Color.white.opacity(0.9),
        success: Color(red: 0.3, green: 0.9, blue: 0.5),
        warning: Color(red: 1.0, green: 0.7, blue: 0.2),
        error: Color(red: 1.0, green: 0.3, blue: 0.4),
        glassMaterial: .ultraThinMaterial
    )
}

// MARK: - Glassmorphic Card Styles (Dark Mode Only)
struct GlassmorphicCard: ViewModifier {
    let theme: ThemeColors
    let cornerRadius: CGFloat
    let shadowRadius: CGFloat

    init(theme: ThemeColors, cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) {
        self.theme = theme
        self.cornerRadius = cornerRadius
        self.shadowRadius = shadowRadius
    }

    func body(content: Content) -> some View {
        content
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(theme.cardBackground)
                    .background(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(theme.glassMaterial)
                    )
                    .shadow(
                        color: Color.black.opacity(0.3),
                        radius: shadowRadius,
                        x: 0,
                        y: 4
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.3),
                                Color.white.opacity(0.15),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
    }
}

extension View {
    func glassmorphicCard(theme: ThemeColors, cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 10) -> some View {
        modifier(GlassmorphicCard(theme: theme, cornerRadius: cornerRadius, shadowRadius: shadowRadius))
    }
}



// MARK: - Theme Environment Key
struct ThemeEnvironmentKey: EnvironmentKey {
    static let defaultValue: ThemeColors = ThemeColors.dark
}

extension EnvironmentValues {
    var theme: ThemeColors {
        get { self[ThemeEnvironmentKey.self] }
        set { self[ThemeEnvironmentKey.self] = newValue }
    }
}

// MARK: - Theme Modifier (Dark Mode Only)
struct ThemedView: ViewModifier {
    @ObservedObject var themeManager: ThemeManager

    func body(content: Content) -> some View {
        content
            .environment(\.theme, ThemeColors.dark)
            .preferredColorScheme(.dark)
    }
}

extension View {
    func themed(_ themeManager: ThemeManager) -> some View {
        modifier(ThemedView(themeManager: themeManager))
    }
}
