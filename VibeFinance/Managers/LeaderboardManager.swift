//
//  LeaderboardManager.swift
//  VibeFinance - Leaderboard Data Management
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI
import Foundation

@MainActor
class LeaderboardManager: ObservableObject {
    @Published var allUsers: [LeaderboardUser] = []
    @Published var competitions: [Competition] = []
    @Published var achievements: [LeaderboardAchievement] = []
    @Published var currentUser: LeaderboardUser?
    @Published var isLoading = false
    
    var topUsers: [LeaderboardUser] {
        Array(allUsers.prefix(10))
    }
    
    init() {
        loadMockData()
    }
    
    private func loadMockData() {
        // Mock Users Data
        allUsers = [
            LeaderboardUser(
                id: "1",
                name: "<PERSON>",
                username: "alexfinance",
                avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
                points: 2850,
                weeklyPoints: 450,
                monthlyPoints: 1200,
                rank: 1,
                previousRank: 3,
                badges: ["streak-master", "top-saver"],
                level: 12,
                streak: 28
            ),
            LeaderboardUser(
                id: "2",
                name: "<PERSON>",
                username: "may<PERSON><PERSON>",
                avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face",
                points: 2720,
                weeklyPoints: 380,
                monthlyPoints: 1150,
                rank: 2,
                previousRank: 1,
                badges: ["budget-boss", "investment-pro"],
                level: 11,
                streak: 21
            ),
            LeaderboardUser(
                id: "3",
                name: "Jordan Kim",
                username: "jordansaves",
                avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face",
                points: 2650,
                weeklyPoints: 420,
                monthlyPoints: 1100,
                rank: 3,
                previousRank: 2,
                badges: ["goal-crusher"],
                level: 10,
                streak: 15
            ),
            LeaderboardUser(
                id: "4",
                name: "Sam Rivera",
                username: "samspends",
                avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
                points: 2400,
                weeklyPoints: 320,
                monthlyPoints: 980,
                rank: 4,
                previousRank: 5,
                badges: ["consistent-saver"],
                level: 9,
                streak: 12
            ),
            LeaderboardUser(
                id: "5",
                name: "Riley Thompson",
                username: "rileyrocks",
                avatar: "https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=150&h=150&fit=crop&crop=face",
                points: 2200,
                weeklyPoints: 280,
                monthlyPoints: 850,
                rank: 5,
                previousRank: 4,
                badges: ["newbie-achiever"],
                level: 8,
                streak: 8
            ),
            LeaderboardUser(
                id: "6",
                name: "Casey Wong",
                username: "caseycash",
                avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
                points: 2100,
                weeklyPoints: 260,
                monthlyPoints: 800,
                rank: 6,
                previousRank: 6,
                badges: ["early-bird"],
                level: 7,
                streak: 5
            ),
            LeaderboardUser(
                id: "7",
                name: "Taylor Swift",
                username: "taylortrader",
                avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
                points: 1950,
                weeklyPoints: 240,
                monthlyPoints: 750,
                rank: 7,
                previousRank: 8,
                badges: ["social-butterfly"],
                level: 6,
                streak: 3
            ),
            LeaderboardUser(
                id: "8",
                name: "Morgan Lee",
                username: "morganmoney",
                avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
                points: 1800,
                weeklyPoints: 220,
                monthlyPoints: 700,
                rank: 8,
                previousRank: 7,
                badges: ["risk-taker"],
                level: 5,
                streak: 1
            )
        ]
        
        // Mock Competitions Data
        competitions = [
            Competition(
                id: "1",
                title: "Weekly Savings Challenge",
                description: "Save the most money this week and win! 💰",
                type: .weekly,
                startDate: Date(),
                endDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date(),
                participants: 1247,
                prize: "$100 Cash Prize",
                isActive: true
            ),
            Competition(
                id: "2",
                title: "Monthly Investment Marathon",
                description: "Best investment portfolio performance wins 📈",
                type: .monthly,
                startDate: Date(),
                endDate: Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date(),
                participants: 856,
                prize: "$500 Investment Bonus",
                isActive: true
            ),
            Competition(
                id: "3",
                title: "Streak Master Challenge",
                description: "Longest daily check-in streak wins! 🔥",
                type: .weekly,
                startDate: Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date(),
                endDate: Calendar.current.date(byAdding: .day, value: 4, to: Date()) ?? Date(),
                participants: 2103,
                prize: "Premium Theme Unlock",
                isActive: true
            )
        ]
        
        // Mock Achievements Data
        achievements = [
            LeaderboardAchievement(
                id: "1",
                title: "Streak Master",
                description: "Complete 30 days in a row",
                icon: "flame.fill",
                rarity: .epic,
                unlockedAt: Date(),
                progress: 28,
                maxProgress: 30
            ),
            LeaderboardAchievement(
                id: "2",
                title: "Budget Boss",
                description: "Stay under budget for 3 months",
                icon: "target",
                rarity: .rare,
                unlockedAt: Date(),
                progress: nil,
                maxProgress: nil
            ),
            LeaderboardAchievement(
                id: "3",
                title: "Investment Pro",
                description: "Make your first investment",
                icon: "chart.line.uptrend.xyaxis",
                rarity: .common,
                unlockedAt: nil,
                progress: 1,
                maxProgress: 1
            ),
            LeaderboardAchievement(
                id: "4",
                title: "Social Butterfly",
                description: "Join 5 investment squads",
                icon: "person.3.fill",
                rarity: .rare,
                unlockedAt: nil,
                progress: 2,
                maxProgress: 5
            ),
            LeaderboardAchievement(
                id: "5",
                title: "Diamond Hands",
                description: "Hold an investment for 1 year",
                icon: "diamond.fill",
                rarity: .legendary,
                unlockedAt: nil,
                progress: 45,
                maxProgress: 365
            ),
            LeaderboardAchievement(
                id: "6",
                title: "Goal Crusher",
                description: "Complete 10 financial goals",
                icon: "flag.checkered",
                rarity: .epic,
                unlockedAt: nil,
                progress: 7,
                maxProgress: 10
            ),
            LeaderboardAchievement(
                id: "7",
                title: "Early Bird",
                description: "Check app before 7 AM for 7 days",
                icon: "sunrise.fill",
                rarity: .common,
                unlockedAt: Date(),
                progress: nil,
                maxProgress: nil
            ),
            LeaderboardAchievement(
                id: "8",
                title: "Millionaire Mindset",
                description: "Reach $1M portfolio value",
                icon: "crown.fill",
                rarity: .legendary,
                unlockedAt: nil,
                progress: 125000,
                maxProgress: 1000000
            )
        ]
        
        // Set current user (for demo purposes)
        currentUser = allUsers.first { $0.id == "4" }
    }
    
    // MARK: - Public Methods
    func refreshLeaderboard() async {
        isLoading = true
        
        // Simulate API call
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        
        // In real app, this would fetch from Supabase
        await MainActor.run {
            loadMockData()
            isLoading = false
        }
    }
    
    func joinCompetition(_ competitionId: String) async -> Bool {
        // Simulate joining competition
        if let index = competitions.firstIndex(where: { $0.id == competitionId }) {
            competitions[index] = Competition(
                id: competitions[index].id,
                title: competitions[index].title,
                description: competitions[index].description,
                type: competitions[index].type,
                startDate: competitions[index].startDate,
                endDate: competitions[index].endDate,
                participants: competitions[index].participants + 1,
                prize: competitions[index].prize,
                isActive: competitions[index].isActive
            )
            return true
        }
        return false
    }
    
    func updateUserProgress(userId: String, points: Int) {
        if let index = allUsers.firstIndex(where: { $0.id == userId }) {
            let user = allUsers[index]
            allUsers[index] = LeaderboardUser(
                id: user.id,
                name: user.name,
                username: user.username,
                avatar: user.avatar,
                points: user.points + points,
                weeklyPoints: user.weeklyPoints + points,
                monthlyPoints: user.monthlyPoints + points,
                rank: user.rank,
                previousRank: user.previousRank,
                badges: user.badges,
                level: user.level,
                streak: user.streak
            )
            
            // Recalculate rankings
            recalculateRankings()
        }
    }
    
    private func recalculateRankings() {
        allUsers.sort { $0.points > $1.points }
        for (index, user) in allUsers.enumerated() {
            allUsers[index] = LeaderboardUser(
                id: user.id,
                name: user.name,
                username: user.username,
                avatar: user.avatar,
                points: user.points,
                weeklyPoints: user.weeklyPoints,
                monthlyPoints: user.monthlyPoints,
                rank: index + 1,
                previousRank: user.rank,
                badges: user.badges,
                level: user.level,
                streak: user.streak
            )
        }
    }
}
