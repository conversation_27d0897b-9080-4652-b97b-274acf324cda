//
//  SquadDetailManager.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI
import Combine

@MainActor
class SquadDetailManager: ObservableObject {
    @Published var squadRank: Int = 0
    @Published var recentActivities: [SquadActivity] = []
    @Published var investmentProposals: [SquadInvestmentProposal] = []
    @Published var squadAnalytics: SquadAnalytics?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    func loadSquadDetails(_ squadID: UUID) async {
        isLoading = true
        errorMessage = nil
        
        await loadSquadRank(squadID)
        await loadRecentActivities(squadID)
        await loadInvestmentProposals(squadID)
        await loadSquadAnalytics(squadID)

        isLoading = false
    }
    
    private func loadSquadRank(_ squadID: UUID) async {
        // Mock squad ranking
        squadRank = Int.random(in: 1...50)
    }
    
    private func loadRecentActivities(_ squadID: UUID) async {
        // Generate mock recent activities
        recentActivities = generateRecentActivities()
    }
    
    private func loadInvestmentProposals(_ squadID: UUID) async {
        // Generate mock investment proposals
        investmentProposals = generateInvestmentProposals()
    }
    
    private func loadSquadAnalytics(_ squadID: UUID) async {
        // Generate mock analytics
        squadAnalytics = generateSquadAnalytics()
    }
    
    // MARK: - Mock Data Generation
    private func generateRecentActivities() -> [SquadActivity] {
        return [
            SquadActivity(
                id: UUID(),
                type: .memberJoined,
                user: "Alex Chen",
                description: "joined the squad",
                timestamp: Date().addingTimeInterval(-300),
                icon: "person.badge.plus"
            ),
            SquadActivity(
                id: UUID(),
                type: .investmentProposed,
                user: "Sarah Kim",
                description: "proposed investing in TSLA",
                timestamp: Date().addingTimeInterval(-600),
                icon: "lightbulb.fill"
            ),
            SquadActivity(
                id: UUID(),
                type: .investmentExecuted,
                user: "Squad",
                description: "invested $2,500 in AAPL",
                timestamp: Date().addingTimeInterval(-900),
                icon: "checkmark.circle.fill"
            ),
            SquadActivity(
                id: UUID(),
                type: .messagePosted,
                user: "Mike Johnson",
                description: "posted in squad chat",
                timestamp: Date().addingTimeInterval(-1200),
                icon: "message.fill"
            ),
            SquadActivity(
                id: UUID(),
                type: .achievementEarned,
                user: "Squad",
                description: "earned 'Diversified Portfolio' achievement",
                timestamp: Date().addingTimeInterval(-1800),
                icon: "trophy.fill"
            )
        ]
    }
    
    private func generateInvestmentProposals() -> [SquadInvestmentProposal] {
        return [
            SquadInvestmentProposal(
                id: UUID(),
                squadID: UUID(),
                symbol: "NVDA",
                companyName: "NVIDIA Corporation",
                proposedAmount: 1500,
                proposedShares: 15,
                currentPrice: 100.0,
                proposer: "Sarah Kim",
                description: "AI and gaming growth potential",
                votesFor: 8,
                votesAgainst: 2,
                status: .voting,
                deadline: Date().addingTimeInterval(172800), // 2 days
                createdAt: Date().addingTimeInterval(-86400)
            ),
            SquadInvestmentProposal(
                id: UUID(),
                squadID: UUID(),
                symbol: "MSFT",
                companyName: "Microsoft Corporation",
                proposedAmount: 2000,
                proposedShares: 20,
                currentPrice: 100.0,
                proposer: "Alex Chen",
                description: "Cloud computing and AI leadership",
                votesFor: 12,
                votesAgainst: 1,
                status: .approved,
                deadline: Date().addingTimeInterval(-3600),
                createdAt: Date().addingTimeInterval(-172800)
            ),
            SquadInvestmentProposal(
                id: UUID(),
                squadID: UUID(),
                symbol: "AMZN",
                companyName: "Amazon.com Inc.",
                proposedAmount: 1800,
                proposedShares: 18,
                currentPrice: 100.0,
                proposer: "Emma Davis",
                description: "E-commerce and AWS growth",
                votesFor: 5,
                votesAgainst: 8,
                status: .rejected,
                deadline: Date().addingTimeInterval(-7200),
                createdAt: Date().addingTimeInterval(-259200)
            )
        ]
    }
    
    private func generateSquadAnalytics() -> SquadAnalytics {
        return SquadAnalytics(
            totalReturn: 12.5,
            monthlyReturn: 3.2,
            weeklyReturn: 1.8,
            dailyReturn: 0.5,
            sharpeRatio: 1.45,
            volatility: 15.2,
            maxDrawdown: -8.3,
            winRate: 68.5,
            averageHoldingPeriod: 45,
            topPerformingStock: "AAPL",
            worstPerformingStock: "META",
            sectorAllocation: [
                SectorAllocation(sector: "Technology", value: 4500.0, percentage: 45.0, performance: 12.5),
                SectorAllocation(sector: "Healthcare", value: 2000.0, percentage: 20.0, performance: 8.3),
                SectorAllocation(sector: "Finance", value: 1500.0, percentage: 15.0, performance: 5.7),
                SectorAllocation(sector: "Consumer", value: 1200.0, percentage: 12.0, performance: 3.2),
                SectorAllocation(sector: "Energy", value: 800.0, percentage: 8.0, performance: -2.1)
            ],
            performanceHistory: generatePerformanceHistory()
        )
    }
    
    private func generatePerformanceHistory() -> [PerformanceDataPoint] {
        var history: [PerformanceDataPoint] = []
        let calendar = Calendar.current
        let startDate = calendar.date(byAdding: .day, value: -30, to: Date()) ?? Date()
        
        var currentValue = 10000.0
        
        for i in 0...30 {
            let date = calendar.date(byAdding: .day, value: i, to: startDate) ?? Date()
            let change = Double.random(in: -2.0...3.0) / 100.0
            currentValue *= (1 + change)
            
            history.append(PerformanceDataPoint(
                date: date,
                portfolioValue: currentValue,
                dayChange: change * currentValue,
                dayChangePercent: change * 100
            ))
        }
        
        return history
    }
}

// MARK: - Supporting Models
struct SquadActivity: Identifiable {
    let id: UUID
    let type: SquadActivityType
    let user: String
    let description: String
    let timestamp: Date
    let icon: String
}

enum SquadActivityType {
    case memberJoined, memberLeft, investmentProposed, investmentExecuted
    case messagePosted, achievementEarned, rankChanged
}

struct SquadInvestmentProposal: Identifiable {
    let id: UUID
    let squadID: UUID
    let symbol: String
    let companyName: String
    let proposedAmount: Double
    let proposedShares: Double
    let currentPrice: Double
    let proposer: String
    let description: String
    let votesFor: Int
    let votesAgainst: Int
    let status: ProposalStatus
    let deadline: Date
    let createdAt: Date
    
    var totalVotes: Int {
        votesFor + votesAgainst
    }
    
    var approvalPercentage: Double {
        guard totalVotes > 0 else { return 0 }
        return Double(votesFor) / Double(totalVotes) * 100
    }
    
    var timeRemaining: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: deadline, relativeTo: Date())
    }
}

enum ProposalStatus: String, CaseIterable {
    case voting = "voting"
    case approved = "approved"
    case rejected = "rejected"
    case executed = "executed"
    
    var color: Color {
        switch self {
        case .voting: return .orange
        case .approved: return .green
        case .rejected: return .red
        case .executed: return .blue
        }
    }
    
    var displayName: String {
        switch self {
        case .voting: return "Voting"
        case .approved: return "Approved"
        case .rejected: return "Rejected"
        case .executed: return "Executed"
        }
    }
}

struct SquadAnalytics {
    let totalReturn: Double
    let monthlyReturn: Double
    let weeklyReturn: Double
    let dailyReturn: Double
    let sharpeRatio: Double
    let volatility: Double
    let maxDrawdown: Double
    let winRate: Double
    let averageHoldingPeriod: Int
    let topPerformingStock: String
    let worstPerformingStock: String
    let sectorAllocation: [SectorAllocation]
    let performanceHistory: [PerformanceDataPoint]
}


