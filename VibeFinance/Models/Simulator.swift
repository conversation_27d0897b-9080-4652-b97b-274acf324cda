//
//  Simulator.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Virtual Portfolio
struct VirtualPortfolio: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    var name: String
    var balance: Double // Virtual cash
    var totalValue: Double // Total portfolio value
    var totalInvested: Double // Total amount invested
    var positions: [VirtualPosition]
    var transactions: [VirtualTransaction]
    let createdAt: Date
    var updatedAt: Date
    
    init(userID: UUID, name: String = "My Portfolio", startingBalance: Double = 10000.0) {
        self.id = UUID()
        self.userID = userID
        self.name = name
        self.balance = startingBalance
        self.totalValue = startingBalance
        self.totalInvested = 0.0
        self.positions = []
        self.transactions = []
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Virtual Position
struct VirtualPosition: Codable, Identifiable {
    let id: UUID
    let symbol: String
    let name: String
    var shares: Double
    let averageCost: Double
    var currentPrice: Double
    let purchaseDate: Date
    var lastUpdated: Date
    
    init(symbol: String, name: String, shares: Double, averageCost: Double, currentPrice: Double? = nil) {
        self.id = UUID()
        self.symbol = symbol
        self.name = name
        self.shares = shares
        self.averageCost = averageCost
        self.currentPrice = currentPrice ?? averageCost
        self.purchaseDate = Date()
        self.lastUpdated = Date()
    }
}

// MARK: - Virtual Transaction
struct VirtualTransaction: Codable, Identifiable {
    let id: UUID
    let portfolioID: UUID
    let type: TransactionType
    let symbol: String
    let name: String
    let shares: Double
    let price: Double
    let totalAmount: Double
    let fee: Double
    let createdAt: Date
    
    init(portfolioID: UUID, type: TransactionType, symbol: String, name: String, shares: Double, price: Double, fee: Double = 0.0) {
        self.id = UUID()
        self.portfolioID = portfolioID
        self.type = type
        self.symbol = symbol
        self.name = name
        self.shares = shares
        self.price = price
        self.totalAmount = shares * price + fee
        self.fee = fee
        self.createdAt = Date()
    }
}

// MARK: - Stock Data (using DataModels.StockPrice)
// MarketData is now defined in DataModels.swift

// MARK: - Watchlist
struct Watchlist: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    var name: String
    var symbols: [String]
    let createdAt: Date
    var updatedAt: Date
    
    init(userID: UUID, name: String = "My Watchlist") {
        self.id = UUID()
        self.userID = userID
        self.name = name
        self.symbols = []
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Transaction Type
enum TransactionType: String, CaseIterable, Codable {
    case buy = "buy"
    case sell = "sell"
    case dividend = "dividend"
    case split = "split"
    
    var displayName: String {
        switch self {
        case .buy: return "Buy"
        case .sell: return "Sell"
        case .dividend: return "Dividend"
        case .split: return "Stock Split"
        }
    }
    
    var emoji: String {
        switch self {
        case .buy: return "📈"
        case .sell: return "📉"
        case .dividend: return "💰"
        case .split: return "🔄"
        }
    }
    
    var color: String {
        switch self {
        case .buy: return "green"
        case .sell: return "red"
        case .dividend: return "blue"
        case .split: return "orange"
        }
    }
}

// Using PortfolioPerformance from AppleIntelligenceManager.swift

// MARK: - Portfolio Extensions
extension VirtualPortfolio {
    var totalReturn: Double {
        return totalValue - totalInvested
    }
    
    var totalReturnPercent: Double {
        guard totalInvested > 0 else { return 0.0 }
        return (totalReturn / totalInvested) * 100
    }
    
    var isPositive: Bool {
        return totalReturn >= 0
    }
    
    mutating func addPosition(_ position: VirtualPosition) {
        // Check if position already exists
        if let index = positions.firstIndex(where: { $0.symbol == position.symbol }) {
            // Update existing position (average cost calculation)
            let existingPosition = positions[index]
            let totalShares = existingPosition.shares + position.shares
            let totalCost = (existingPosition.shares * existingPosition.averageCost) + (position.shares * position.averageCost)
            let newAverageCost = totalCost / totalShares
            
            positions[index] = VirtualPosition(
                symbol: position.symbol,
                name: position.name,
                shares: totalShares,
                averageCost: newAverageCost,
                currentPrice: position.currentPrice
            )
        } else {
            positions.append(position)
        }
        
        balance -= position.shares * position.averageCost
        totalInvested += position.shares * position.averageCost
        updatedAt = Date()
    }
    
    mutating func removePosition(_ symbol: String, shares: Double) {
        guard let index = positions.firstIndex(where: { $0.symbol == symbol }) else { return }
        
        let position = positions[index]
        if position.shares <= shares {
            // Remove entire position
            balance += position.shares * position.currentPrice
            positions.remove(at: index)
        } else {
            // Reduce position
            positions[index] = VirtualPosition(
                symbol: position.symbol,
                name: position.name,
                shares: position.shares - shares,
                averageCost: position.averageCost,
                currentPrice: position.currentPrice
            )
            balance += shares * position.currentPrice
        }
        
        updatedAt = Date()
    }
    
    mutating func updatePrices(_ stockPrices: [StockPrice]) {
        for (index, position) in positions.enumerated() {
            if let stockPrice = stockPrices.first(where: { $0.symbol == position.symbol }) {
                positions[index].currentPrice = stockPrice.price
                positions[index].lastUpdated = Date()
            }
        }

        // Recalculate total value
        let positionsValue = positions.reduce(0) { $0 + ($1.shares * $1.currentPrice) }
        totalValue = balance + positionsValue
        updatedAt = Date()
    }
    
    var diversificationScore: Double {
        guard !positions.isEmpty else { return 0.0 }
        
        let totalPositionValue = positions.reduce(0) { $0 + ($1.shares * $1.currentPrice) }
        guard totalPositionValue > 0 else { return 0.0 }
        
        // Calculate concentration (higher concentration = lower diversification)
        let concentrations = positions.map { ($0.shares * $0.currentPrice) / totalPositionValue }
        let maxConcentration = concentrations.max() ?? 0.0
        
        // Score based on number of positions and concentration
        let positionScore = min(Double(positions.count) / 10.0, 1.0) * 50 // Max 50 points for 10+ positions
        let concentrationScore = (1.0 - maxConcentration) * 50 // Max 50 points for low concentration
        
        return positionScore + concentrationScore
    }
}

extension VirtualPosition {
    var currentValue: Double {
        return shares * currentPrice
    }
    
    var totalReturn: Double {
        return currentValue - (shares * averageCost)
    }
    
    var totalReturnPercent: Double {
        let invested = shares * averageCost
        guard invested > 0 else { return 0.0 }
        return (totalReturn / invested) * 100
    }
    
    var isPositive: Bool {
        return totalReturn >= 0
    }
    
    var dayChange: Double {
        // This would need historical data to calculate properly
        // For now, return a simple calculation based on current vs average
        return currentPrice - averageCost
    }
    
    var dayChangePercent: Double {
        guard averageCost > 0 else { return 0.0 }
        return (dayChange / averageCost) * 100
    }
}

// MarketData extensions moved to DataModels.swift
