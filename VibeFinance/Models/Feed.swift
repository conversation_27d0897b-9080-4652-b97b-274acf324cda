//
//  Feed.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Feed Item
struct FeedItem: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    let content: FeedContent
    let createdAt: Date
    var isRead: Bool
    var isBookmarked: Bool
    var reactions: [Reaction]
    
    init(userID: UUID, content: FeedContent) {
        self.id = UUID()
        self.userID = userID
        self.content = content
        self.createdAt = Date()
        self.isRead = false
        self.isBookmarked = false
        self.reactions = []
    }
}

// MARK: - Feed Content
struct FeedContent: Codable {
    let summary: String
    let tags: [String]
    let investmentSuggestion: InvestmentSuggestion?
    let citation: Citation
    let category: FeedCategory
    let readingTime: Int // in minutes
    let difficulty: DifficultyLevel
    let vibeScore: Int // 1-100, how "viby" this content is
    
    init(summary: String, tags: [String], investmentSuggestion: InvestmentSuggestion? = nil, citation: Citation, category: FeedCategory, readingTime: Int = 2, difficulty: DifficultyLevel = .beginner, vibeScore: Int = 75) {
        self.summary = summary
        self.tags = tags
        self.investmentSuggestion = investmentSuggestion
        self.citation = citation
        self.category = category
        self.readingTime = readingTime
        self.difficulty = difficulty
        self.vibeScore = vibeScore
    }
}

// MARK: - Investment Suggestion
struct InvestmentSuggestion: Codable {
    let amount: Double // e.g., 5.0 for $5
    let asset: String // e.g., "Gaming ETF"
    let symbol: String // e.g., "GAMR"
    let reasoning: String
    let riskLevel: RiskTolerance
    let potentialReturn: String // e.g., "5-15% annually"
    
    var formattedAmount: String {
        return String(format: "$%.0f", amount)
    }
}

// MARK: - Citation
struct Citation: Codable {
    let source: String // e.g., "X", "NewsAPI"
    let author: String? // e.g., "@GameInvestor"
    let url: String?
    let publishedAt: Date?
    
    var displayText: String {
        if let author = author {
            return "Source: \(source) \(author)"
        } else {
            return "Source: \(source)"
        }
    }
}

// MARK: - Feed Category
enum FeedCategory: String, CaseIterable, Codable {
    case stocks = "stocks"
    case crypto = "crypto"
    case etfs = "etfs"
    case news = "news"
    case education = "education"
    case trends = "trends"
    case gaming = "gaming"
    case sustainability = "sustainability"
    case tech = "tech"
    case lifestyle = "lifestyle"
    
    var displayName: String {
        switch self {
        case .stocks: return "Stocks 📈"
        case .crypto: return "Crypto 🪙"
        case .etfs: return "ETFs 📊"
        case .news: return "News 📰"
        case .education: return "Learn 🎓"
        case .trends: return "Trends 🔥"
        case .gaming: return "Gaming 🎮"
        case .sustainability: return "Green 🌱"
        case .tech: return "Tech 💻"
        case .lifestyle: return "Lifestyle ✨"
        }
    }
    
    var color: String {
        switch self {
        case .stocks: return "green"
        case .crypto: return "orange"
        case .etfs: return "blue"
        case .news: return "red"
        case .education: return "purple"
        case .trends: return "pink"
        case .gaming: return "cyan"
        case .sustainability: return "mint"
        case .tech: return "indigo"
        case .lifestyle: return "yellow"
        }
    }
}

// MARK: - Difficulty Level
enum DifficultyLevel: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner 🌱"
        case .intermediate: return "Intermediate 🚀"
        case .advanced: return "Advanced 🧠"
        }
    }
    
    var emoji: String {
        switch self {
        case .beginner: return "🌱"
        case .intermediate: return "🚀"
        case .advanced: return "🧠"
        }
    }
}

// MARK: - Reaction
struct Reaction: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    let type: ReactionType
    let createdAt: Date
    
    init(userID: UUID, type: ReactionType) {
        self.id = UUID()
        self.userID = userID
        self.type = type
        self.createdAt = Date()
    }
}

// MARK: - Reaction Type
enum ReactionType: String, CaseIterable, Codable {
    case fire = "fire"
    case rocket = "rocket"
    case gem = "gem"
    case brain = "brain"
    case money = "money"
    
    var emoji: String {
        switch self {
        case .fire: return "🔥"
        case .rocket: return "🚀"
        case .gem: return "💎"
        case .brain: return "🧠"
        case .money: return "💰"
        }
    }
}

// MARK: - Feed Extensions
extension FeedItem {
    var timeAgo: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: createdAt, relativeTo: Date())
    }
    
    mutating func addReaction(_ reaction: Reaction) {
        // Remove existing reaction from same user
        reactions.removeAll { $0.userID == reaction.userID }
        // Add new reaction
        reactions.append(reaction)
    }
    
    func getReactionCount(for type: ReactionType) -> Int {
        return reactions.filter { $0.type == type }.count
    }
    
    var totalReactions: Int {
        return reactions.count
    }
    
    var isPopular: Bool {
        return totalReactions >= 10 || content.vibeScore >= 85
    }
}

// MARK: - Feed Query
struct FeedQuery {
    let interests: [String]
    let goals: [String]
    let categories: [FeedCategory]
    let difficulty: DifficultyLevel?
    let limit: Int
    
    init(interests: [String] = [], goals: [String] = [], categories: [FeedCategory] = [], difficulty: DifficultyLevel? = nil, limit: Int = 10) {
        self.interests = interests
        self.goals = goals
        self.categories = categories
        self.difficulty = difficulty
        self.limit = limit
    }
    
    var queryString: String {
        var components: [String] = []
        
        if !interests.isEmpty {
            let interestQuery = interests.joined(separator: " OR ")
            components.append("(\(interestQuery))")
        }
        
        if !goals.isEmpty {
            let goalQuery = goals.joined(separator: " OR ")
            components.append("(\(goalQuery))")
        }
        
        // Always include finance keywords
        components.append("(finance OR investing OR money OR stocks)")
        
        return components.joined(separator: " AND ")
    }
}
