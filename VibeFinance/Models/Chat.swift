//
//  Chat.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Chat Session
struct ChatSession: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    var title: String
    var messages: [ChatMessage]
    let createdAt: Date
    var updatedAt: Date
    var isActive: Bool
    
    init(userID: UUID, title: String = "New Chat") {
        self.id = UUID()
        self.userID = userID
        self.title = title
        self.messages = []
        self.createdAt = Date()
        self.updatedAt = Date()
        self.isActive = true
    }
}

// MARK: - Chat Message
struct ChatMessage: Codable, Identifiable {
    let id: UUID
    let sessionID: UUID
    let content: String
    let type: MessageType
    let sender: MessageSender
    let timestamp: Date
    var isRead: Bool
    var reactions: [MessageReaction]
    let metadata: MessageMetadata?
    
    init(sessionID: UUID, content: String, type: MessageType, sender: MessageSender, metadata: MessageMetadata? = nil) {
        self.id = UUID()
        self.sessionID = sessionID
        self.content = content
        self.type = type
        self.sender = sender
        self.timestamp = Date()
        self.isRead = false
        self.reactions = []
        self.metadata = metadata
    }

    // Simple initializer for compatibility
    init(id: UUID = UUID(), content: String, isFromUser: Bool, timestamp: Date = Date()) {
        self.id = id
        self.sessionID = UUID() // Default session
        self.content = content
        self.type = .text
        self.sender = isFromUser ? .user : .assistant
        self.timestamp = timestamp
        self.isRead = false
        self.reactions = []
        self.metadata = nil
    }
}

// MARK: - Message Type
enum MessageType: String, CaseIterable, Codable {
    case text = "text"
    case voice = "voice"
    case image = "image"
    case suggestion = "suggestion"
    case quickReply = "quick_reply"
    case investment = "investment"
    case quest = "quest"
    case educational = "educational"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .text: return "Text"
        case .voice: return "Voice"
        case .image: return "Image"
        case .suggestion: return "Suggestion"
        case .quickReply: return "Quick Reply"
        case .investment: return "Investment"
        case .quest: return "Quest"
        case .educational: return "Educational"
        case .system: return "System"
        }
    }
    
    var emoji: String {
        switch self {
        case .text: return "💬"
        case .voice: return "🎤"
        case .image: return "🖼️"
        case .suggestion: return "💡"
        case .quickReply: return "⚡"
        case .investment: return "📈"
        case .quest: return "🎯"
        case .educational: return "📚"
        case .system: return "⚙️"
        }
    }
}

// MARK: - Message Sender
enum MessageSender: String, CaseIterable, Codable {
    case user = "user"
    case assistant = "assistant"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .user: return "You"
        case .assistant: return "Vibe Buddy"
        case .system: return "System"
        }
    }
    
    var avatar: String {
        switch self {
        case .user: return "person.circle.fill"
        case .assistant: return "sparkles.rectangle.stack.fill"
        case .system: return "gear.circle.fill"
        }
    }
}

// MARK: - Message Metadata
struct MessageMetadata: Codable {
    let voiceURL: String?
    let voiceDuration: Double?
    let imageURL: String?
    let suggestions: [String]?
    let investmentData: InvestmentMessageData?
    let questData: QuestMessageData?
    let educationalData: EducationalMessageData?
    let quickReplies: [QuickReply]?
    
    init(voiceURL: String? = nil, voiceDuration: Double? = nil, imageURL: String? = nil, suggestions: [String]? = nil, investmentData: InvestmentMessageData? = nil, questData: QuestMessageData? = nil, educationalData: EducationalMessageData? = nil, quickReplies: [QuickReply]? = nil) {
        self.voiceURL = voiceURL
        self.voiceDuration = voiceDuration
        self.imageURL = imageURL
        self.suggestions = suggestions
        self.investmentData = investmentData
        self.questData = questData
        self.educationalData = educationalData
        self.quickReplies = quickReplies
    }
}

// MARK: - Investment Message Data
struct InvestmentMessageData: Codable {
    let symbol: String
    let name: String
    let price: Double
    let change: Double
    let changePercent: Double
    let recommendation: String
    let reasoning: String
    
    var formattedPrice: String {
        return String(format: "$%.2f", price)
    }
    
    var formattedChange: String {
        let sign = change >= 0 ? "+" : ""
        return String(format: "%@$%.2f (%.2f%%)", sign, change, changePercent)
    }
    
    var isPositive: Bool {
        return change >= 0
    }
}

// MARK: - Quest Message Data
struct QuestMessageData: Codable {
    let questID: UUID
    let title: String
    let description: String
    let xpReward: Int
    let estimatedTime: Int
    let category: String
    let difficulty: String
    
    var formattedTime: String {
        return "\(estimatedTime) min"
    }
    
    var formattedXP: String {
        return "+\(xpReward) XP"
    }
}

// MARK: - Educational Message Data
struct EducationalMessageData: Codable {
    let title: String
    let summary: String
    let keyPoints: [String]
    let readingTime: Int
    let difficulty: String
    let category: String
    
    var formattedReadingTime: String {
        return "\(readingTime) min read"
    }
}

// MARK: - Quick Reply
struct QuickReply: Codable, Identifiable {
    let id: UUID
    let text: String
    let action: QuickReplyAction
    
    init(text: String, action: QuickReplyAction) {
        self.id = UUID()
        self.text = text
        self.action = action
    }
}

// MARK: - Quick Reply Action
enum QuickReplyAction: String, CaseIterable, Codable {
    case sendMessage = "send_message"
    case openQuest = "open_quest"
    case openSimulator = "open_simulator"
    case openFeed = "open_feed"
    case openSquads = "open_squads"
    case showInvestment = "show_investment"
    case explainConcept = "explain_concept"
    case getAdvice = "get_advice"
}

// MARK: - Message Reaction
struct MessageReaction: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    let emoji: String
    let timestamp: Date
    
    init(userID: UUID, emoji: String) {
        self.id = UUID()
        self.userID = userID
        self.emoji = emoji
        self.timestamp = Date()
    }
}

// MARK: - Chat Context
struct ChatContext: Codable {
    let userPreferences: UserPreferences
    let currentPortfolio: VirtualPortfolio?
    let recentQuests: [Quest]
    let currentLevel: Int
    let subscriptionTier: SubscriptionTier
    let recentFeedItems: [FeedItem]
    
    init(userPreferences: UserPreferences, currentPortfolio: VirtualPortfolio? = nil, recentQuests: [Quest] = [], currentLevel: Int = 1, subscriptionTier: SubscriptionTier = .free, recentFeedItems: [FeedItem] = []) {
        self.userPreferences = userPreferences
        self.currentPortfolio = currentPortfolio
        self.recentQuests = recentQuests
        self.currentLevel = currentLevel
        self.subscriptionTier = subscriptionTier
        self.recentFeedItems = recentFeedItems
    }
}

// MARK: - Chat Extensions
extension ChatSession {
    var lastMessage: ChatMessage? {
        return messages.last
    }
    
    var lastMessageTime: String {
        guard let lastMessage = lastMessage else { return "" }
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastMessage.timestamp, relativeTo: Date())
    }
    
    var unreadCount: Int {
        return messages.filter { !$0.isRead && $0.sender == .assistant }.count
    }
    
    mutating func addMessage(_ message: ChatMessage) {
        messages.append(message)
        updatedAt = Date()
        
        // Auto-generate title from first user message if still default
        if title == "New Chat", message.sender == .user, message.type == .text {
            let words = message.content.split(separator: " ").prefix(4)
            title = String(words.joined(separator: " "))
        }
    }
    
    mutating func markAllAsRead() {
        for index in messages.indices {
            messages[index].isRead = true
        }
        updatedAt = Date()
    }
}

extension ChatMessage {
    var timeAgo: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }
    
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
    
    var isFromUser: Bool {
        return sender == .user
    }
    
    var isFromAssistant: Bool {
        return sender == .assistant
    }
    
    var hasVoice: Bool {
        return type == .voice && metadata?.voiceURL != nil
    }
    
    var hasImage: Bool {
        return type == .image && metadata?.imageURL != nil
    }
    
    var hasQuickReplies: Bool {
        return metadata?.quickReplies?.isEmpty == false
    }
    
    var hasInvestmentData: Bool {
        return type == .investment && metadata?.investmentData != nil
    }
    
    var hasQuestData: Bool {
        return type == .quest && metadata?.questData != nil
    }
    
    mutating func addReaction(_ reaction: MessageReaction) {
        // Remove existing reaction from same user
        reactions.removeAll { $0.userID == reaction.userID && $0.emoji == reaction.emoji }
        // Add new reaction
        reactions.append(reaction)
    }
    
    func getReactionCount(for emoji: String) -> Int {
        return reactions.filter { $0.emoji == emoji }.count
    }
}
