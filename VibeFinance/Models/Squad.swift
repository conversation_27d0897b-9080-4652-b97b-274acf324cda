//
//  Squad.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Squad
struct Squad: Codable, Identifiable {
    let id: UUID
    var name: String
    var description: String
    var emoji: String
    let creatorID: UUID
    var members: [SquadMember]
    var investments: [SquadInvestment]
    var totalValue: Double
    var totalInvested: Double
    var isPublic: Bool
    var maxMembers: Int
    let createdAt: Date
    var updatedAt: Date
    
    init(name: String, description: String, emoji: String, creatorID: UUID, isPublic: Bool = true, maxMembers: Int = 20) {
        self.id = UUID()
        self.name = name
        self.description = description
        self.emoji = emoji
        self.creatorID = creatorID
        self.members = [SquadMember(userID: creatorID, role: .creator)]
        self.investments = []
        self.totalValue = 0.0
        self.totalInvested = 0.0
        self.isPublic = isPublic
        self.maxMembers = maxMembers
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Squad Member
struct SquadMember: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    var username: String?
    let role: SquadRole
    var contributedAmount: Double
    let joinedAt: Date
    var isActive: Bool
    
    init(userID: UUID, role: SquadRole = .member, username: String? = nil) {
        self.id = UUID()
        self.userID = userID
        self.username = username
        self.role = role
        self.contributedAmount = 0.0
        self.joinedAt = Date()
        self.isActive = true
    }
}

// MARK: - Squad Investment
struct SquadInvestment: Codable, Identifiable {
    let id: UUID
    let squadID: UUID
    let symbol: String
    let name: String
    let amount: Double
    let shares: Double
    let pricePerShare: Double
    var currentPrice: Double
    let votes: [InvestmentVote]
    let proposedBy: UUID
    var status: InvestmentStatus
    let createdAt: Date
    var executedAt: Date?
    
    init(squadID: UUID, symbol: String, name: String, amount: Double, shares: Double, pricePerShare: Double, proposedBy: UUID) {
        self.id = UUID()
        self.squadID = squadID
        self.symbol = symbol
        self.name = name
        self.amount = amount
        self.shares = shares
        self.pricePerShare = pricePerShare
        self.currentPrice = pricePerShare
        self.votes = []
        self.proposedBy = proposedBy
        self.status = .proposed
        self.createdAt = Date()
        self.executedAt = nil
    }
}

// MARK: - Investment Vote
struct InvestmentVote: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    let investmentID: UUID
    let vote: VoteType
    let amount: Double // How much they want to contribute
    let createdAt: Date
    
    init(userID: UUID, investmentID: UUID, vote: VoteType, amount: Double) {
        self.id = UUID()
        self.userID = userID
        self.investmentID = investmentID
        self.vote = vote
        self.amount = amount
        self.createdAt = Date()
    }
}

// MARK: - Squad Role
enum SquadRole: String, CaseIterable, Codable {
    case creator = "creator"
    case admin = "admin"
    case member = "member"
    
    var displayName: String {
        switch self {
        case .creator: return "Creator 👑"
        case .admin: return "Admin ⚡"
        case .member: return "Member 👤"
        }
    }
    
    var permissions: [SquadPermission] {
        switch self {
        case .creator:
            return SquadPermission.allCases
        case .admin:
            return [.proposeInvestment, .vote, .inviteMembers, .kickMembers]
        case .member:
            return [.proposeInvestment, .vote]
        }
    }
}

// MARK: - Squad Permission
enum SquadPermission: String, CaseIterable, Codable {
    case proposeInvestment = "propose_investment"
    case vote = "vote"
    case inviteMembers = "invite_members"
    case kickMembers = "kick_members"
    case editSquad = "edit_squad"
    case deleteSquad = "delete_squad"
}

// MARK: - Vote Type
enum VoteType: String, CaseIterable, Codable {
    case yes = "yes"
    case no = "no"
    
    var emoji: String {
        switch self {
        case .yes: return "👍"
        case .no: return "👎"
        }
    }
    
    var displayName: String {
        switch self {
        case .yes: return "Yes"
        case .no: return "No"
        }
    }
}

// MARK: - Investment Status
enum InvestmentStatus: String, CaseIterable, Codable {
    case proposed = "proposed"
    case voting = "voting"
    case approved = "approved"
    case rejected = "rejected"
    case executed = "executed"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .proposed: return "Proposed"
        case .voting: return "Voting"
        case .approved: return "Approved"
        case .rejected: return "Rejected"
        case .executed: return "Executed"
        case .cancelled: return "Cancelled"
        }
    }
    
    var emoji: String {
        switch self {
        case .proposed: return "💡"
        case .voting: return "🗳️"
        case .approved: return "✅"
        case .rejected: return "❌"
        case .executed: return "🚀"
        case .cancelled: return "🚫"
        }
    }
    
    var color: String {
        switch self {
        case .proposed: return "blue"
        case .voting: return "orange"
        case .approved: return "green"
        case .rejected: return "red"
        case .executed: return "purple"
        case .cancelled: return "gray"
        }
    }
}

// MARK: - Squad Extensions
extension Squad {
    var memberCount: Int {
        return members.filter { $0.isActive }.count
    }
    
    var isFull: Bool {
        return memberCount >= maxMembers
    }
    
    var totalReturn: Double {
        return totalValue - totalInvested
    }
    
    var returnPercentage: Double {
        guard totalInvested > 0 else { return 0.0 }
        return (totalReturn / totalInvested) * 100
    }
    
    var averageContribution: Double {
        let activeMembers = members.filter { $0.isActive }
        guard !activeMembers.isEmpty else { return 0.0 }
        let totalContributions = activeMembers.reduce(0) { $0 + $1.contributedAmount }
        return totalContributions / Double(activeMembers.count)
    }
    
    var topContributor: SquadMember? {
        return members.filter { $0.isActive }.max { $0.contributedAmount < $1.contributedAmount }
    }
    
    mutating func addMember(_ member: SquadMember) {
        guard !isFull else { return }
        members.append(member)
        updatedAt = Date()
    }
    
    mutating func removeMember(_ userID: UUID) {
        members.removeAll { $0.userID == userID }
        updatedAt = Date()
    }
    
    func canUserPerform(_ permission: SquadPermission, userID: UUID) -> Bool {
        guard let member = members.first(where: { $0.userID == userID && $0.isActive }) else {
            return false
        }
        return member.role.permissions.contains(permission)
    }
}

extension SquadInvestment {
    var currentValue: Double {
        return shares * currentPrice
    }
    
    var totalReturn: Double {
        return currentValue - amount
    }
    
    var returnPercentage: Double {
        guard amount > 0 else { return 0.0 }
        return (totalReturn / amount) * 100
    }
    
    var yesVotes: Int {
        return votes.filter { $0.vote == .yes }.count
    }
    
    var noVotes: Int {
        return votes.filter { $0.vote == .no }.count
    }
    
    var totalVotes: Int {
        return votes.count
    }
    
    var votePercentage: Double {
        guard totalVotes > 0 else { return 0.0 }
        return Double(yesVotes) / Double(totalVotes) * 100
    }
    
    var isApproved: Bool {
        return votePercentage >= 60.0 && totalVotes >= 3 // Require 60% approval and at least 3 votes
    }
    
    var timeAgo: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: createdAt, relativeTo: Date())
    }
}
