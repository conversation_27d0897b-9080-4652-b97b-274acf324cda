//
//  PaymentModels.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import StoreKit

// MARK: - Payment Result
enum PaymentResult {
    case success(Transaction)
    case cancelled
    case pending
    case failed(PaymentError)
}

// MARK: - Payment Error
enum PaymentError: Error, LocalizedError {
    case paymentPending
    case verificationFailed
    case invalidPaymentMethod
    case invalidBankDetails
    case subscriptionLoadFailed
    case subscriptionChangeFailed
    case subscriptionPauseFailed
    case subscriptionResumeFailed
    case billingHistoryLoadFailed
    case invoiceGenerationFailed
    case invalidPromoCode
    case paymentMethodAddFailed
    case paymentMethodRemoveFailed
    case paymentMethodsLoadFailed
    case networkError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .paymentPending:
            return "Payment is pending approval"
        case .verificationFailed:
            return "Payment verification failed"
        case .invalidPaymentMethod:
            return "Invalid payment method"
        case .invalidBankDetails:
            return "Invalid bank account details"
        case .subscriptionLoadFailed:
            return "Failed to load subscription details"
        case .subscriptionChangeFailed:
            return "Failed to change subscription plan"
        case .subscriptionPauseFailed:
            return "Failed to pause subscription"
        case .subscriptionResumeFailed:
            return "Failed to resume subscription"
        case .billingHistoryLoadFailed:
            return "Failed to load billing history"
        case .invoiceGenerationFailed:
            return "Failed to generate invoice"
        case .invalidPromoCode:
            return "Invalid or expired promo code"
        case .paymentMethodAddFailed:
            return "Failed to add payment method"
        case .paymentMethodRemoveFailed:
            return "Failed to remove payment method"
        case .paymentMethodsLoadFailed:
            return "Failed to load payment methods"
        case .networkError:
            return "Network connection error"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
    
    static func from(_ error: Error) -> PaymentError {
        if let paymentError = error as? PaymentError {
            return paymentError
        }
        return .unknownError
    }
}

// MARK: - Payment Method
struct PaymentMethod: Codable, Identifiable {
    let id: String
    let type: PaymentMethodType
    let displayName: String
    let lastFour: String?
    let expiryDate: String?
    let isDefault: Bool
    let bankDetails: BankDetails?
    let createdAt: Date
    
    var isValid: Bool {
        switch type {
        case .creditCard:
            guard let expiryDate = expiryDate else { return false }
            return !isExpired(expiryDate)
        case .applePay:
            return true
        case .bankTransfer:
            return bankDetails?.isValid ?? false
        }
    }
    
    var displayIcon: String {
        switch type {
        case .creditCard:
            return "creditcard.fill"
        case .applePay:
            return "apple.logo"
        case .bankTransfer:
            return "building.columns.fill"
        }
    }
    
    private func isExpired(_ expiryDate: String) -> Bool {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/yy"
        guard let expiry = formatter.date(from: expiryDate) else { return true }
        return expiry < Date()
    }
}

enum PaymentMethodType: String, Codable, CaseIterable {
    case creditCard = "credit_card"
    case applePay = "apple_pay"
    case bankTransfer = "bank_transfer"
    
    var displayName: String {
        switch self {
        case .creditCard: return "Credit Card"
        case .applePay: return "Apple Pay"
        case .bankTransfer: return "Bank Transfer"
        }
    }
}

struct BankDetails: Codable {
    let accountNumber: String
    let routingNumber: String
    let accountType: BankAccountType
    let bankName: String
    
    var isValid: Bool {
        return !accountNumber.isEmpty && 
               !routingNumber.isEmpty && 
               routingNumber.count == 9 &&
               accountNumber.count >= 4
    }
}

enum BankAccountType: String, Codable, CaseIterable {
    case checking = "checking"
    case savings = "savings"
    
    var displayName: String {
        switch self {
        case .checking: return "Checking"
        case .savings: return "Savings"
        }
    }
}

// MARK: - Subscription Details
struct SubscriptionDetails: Codable {
    let currentSubscription: ActiveSubscription?
    let nextBillingDate: Date?
    let billingCycle: BillingCycle
    
    var isActive: Bool {
        return currentSubscription?.status == .active
    }
    
    var daysUntilRenewal: Int? {
        guard let nextBillingDate = nextBillingDate else { return nil }
        return Calendar.current.dateComponents([.day], from: Date(), to: nextBillingDate).day
    }
}

struct ActiveSubscription: Codable, Identifiable {
    let id: String
    let productId: String
    let status: SubscriptionStatus
    let currentPeriodStart: Date
    let currentPeriodEnd: Date
    let cancelAtPeriodEnd: Bool
    
    var tier: SubscriptionTier {
        if productId.contains("pro") {
            return .pro
        } else if productId.contains("basic") {
            return .basic
        } else {
            return .free
        }
    }
    
    var isExpiringSoon: Bool {
        let daysUntilExpiry = Calendar.current.dateComponents([.day], from: Date(), to: currentPeriodEnd).day ?? 0
        return daysUntilExpiry <= 7
    }
}

enum SubscriptionStatus: String, Codable, CaseIterable {
    case active = "active"
    case paused = "paused"
    case cancelled = "cancelled"
    case expired = "expired"
    case pastDue = "past_due"
    
    var displayName: String {
        switch self {
        case .active: return "Active"
        case .paused: return "Paused"
        case .cancelled: return "Cancelled"
        case .expired: return "Expired"
        case .pastDue: return "Past Due"
        }
    }
    
    var color: String {
        switch self {
        case .active: return "green"
        case .paused: return "orange"
        case .cancelled, .expired: return "red"
        case .pastDue: return "red"
        }
    }
}

enum BillingCycle: String, Codable, CaseIterable {
    case monthly = "monthly"
    case yearly = "yearly"
    case lifetime = "lifetime"
    
    var displayName: String {
        switch self {
        case .monthly: return "Monthly"
        case .yearly: return "Yearly"
        case .lifetime: return "Lifetime"
        }
    }
    
    var discountPercentage: Int {
        switch self {
        case .monthly: return 0
        case .yearly: return 20 // 20% discount for yearly
        case .lifetime: return 50 // 50% discount for lifetime
        }
    }
}

// MARK: - Billing Record
struct BillingRecord: Codable, Identifiable {
    let id: String
    let productId: String
    let amount: Decimal
    let currency: String
    let date: Date
    let status: BillingStatus
    let description: String
    let invoiceURL: URL?
    
    var formattedAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        return formatter.string(from: amount as NSDecimalNumber) ?? "$0.00"
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }
}

enum BillingStatus: String, Codable, CaseIterable {
    case completed = "completed"
    case pending = "pending"
    case failed = "failed"
    case refunded = "refunded"
    
    var displayName: String {
        switch self {
        case .completed: return "Completed"
        case .pending: return "Pending"
        case .failed: return "Failed"
        case .refunded: return "Refunded"
        }
    }
    
    var color: String {
        switch self {
        case .completed: return "green"
        case .pending: return "orange"
        case .failed: return "red"
        case .refunded: return "blue"
        }
    }
    
    var icon: String {
        switch self {
        case .completed: return "checkmark.circle.fill"
        case .pending: return "clock.fill"
        case .failed: return "xmark.circle.fill"
        case .refunded: return "arrow.counterclockwise.circle.fill"
        }
    }
}

// MARK: - Discount & Promo Codes
struct Discount: Codable, Identifiable {
    let id: String
    let code: String
    let type: DiscountType
    let value: Double
    let description: String
    let validUntil: Date?
    let maxUses: Int?
    let currentUses: Int
    
    var isValid: Bool {
        if let validUntil = validUntil, validUntil < Date() {
            return false
        }
        
        if let maxUses = maxUses, currentUses >= maxUses {
            return false
        }
        
        return true
    }
    
    var formattedValue: String {
        switch type {
        case .percentage:
            return "\(Int(value))% off"
        case .fixedAmount:
            return "$\(String(format: "%.2f", value)) off"
        case .freeMonths:
            return "\(Int(value)) month\(value == 1 ? "" : "s") free"
        }
    }
}

enum DiscountType: String, Codable, CaseIterable {
    case percentage = "percentage"
    case fixedAmount = "fixed_amount"
    case freeMonths = "free_months"
    
    var displayName: String {
        switch self {
        case .percentage: return "Percentage"
        case .fixedAmount: return "Fixed Amount"
        case .freeMonths: return "Free Months"
        }
    }
}

// MARK: - Revenue Metrics
struct RevenueMetrics: Codable {
    let monthlyRecurringRevenue: Double
    let annualRecurringRevenue: Double
    let churnRate: Double
    let averageRevenuePerUser: Double
    let conversionRate: Double
    let lifetimeValue: Double
    
    var formattedMRR: String {
        return formatCurrency(monthlyRecurringRevenue)
    }
    
    var formattedARR: String {
        return formatCurrency(annualRecurringRevenue)
    }
    
    var formattedARPU: String {
        return formatCurrency(averageRevenuePerUser)
    }
    
    var formattedLTV: String {
        return formatCurrency(lifetimeValue)
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "USD"
        
        if amount >= 1_000_000 {
            formatter.maximumFractionDigits = 1
            return formatter.string(from: NSNumber(value: amount / 1_000_000)) ?? "$0" + "M"
        } else if amount >= 1_000 {
            formatter.maximumFractionDigits = 0
            return formatter.string(from: NSNumber(value: amount / 1_000)) ?? "$0" + "K"
        } else {
            formatter.maximumFractionDigits = 2
            return formatter.string(from: NSNumber(value: amount)) ?? "$0"
        }
    }
}

// MARK: - Conversion Event
struct ConversionEvent: Codable {
    let fromTier: SubscriptionTier
    let toTier: SubscriptionTier
    let timestamp: Date
    let revenue: Double
    
    var conversionType: String {
        if fromTier == .free {
            return "Acquisition"
        } else {
            return "Upgrade"
        }
    }
}

// MARK: - Enhanced Subscription Tier
extension SubscriptionTier {
    var monthlyPrice: Double {
        switch self {
        case .free: return 0.0
        case .basic: return 9.99
        case .pro: return 19.99
        }
    }
    
    var yearlyPrice: Double {
        return monthlyPrice * 12 * 0.8 // 20% discount for yearly
    }
    
    var lifetimePrice: Double {
        return monthlyPrice * 12 * 5 // 5 years worth at 50% discount
    }
    
    var savings: String {
        switch self {
        case .free: return ""
        case .basic: return "Save $24/year with yearly billing"
        case .pro: return "Save $48/year with yearly billing"
        }
    }
    
    var popularBadge: Bool {
        return self == .pro
    }
}
