//
//  AnalyticsDataModels.swift
//  VibeFinance - Analytics Data Models
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI

// MARK: - Analytics Data Models

struct AnalyticsDataPoint: Identifiable, Codable {
    let id: UUID
    let date: Date
    let value: Double
    let volume: Int64?
    let metadata: [String: String]
    
    // Additional OHLC data for candlestick charts
    let open: Double?
    let high: Double?
    let low: Double?
    let close: Double?
    
    init(id: UUID = UUID(), date: Date, value: Double, volume: Int64? = nil, metadata: [String: String] = [:], open: Double? = nil, high: Double? = nil, low: Double? = nil, close: Double? = nil) {
        self.id = id
        self.date = date
        self.value = value
        self.volume = volume
        self.metadata = metadata
        self.open = open
        self.high = high
        self.low = low
        self.close = close
    }
}

// MARK: - Analytics Enums

enum AnalyticsTimeframe: String, CaseIterable, Codable {
    case oneWeek = "1W"
    case oneMonth = "1M"
    case threeMonths = "3M"
    case sixMonths = "6M"
    case oneYear = "1Y"
    case threeYears = "3Y"
    case fiveYears = "5Y"
    case all = "ALL"
    
    var displayName: String {
        switch self {
        case .oneWeek: return "1 Week"
        case .oneMonth: return "1 Month"
        case .threeMonths: return "3 Months"
        case .sixMonths: return "6 Months"
        case .oneYear: return "1 Year"
        case .threeYears: return "3 Years"
        case .fiveYears: return "5 Years"
        case .all: return "All Time"
        }
    }
    
    var dataPoints: Int {
        switch self {
        case .oneWeek: return 7
        case .oneMonth: return 30
        case .threeMonths: return 90
        case .sixMonths: return 180
        case .oneYear: return 365
        case .threeYears: return 1095
        case .fiveYears: return 1825
        case .all: return 2555 // 7 years
        }
    }
}

enum AnalyticsMetricType: String, CaseIterable, Codable {
    case portfolioValue = "portfolio_value"
    case totalReturn = "total_return"
    case sharpeRatio = "sharpe_ratio"
    case volatility = "volatility"
    case beta = "beta"
    case maxDrawdown = "max_drawdown"
    
    var displayName: String {
        switch self {
        case .portfolioValue: return "Portfolio Value"
        case .totalReturn: return "Total Return"
        case .sharpeRatio: return "Sharpe Ratio"
        case .volatility: return "Volatility"
        case .beta: return "Beta"
        case .maxDrawdown: return "Max Drawdown"
        }
    }
    
    var description: String {
        switch self {
        case .portfolioValue: return "Total value of your investment portfolio"
        case .totalReturn: return "Cumulative return including dividends and capital gains"
        case .sharpeRatio: return "Risk-adjusted return measure (return per unit of risk)"
        case .volatility: return "Standard deviation of returns (price fluctuation)"
        case .beta: return "Sensitivity to market movements (1.0 = market average)"
        case .maxDrawdown: return "Largest peak-to-trough decline in portfolio value"
        }
    }
    
    var unit: String {
        switch self {
        case .portfolioValue: return "$"
        case .totalReturn: return "%"
        case .sharpeRatio: return ""
        case .volatility: return "%"
        case .beta: return ""
        case .maxDrawdown: return "%"
        }
    }
    
    var formatStyle: String {
        switch self {
        case .portfolioValue: return "currency"
        case .totalReturn, .volatility, .maxDrawdown: return "percentage"
        case .sharpeRatio, .beta: return "decimal"
        }
    }
}

enum DisclosureLevel: String, CaseIterable, Codable {
    case overview = "overview"
    case detailed = "detailed"
    case expert = "expert"
    
    var displayName: String {
        switch self {
        case .overview: return "Overview"
        case .detailed: return "Detailed"
        case .expert: return "Expert"
        }
    }
    
    var description: String {
        switch self {
        case .overview: return "Key metrics and trends"
        case .detailed: return "Comprehensive analysis with context"
        case .expert: return "Advanced metrics and technical analysis"
        }
    }
}

// MARK: - Analytics Insight Model

struct AnalyticsInsight: Identifiable, Codable {
    let id: UUID
    let title: String
    let description: String
    let type: InsightType
    let confidence: Double // 0.0 to 1.0
    let buffettQuote: String
    let actionable: Bool
    let priority: InsightPriority
    let category: InsightCategory
    
    init(id: UUID = UUID(), title: String, description: String, type: InsightType, confidence: Double, buffettQuote: String, actionable: Bool = false, priority: InsightPriority = .medium, category: InsightCategory = .performance) {
        self.id = id
        self.title = title
        self.description = description
        self.type = type
        self.confidence = confidence
        self.buffettQuote = buffettQuote
        self.actionable = actionable
        self.priority = priority
        self.category = category
    }
}

enum InsightType: String, CaseIterable, Codable {
    case positive = "positive"
    case negative = "negative"
    case neutral = "neutral"
    case warning = "warning"
    case opportunity = "opportunity"
    
    var iconName: String {
        switch self {
        case .positive: return "checkmark.circle.fill"
        case .negative: return "xmark.circle.fill"
        case .neutral: return "info.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .opportunity: return "star.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .positive: return .green
        case .negative: return .red
        case .neutral: return .blue
        case .warning: return .orange
        case .opportunity: return .yellow
        }
    }
    
    var backgroundColor: Color {
        switch self {
        case .positive: return .green.opacity(0.1)
        case .negative: return .red.opacity(0.1)
        case .neutral: return .blue.opacity(0.1)
        case .warning: return .orange.opacity(0.1)
        case .opportunity: return .yellow.opacity(0.1)
        }
    }
    
    var borderColor: Color {
        switch self {
        case .positive: return .green.opacity(0.3)
        case .negative: return .red.opacity(0.3)
        case .neutral: return .blue.opacity(0.3)
        case .warning: return .orange.opacity(0.3)
        case .opportunity: return .yellow.opacity(0.3)
        }
    }
}

enum InsightPriority: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        case .critical: return "Critical"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .gray
        case .medium: return .blue
        case .high: return .orange
        case .critical: return .red
        }
    }
}

enum InsightCategory: String, CaseIterable, Codable {
    case performance = "performance"
    case risk = "risk"
    case diversification = "diversification"
    case allocation = "allocation"
    case market = "market"
    case behavioral = "behavioral"
    
    var displayName: String {
        switch self {
        case .performance: return "Performance"
        case .risk: return "Risk Management"
        case .diversification: return "Diversification"
        case .allocation: return "Asset Allocation"
        case .market: return "Market Analysis"
        case .behavioral: return "Behavioral Finance"
        }
    }
    
    var iconName: String {
        switch self {
        case .performance: return "chart.line.uptrend.xyaxis"
        case .risk: return "shield.checkered"
        case .diversification: return "chart.pie"
        case .allocation: return "square.grid.3x3"
        case .market: return "globe"
        case .behavioral: return "brain.head.profile"
        }
    }
}

// MARK: - Benchmark Data Model

struct BenchmarkData: Identifiable, Codable {
    let id: UUID
    let name: String
    let symbol: String
    let value: Double
    let change: Double
    let changePercent: Double
    let description: String
    
    init(id: UUID = UUID(), name: String, symbol: String, value: Double, change: Double, changePercent: Double, description: String) {
        self.id = id
        self.name = name
        self.symbol = symbol
        self.value = value
        self.change = change
        self.changePercent = changePercent
        self.description = description
    }
}

// MARK: - Technical Indicator Models

struct TechnicalIndicator: Identifiable, Codable {
    let id: UUID
    let name: String
    let value: Double
    let signal: TechnicalSignal
    let description: String
    let confidence: Double
    
    init(id: UUID = UUID(), name: String, value: Double, signal: TechnicalSignal, description: String, confidence: Double) {
        self.id = id
        self.name = name
        self.value = value
        self.signal = signal
        self.description = description
        self.confidence = confidence
    }
}

enum TechnicalSignal: String, CaseIterable, Codable {
    case buy = "buy"
    case sell = "sell"
    case hold = "hold"
    case strongBuy = "strong_buy"
    case strongSell = "strong_sell"
    
    var displayName: String {
        switch self {
        case .buy: return "Buy"
        case .sell: return "Sell"
        case .hold: return "Hold"
        case .strongBuy: return "Strong Buy"
        case .strongSell: return "Strong Sell"
        }
    }
    
    var color: Color {
        switch self {
        case .buy, .strongBuy: return .green
        case .sell, .strongSell: return .red
        case .hold: return .orange
        }
    }
}

// MARK: - Analytics Configuration

struct AnalyticsConfiguration: Codable {
    let enabledMetrics: [AnalyticsMetricType]
    let defaultTimeframe: AnalyticsTimeframe
    let defaultDisclosureLevel: DisclosureLevel
    let benchmarks: [String] // Benchmark symbols
    let technicalIndicators: [String] // Enabled technical indicators
    let refreshInterval: TimeInterval
    let cacheEnabled: Bool
    
    static let `default` = AnalyticsConfiguration(
        enabledMetrics: AnalyticsMetricType.allCases,
        defaultTimeframe: .oneMonth,
        defaultDisclosureLevel: .overview,
        benchmarks: ["SPY", "QQQ", "VTI"],
        technicalIndicators: ["SMA", "EMA", "RSI", "MACD"],
        refreshInterval: 300, // 5 minutes
        cacheEnabled: true
    )
}

// MARK: - Mock Data Generation

extension AnalyticsDataPoint {
    static func generateMockData(for metric: AnalyticsMetricType, timeframe: AnalyticsTimeframe, count: Int? = nil) -> [AnalyticsDataPoint] {
        let dataCount = count ?? min(timeframe.dataPoints, 100)
        let calendar = Calendar.current
        let endDate = Date()
        
        var data: [AnalyticsDataPoint] = []
        var currentValue = getBaseValue(for: metric)
        
        for i in 0..<dataCount {
            let date = calendar.date(byAdding: .day, value: -i, to: endDate) ?? endDate
            
            // Add some realistic variation
            let variation = Double.random(in: -0.05...0.05)
            currentValue *= (1 + variation)
            
            // Generate OHLC data for candlestick charts
            let open = currentValue * (1 + Double.random(in: -0.02...0.02))
            let high = max(open, currentValue) * (1 + Double.random(in: 0...0.03))
            let low = min(open, currentValue) * (1 - Double.random(in: 0...0.03))
            let close = currentValue
            
            data.append(AnalyticsDataPoint(
                date: date,
                value: currentValue,
                volume: Int64.random(in: 1_000_000...10_000_000),
                open: open,
                high: high,
                low: low,
                close: close
            ))
        }
        
        return data.reversed()
    }
    
    private static func getBaseValue(for metric: AnalyticsMetricType) -> Double {
        switch metric {
        case .portfolioValue: return 125000
        case .totalReturn: return 10.0
        case .sharpeRatio: return 1.3
        case .volatility: return 20.0
        case .beta: return 1.1
        case .maxDrawdown: return -10.0
        }
    }
}
