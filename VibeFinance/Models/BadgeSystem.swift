//
//  BadgeSystem.swift
//  VibeFinance - Advanced Badge Collection System
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import Foundation
import SwiftUI

// MARK: - Badge Models

struct Badge: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let icon: String
    let category: BadgeCategory
    let rarity: BadgeRarity
    let requirements: BadgeRequirements
    let rewards: BadgeRewards
    let isUnlocked: Bool
    let unlockedAt: Date?
    let progress: BadgeProgress?
    
    // Visual properties
    var rarityColor: Color {
        switch rarity {
        case .common: return .gray
        case .uncommon: return .green
        case .rare: return .blue
        case .epic: return .purple
        case .legendary: return .orange
        case .mythic: return .pink
        }
    }
    
    var rarityGradient: LinearGradient {
        switch rarity {
        case .common:
            return LinearGradient(colors: [.gray.opacity(0.3), .gray], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .uncommon:
            return LinearGradient(colors: [.green.opacity(0.3), .green], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .rare:
            return LinearGradient(colors: [.blue.opacity(0.3), .blue], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .epic:
            return LinearGradient(colors: [.purple.opacity(0.3), .purple], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .legendary:
            return LinearGradient(colors: [.orange.opacity(0.3), .orange, .yellow], startPoint: .topLeading, endPoint: .bottomTrailing)
        case .mythic:
            return LinearGradient(colors: [.pink.opacity(0.3), .pink, .purple, .blue], startPoint: .topLeading, endPoint: .bottomTrailing)
        }
    }
}

enum BadgeCategory: String, CaseIterable, Codable {
    case learning = "learning"
    case trading = "trading"
    case social = "social"
    case milestones = "milestones"
    case special = "special"
    case seasonal = "seasonal"
    
    var emoji: String {
        switch self {
        case .learning: return "🎓"
        case .trading: return "📈"
        case .social: return "👥"
        case .milestones: return "🏆"
        case .special: return "⭐"
        case .seasonal: return "🎃"
        }
    }
    
    var displayName: String {
        switch self {
        case .learning: return "Learning"
        case .trading: return "Trading"
        case .social: return "Social"
        case .milestones: return "Milestones"
        case .special: return "Special"
        case .seasonal: return "Seasonal"
        }
    }
}

enum BadgeRarity: String, CaseIterable, Codable {
    case common = "common"
    case uncommon = "uncommon"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"
    case mythic = "mythic"
    
    var displayName: String {
        switch self {
        case .common: return "Common"
        case .uncommon: return "Uncommon"
        case .rare: return "Rare"
        case .epic: return "Epic"
        case .legendary: return "Legendary"
        case .mythic: return "Mythic"
        }
    }
    
    var sparkleCount: Int {
        switch self {
        case .common: return 0
        case .uncommon: return 1
        case .rare: return 2
        case .epic: return 3
        case .legendary: return 4
        case .mythic: return 5
        }
    }
}

struct BadgeRequirements: Codable {
    let type: RequirementType
    let target: Int
    let description: String
    let additionalConditions: [String]?
    
    enum RequirementType: String, Codable {
        case questsCompleted = "quests_completed"
        case daysActive = "days_active"
        case tradingVolume = "trading_volume"
        case socialInteractions = "social_interactions"
        case portfolioValue = "portfolio_value"
        case streakDays = "streak_days"
        case achievementsUnlocked = "achievements_unlocked"
        case referralsCompleted = "referrals_completed"
        case lessonsCompleted = "lessons_completed"
        case perfectScores = "perfect_scores"
    }
}

struct BadgeRewards: Codable {
    let xp: Int
    let coins: Int
    let unlockableThemes: [String]?
    let specialPerks: [String]?
    let title: String?
}

struct BadgeProgress: Codable {
    let current: Int
    let target: Int
    let percentage: Double
    
    init(current: Int, target: Int) {
        self.current = current
        self.target = target
        self.percentage = min(Double(current) / Double(target), 1.0)
    }
}

// MARK: - Badge Collection

struct BadgeCollection: Codable {
    let userID: UUID
    let badges: [Badge]
    let totalBadges: Int
    let unlockedBadges: Int
    let favoriteBadge: String?
    let displayBadges: [String] // Up to 3 badges to display on profile
    
    var completionPercentage: Double {
        guard totalBadges > 0 else { return 0 }
        return Double(unlockedBadges) / Double(totalBadges)
    }
    
    var rarityBreakdown: [BadgeRarity: Int] {
        var breakdown: [BadgeRarity: Int] = [:]
        for badge in badges.filter({ $0.isUnlocked }) {
            breakdown[badge.rarity, default: 0] += 1
        }
        return breakdown
    }
}

// MARK: - Badge Showcase

struct BadgeShowcase: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let badges: [Badge]
    let isLimited: Bool
    let expiresAt: Date?
}
