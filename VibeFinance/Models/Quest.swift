//
//  Quest.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Quest
struct Quest: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let category: QuestCategory
    let difficulty: DifficultyLevel
    let xpReward: Int
    let estimatedTime: Int // in minutes
    let tasks: [QuestTask]
    let unlockLevel: Int
    let isDaily: Bool
    let expiresAt: Date?
    let createdAt: Date
    
    init(title: String, description: String, category: QuestCategory, difficulty: DifficultyLevel, xpReward: Int, estimatedTime: Int, tasks: [QuestTask], unlockLevel: Int = 1, isDaily: Bool = false, expiresAt: Date? = nil) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.category = category
        self.difficulty = difficulty
        self.xpReward = xpReward
        self.estimatedTime = estimatedTime
        self.tasks = tasks
        self.unlockLevel = unlockLevel
        self.isDaily = isDaily
        self.expiresAt = expiresAt
        self.createdAt = Date()
    }
}

// MARK: - Quest Progress
struct QuestProgress: Codable, Identifiable {
    let id: UUID
    let userID: UUID
    let questID: UUID
    var status: QuestStatus
    var completedTasks: [UUID] // Task IDs
    var startedAt: Date
    var completedAt: Date?
    var xpEarned: Int
    
    init(userID: UUID, questID: UUID) {
        self.id = UUID()
        self.userID = userID
        self.questID = questID
        self.status = .notStarted
        self.completedTasks = []
        self.startedAt = Date()
        self.completedAt = nil
        self.xpEarned = 0
    }
}

// MARK: - Quest Task
struct QuestTask: Codable, Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: TaskType
    let data: TaskData?
    let xpReward: Int
    let order: Int
    
    init(title: String, description: String, type: TaskType, data: TaskData? = nil, xpReward: Int, order: Int) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.type = type
        self.data = data
        self.xpReward = xpReward
        self.order = order
    }
}

// MARK: - Task Data
struct TaskData: Codable {
    let question: String?
    let options: [String]?
    let correctAnswer: String?
    let explanation: String?
    let targetAmount: Double?
    let targetSymbol: String?
    let readingMaterial: String?
    let videoURL: String?
    
    init(question: String? = nil, options: [String]? = nil, correctAnswer: String? = nil, explanation: String? = nil, targetAmount: Double? = nil, targetSymbol: String? = nil, readingMaterial: String? = nil, videoURL: String? = nil) {
        self.question = question
        self.options = options
        self.correctAnswer = correctAnswer
        self.explanation = explanation
        self.targetAmount = targetAmount
        self.targetSymbol = targetSymbol
        self.readingMaterial = readingMaterial
        self.videoURL = videoURL
    }
}

// MARK: - Quest Category
enum QuestCategory: String, CaseIterable, Codable {
    case basics = "basics"
    case stocks = "stocks"
    case crypto = "crypto"
    case budgeting = "budgeting"
    case investing = "investing"
    case trading = "trading"
    case retirement = "retirement"
    case taxes = "taxes"
    case realEstate = "real_estate"
    case entrepreneurship = "entrepreneurship"
    
    var displayName: String {
        switch self {
        case .basics: return "Finance Basics 📚"
        case .stocks: return "Stock Market 📈"
        case .crypto: return "Cryptocurrency 🪙"
        case .budgeting: return "Budgeting 💰"
        case .investing: return "Investing 🎯"
        case .trading: return "Trading ⚡"
        case .retirement: return "Retirement 🏖️"
        case .taxes: return "Taxes 📋"
        case .realEstate: return "Real Estate 🏠"
        case .entrepreneurship: return "Business 🚀"
        }
    }
    
    var emoji: String {
        switch self {
        case .basics: return "📚"
        case .stocks: return "📈"
        case .crypto: return "🪙"
        case .budgeting: return "💰"
        case .investing: return "🎯"
        case .trading: return "⚡"
        case .retirement: return "🏖️"
        case .taxes: return "📋"
        case .realEstate: return "🏠"
        case .entrepreneurship: return "🚀"
        }
    }
    
    var color: String {
        switch self {
        case .basics: return "blue"
        case .stocks: return "green"
        case .crypto: return "orange"
        case .budgeting: return "purple"
        case .investing: return "red"
        case .trading: return "yellow"
        case .retirement: return "pink"
        case .taxes: return "gray"
        case .realEstate: return "brown"
        case .entrepreneurship: return "cyan"
        }
    }
}

// MARK: - Quest Status
enum QuestStatus: String, CaseIterable, Codable {
    case notStarted = "not_started"
    case inProgress = "in_progress"
    case completed = "completed"
    case expired = "expired"
    case locked = "locked"
    
    var displayName: String {
        switch self {
        case .notStarted: return "Not Started"
        case .inProgress: return "In Progress"
        case .completed: return "Completed"
        case .expired: return "Expired"
        case .locked: return "Locked"
        }
    }
    
    var emoji: String {
        switch self {
        case .notStarted: return "⭐"
        case .inProgress: return "🔄"
        case .completed: return "✅"
        case .expired: return "⏰"
        case .locked: return "🔒"
        }
    }
}

// MARK: - Task Type
enum TaskType: String, CaseIterable, Codable {
    case multipleChoice = "multiple_choice"
    case reading = "reading"
    case video = "video"
    case simulation = "simulation"
    case calculation = "calculation"
    case research = "research"
    case reflection = "reflection"
    
    var displayName: String {
        switch self {
        case .multipleChoice: return "Quiz"
        case .reading: return "Reading"
        case .video: return "Video"
        case .simulation: return "Simulation"
        case .calculation: return "Calculation"
        case .research: return "Research"
        case .reflection: return "Reflection"
        }
    }
    
    var emoji: String {
        switch self {
        case .multipleChoice: return "❓"
        case .reading: return "📖"
        case .video: return "🎥"
        case .simulation: return "🎮"
        case .calculation: return "🧮"
        case .research: return "🔍"
        case .reflection: return "💭"
        }
    }
}

// MARK: - Quest Extensions
extension Quest {
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    var timeRemaining: String? {
        guard let expiresAt = expiresAt else { return nil }
        let timeInterval = expiresAt.timeIntervalSince(Date())
        if timeInterval <= 0 { return "Expired" }
        
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m left"
        } else {
            return "\(minutes)m left"
        }
    }
    
    var difficultyColor: String {
        switch difficulty {
        case .beginner: return "green"
        case .intermediate: return "orange"
        case .advanced: return "red"
        }
    }
}

extension QuestProgress {
    var progressPercentage: Double {
        guard !completedTasks.isEmpty else { return 0.0 }
        // This would need the actual quest to calculate properly
        // For now, return a simple calculation
        return Double(completedTasks.count) / 5.0 // Assuming 5 tasks per quest
    }
    
    var isCompleted: Bool {
        return status == .completed
    }
    
    var canStart: Bool {
        return status == .notStarted
    }
    
    var canContinue: Bool {
        return status == .inProgress
    }
    
    mutating func completeTask(_ taskID: UUID, xpReward: Int) {
        if !completedTasks.contains(taskID) {
            completedTasks.append(taskID)
            xpEarned += xpReward
        }
    }
    
    mutating func complete(totalXP: Int) {
        status = .completed
        completedAt = Date()
        xpEarned = totalXP
    }
}
