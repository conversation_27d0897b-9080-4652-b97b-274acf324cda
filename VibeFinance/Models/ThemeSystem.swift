//
//  ThemeSystem.swift
//  VibeFinance - Advanced Theme & Customization System
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import Foundation
import SwiftUI

// MARK: - Unlockable Theme Models

struct UnlockableAppTheme: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let category: ThemeCategory
    let rarity: ThemeRarity
    let unlockRequirements: ThemeUnlockRequirements
    let colors: ThemeColors
    let effects: ThemeEffects?
    let isUnlocked: Bool
    let unlockedAt: Date?
    let isPremium: Bool
    let previewImage: String?
    
    var rarityColor: Color {
        switch rarity {
        case .basic: return .gray
        case .premium: return .blue
        case .exclusive: return .purple
        case .legendary: return .orange
        case .mythic: return .pink
        }
    }
}

enum ThemeCategory: String, CaseIterable, Codable {
    case classic = "classic"
    case neon = "neon"
    case nature = "nature"
    case space = "space"
    case retro = "retro"
    case minimal = "minimal"
    case gaming = "gaming"
    case seasonal = "seasonal"
    
    var displayName: String {
        switch self {
        case .classic: return "Classic"
        case .neon: return "Neon"
        case .nature: return "Nature"
        case .space: return "Space"
        case .retro: return "Retro"
        case .minimal: return "Minimal"
        case .gaming: return "Gaming"
        case .seasonal: return "Seasonal"
        }
    }
    
    var emoji: String {
        switch self {
        case .classic: return "🎨"
        case .neon: return "⚡"
        case .nature: return "🌿"
        case .space: return "🚀"
        case .retro: return "📼"
        case .minimal: return "⚪"
        case .gaming: return "🎮"
        case .seasonal: return "🎃"
        }
    }
}

enum ThemeRarity: String, CaseIterable, Codable {
    case basic = "basic"
    case premium = "premium"
    case exclusive = "exclusive"
    case legendary = "legendary"
    case mythic = "mythic"
    
    var displayName: String {
        switch self {
        case .basic: return "Basic"
        case .premium: return "Premium"
        case .exclusive: return "Exclusive"
        case .legendary: return "Legendary"
        case .mythic: return "Mythic"
        }
    }
}

struct ThemeUnlockRequirements: Codable {
    let type: UnlockType
    let target: Int
    let description: String
    let badgeRequired: String?
    let achievementRequired: String?
    let levelRequired: Int?
    
    enum UnlockType: String, Codable {
        case questsCompleted = "quests_completed"
        case badgeUnlocked = "badge_unlocked"
        case achievementUnlocked = "achievement_unlocked"
        case streakDays = "streak_days"
        case levelReached = "level_reached"
        case premiumPurchase = "premium_purchase"
        case socialMilestone = "social_milestone"
        case tradingMilestone = "trading_milestone"
    }
}

struct ThemeColors: Codable {
    let primary: String
    let secondary: String
    let accent: String
    let background: String
    let surface: String
    let onPrimary: String
    let onSecondary: String
    let onBackground: String
    let onSurface: String
    let success: String
    let warning: String
    let error: String
    
    // Convert hex strings to SwiftUI Colors
    var primaryColor: Color { Color(hex: primary) }
    var secondaryColor: Color { Color(hex: secondary) }
    var accentColor: Color { Color(hex: accent) }
    var backgroundColor: Color { Color(hex: background) }
    var surfaceColor: Color { Color(hex: surface) }
    var onPrimaryColor: Color { Color(hex: onPrimary) }
    var onSecondaryColor: Color { Color(hex: onSecondary) }
    var onBackgroundColor: Color { Color(hex: onBackground) }
    var onSurfaceColor: Color { Color(hex: onSurface) }
    var successColor: Color { Color(hex: success) }
    var warningColor: Color { Color(hex: warning) }
    var errorColor: Color { Color(hex: error) }
}

struct ThemeEffects: Codable {
    let hasGradients: Bool
    let hasAnimations: Bool
    let hasParticles: Bool
    let hasGlowEffects: Bool
    let hasBlurEffects: Bool
    let customAnimations: [String]?
    let particleType: ParticleType?
    
    enum ParticleType: String, Codable {
        case stars = "stars"
        case sparkles = "sparkles"
        case bubbles = "bubbles"
        case snow = "snow"
        case leaves = "leaves"
        case fire = "fire"
    }
}

// MARK: - Avatar Customization

struct AvatarCustomization: Identifiable, Codable {
    let id: String
    let name: String
    let category: AvatarCategory
    let rarity: ThemeRarity
    let unlockRequirements: ThemeUnlockRequirements
    let assetName: String
    let isUnlocked: Bool
    let isPremium: Bool
}

enum AvatarCategory: String, CaseIterable, Codable {
    case background = "background"
    case frame = "frame"
    case badge = "badge"
    case effect = "effect"
    
    var displayName: String {
        switch self {
        case .background: return "Background"
        case .frame: return "Frame"
        case .badge: return "Badge"
        case .effect: return "Effect"
        }
    }
}

// MARK: - User Customization Profile

struct UserCustomizationProfile: Codable {
    let userID: UUID
    let activeTheme: String
    let unlockedThemes: [String]
    let favoriteThemes: [String]
    let avatarCustomizations: [String: String] // category -> selected item ID
    let unlockedAvatarItems: [String]
    let customizationLevel: Int
    let totalCustomizationsUnlocked: Int
    
    var customizationProgress: Double {
        // Calculate based on unlocked items vs total available
        return Double(totalCustomizationsUnlocked) / 100.0 // Assuming 100 total items
    }
}

// MARK: - Theme Showcase

struct ThemeShowcase: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let themes: [UnlockableAppTheme]
    let isLimited: Bool
    let expiresAt: Date?
    let category: ThemeCategory?
}

// MARK: - Color Extension for Hex Support

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
