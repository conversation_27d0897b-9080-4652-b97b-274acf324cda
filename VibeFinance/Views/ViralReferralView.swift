//
//  ViralReferralView.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI

struct ViralReferralView: View {
    @Environment(\.theme) var theme
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @State private var referralCode = "VIBE2025"
    @State private var showingShareSheet = false
    @State private var copiedToClipboard = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    viralHeader
                    
                    // Referral stats
                    referralStats
                    
                    // Share section
                    shareSection
                    
                    // Rewards breakdown
                    rewardsBreakdown
                    
                    // Social sharing options
                    socialSharingOptions
                }
                .padding(16)
            }
            .navigationTitle("Invite Friends")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [shareMessage])
        }
    }
    
    // MARK: - Viral Header
    private var viralHeader: some View {
        VStack(spacing: 16) {
            // Animated money emojis
            HStack(spacing: 12) {
                Text("💰")
                    .font(.title)
                    .scaleEffect(1.2)
                Text("🚀")
                    .font(.title)
                    .scaleEffect(1.1)
                Text("💎")
                    .font(.title)
                    .scaleEffect(1.2)
            }
            
            VStack(spacing: 8) {
                Text("Get Paid to Share! 💅")
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.purple, .pink, .orange],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                
                Text("Invite your besties and earn rewards when they join the finance revolution! ✨")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [.purple.opacity(0.5), .pink.opacity(0.5)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
        )
    }
    
    // MARK: - Referral Stats
    private var referralStats: some View {
        HStack(spacing: 16) {
            ReferralStatCard(
                title: "Friends Invited",
                value: "12",
                emoji: "👥",
                color: .blue
            )

            ReferralStatCard(
                title: "Rewards Earned",
                value: "$48",
                emoji: "💰",
                color: .green
            )

            ReferralStatCard(
                title: "This Month",
                value: "5",
                emoji: "🔥",
                color: .orange
            )
        }
    }
    
    // MARK: - Share Section
    private var shareSection: some View {
        VStack(spacing: 16) {
            Text("Your Referral Code")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack {
                Text(referralCode)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.purple.opacity(0.1))
                            .stroke(.purple.opacity(0.3), lineWidth: 1)
                    )
                
                Button(action: copyToClipboard) {
                    Image(systemName: copiedToClipboard ? "checkmark" : "doc.on.doc")
                        .foregroundColor(.white)
                        .frame(width: 44, height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(copiedToClipboard ? .green : .purple)
                        )
                }
            }
            
            Button(action: { showingShareSheet = true }) {
                Text("Share with Friends! 🚀")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
            }
        }
    }
    
    // MARK: - Rewards Breakdown
    private var rewardsBreakdown: some View {
        VStack(spacing: 16) {
            Text("How It Works 💡")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                RewardStep(
                    step: "1",
                    title: "Share your code",
                    description: "Send your referral code to friends",
                    emoji: "📱"
                )
                
                RewardStep(
                    step: "2",
                    title: "They sign up",
                    description: "Friend joins using your code",
                    emoji: "✨"
                )
                
                RewardStep(
                    step: "3",
                    title: "You both get paid!",
                    description: "Both get $5 credit + premium features",
                    emoji: "💰"
                )
            }
        }
    }
    
    // MARK: - Social Sharing Options
    private var socialSharingOptions: some View {
        VStack(spacing: 16) {
            Text("Share On Social 📱")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 16) {
                SocialShareButton(
                    platform: "Instagram",
                    color: .pink,
                    icon: "camera.fill"
                ) {
                    // Share to Instagram Stories
                }
                
                SocialShareButton(
                    platform: "TikTok",
                    color: .black,
                    icon: "music.note"
                ) {
                    // Share to TikTok
                }
                
                SocialShareButton(
                    platform: "Snapchat",
                    color: .yellow,
                    icon: "camera.viewfinder"
                ) {
                    // Share to Snapchat
                }
            }
        }
    }
    
    // MARK: - Helper Functions
    private func copyToClipboard() {
        UIPasteboard.general.string = referralCode
        copiedToClipboard = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            copiedToClipboard = false
        }
    }
    
    private var shareMessage: String {
        "Hey bestie! 💅 I'm using VibeFinance to level up my money game and it's actually fun! Use my code \(referralCode) and we both get $5 + premium features! Download: https://vibefi.app 🚀✨"
    }
}

// MARK: - Supporting Views
struct ReferralStatCard: View {
    let title: String
    let value: String
    let emoji: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(emoji)
                .font(.title2)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct RewardStep: View {
    let step: String
    let title: String
    let description: String
    let emoji: String
    
    var body: some View {
        HStack(spacing: 16) {
            Text(step)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(.purple)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Text(emoji)
                        .font(.subheadline)
                }
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct SocialShareButton: View {
    let platform: String
    let color: Color
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 50, height: 50)
                    .background(
                        Circle()
                            .fill(color)
                    )
                
                Text(platform)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    ViralReferralView()
        .environmentObject(SubscriptionManager())
}
