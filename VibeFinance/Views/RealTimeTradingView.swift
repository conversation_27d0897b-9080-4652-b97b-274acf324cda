//
//  RealTimeTradingView.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI
import Charts

struct RealTimeTradingView: View {
    @Environment(\.theme) var theme
    @EnvironmentObject var realTradingManager: RealTradingManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @StateObject private var realTimeDataService = RealTimeDataService.shared
    @State private var selectedSymbol = "AAPL"
    @State private var showingOrderSheet = false
    @State private var showingConnectionSheet = false
    @State private var selectedTimeframe: TimeFrame = .oneDay
    @State private var isLiveMode = true
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header with live indicator
                    liveHeader
                    
                    // Connection status
                    connectionStatus
                    
                    // Real-time portfolio overview
                    if realTradingManager.isConnected {
                        portfolioOverview
                        
                        // Live market data
                        liveMarketData
                        
                        // Quick trade section
                        quickTradeSection
                        
                        // Recent orders
                        recentOrders
                    } else {
                        connectionPrompt
                    }
                }
                .padding(16)
            }
            .navigationTitle("Live Trading")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Connect Account") {
                            showingConnectionSheet = true
                        }
                        
                        Button("Refresh Data") {
                            Task {
                                await realTradingManager.loadRealPortfolio()
                            }
                        }
                        
                        Button(isLiveMode ? "Pause Live Updates" : "Resume Live Updates") {
                            isLiveMode.toggle()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingOrderSheet) {
                QuickOrderSheet(symbol: selectedSymbol)
            }
            .sheet(isPresented: $showingConnectionSheet) {
                BrokerageConnectionSheet()
            }
        }
        .onAppear {
            if realTradingManager.isConnected {
                Task {
                    await realTradingManager.loadRealPortfolio()
                }
            }
        }
    }
    
    // MARK: - Live Header
    private var liveHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Live Trading 🚀")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.green, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                
                Text("Real money, real gains! 💰")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Live indicator
            HStack(spacing: 6) {
                Circle()
                    .fill(isLiveMode ? .green : .orange)
                    .frame(width: 8, height: 8)
                    .scaleEffect(isLiveMode ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 1).repeatForever(), value: isLiveMode)
                
                Text(isLiveMode ? "LIVE" : "PAUSED")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(isLiveMode ? .green : .orange)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    // MARK: - Connection Status
    private var connectionStatus: some View {
        HStack {
            Image(systemName: realTradingManager.isConnected ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(realTradingManager.isConnected ? .green : .red)
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(realTradingManager.isConnected ? "Connected to Alpaca" : "Not Connected")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                if let accountInfo = realTradingManager.accountInfo {
                    Text("Account: \(accountInfo.accountNumber)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    Text("Connect your brokerage account to start trading")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if !realTradingManager.isConnected {
                Button("Connect") {
                    showingConnectionSheet = true
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(12)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Portfolio Overview
    private var portfolioOverview: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Portfolio Overview 📊")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                
                Button("View Details") {
                    // Navigate to detailed portfolio view
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            if let portfolio = realTradingManager.realPortfolio {
                HStack(spacing: 20) {
                    PortfolioMetricCard(
                        title: "Total Value",
                        value: portfolio.totalValue.formatted(.currency(code: "USD")),
                        change: portfolio.dayChange,
                        changePercent: portfolio.dayChangePercent,
                        icon: "dollarsign.circle.fill",
                        color: .green
                    )
                    
                    PortfolioMetricCard(
                        title: "Day P&L",
                        value: portfolio.dayChange.formatted(.currency(code: "USD")),
                        change: portfolio.dayChange,
                        changePercent: portfolio.dayChangePercent,
                        icon: "chart.line.uptrend.xyaxis.circle.fill",
                        color: portfolio.dayChange >= 0 ? .green : .red
                    )
                }
            } else {
                Text("Loading portfolio data...")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Live Market Data
    private var liveMarketData: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Live Market Data 📈")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("Symbol", selection: $selectedSymbol) {
                    Text("AAPL").tag("AAPL")
                    Text("TSLA").tag("TSLA")
                    Text("NVDA").tag("NVDA")
                    Text("MSFT").tag("MSFT")
                    Text("GOOGL").tag("GOOGL")
                }
                .pickerStyle(MenuPickerStyle())
            }
            
            // Real-time price chart would go here
            RealTimePriceChart(symbol: selectedSymbol, timeframe: selectedTimeframe)
                .frame(height: 200)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Quick Trade Section
    private var quickTradeSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Quick Trade ⚡")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if !subscriptionManager.canAccessFeature(.realInvestments) {
                    Text("Pro Only")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.orange.opacity(0.2))
                        )
                }
            }
            
            HStack(spacing: 12) {
                Button(action: {
                    showingOrderSheet = true
                }) {
                    HStack {
                        Image(systemName: "arrow.up.circle.fill")
                            .foregroundColor(.green)
                        Text("Buy")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.green.opacity(0.1))
                            .stroke(.green.opacity(0.3), lineWidth: 1)
                    )
                }
                .disabled(!subscriptionManager.canAccessFeature(.realInvestments))
                
                Button(action: {
                    showingOrderSheet = true
                }) {
                    HStack {
                        Image(systemName: "arrow.down.circle.fill")
                            .foregroundColor(.red)
                        Text("Sell")
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.red.opacity(0.1))
                            .stroke(.red.opacity(0.3), lineWidth: 1)
                    )
                }
                .disabled(!subscriptionManager.canAccessFeature(.realInvestments))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Recent Orders
    private var recentOrders: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recent Orders 📋")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to orders history
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            if realTradingManager.pendingOrders.isEmpty {
                Text("No recent orders")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 60)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(realTradingManager.pendingOrders.prefix(3), id: \.id) { order in
                        OrderRowView(order: order)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Connection Prompt
    private var connectionPrompt: some View {
        VStack(spacing: 20) {
            Image(systemName: "link.circle")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            VStack(spacing: 8) {
                Text("Connect Your Brokerage")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Link your Alpaca account to start live trading with real money! 🚀")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button("Connect Account") {
                showingConnectionSheet = true
            }
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                LinearGradient(
                    colors: [.blue, .purple],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
        }
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
        )
    }
}

enum TimeFrame: String, CaseIterable {
    case oneDay = "1D"
    case oneWeek = "1W"
    case oneMonth = "1M"
    case threeMonths = "3M"
    case oneYear = "1Y"
}

// MARK: - Supporting Views
struct PortfolioMetricCard: View {
    let title: String
    let value: String
    let change: Double
    let changePercent: Double
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)

                Spacer()

                Image(systemName: change >= 0 ? "arrow.up.right" : "arrow.down.right")
                    .foregroundColor(change >= 0 ? .green : .red)
                    .font(.caption)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title3)
                    .fontWeight(.bold)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack(spacing: 4) {
                    Text(change >= 0 ? "+" : "")
                    Text(change.formatted(.currency(code: "USD")))
                    Text("(\(changePercent.formatted(.number.precision(.fractionLength(2))))%)")
                }
                .font(.caption)
                .foregroundColor(change >= 0 ? .green : .red)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct RealTimePriceChart: View {
    let symbol: String
    let timeframe: TimeFrame
    @State private var priceData: [PricePoint] = []
    @State private var isLoading = true

    var body: some View {
        VStack {
            if isLoading {
                ProgressView("Loading chart data...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                Chart(priceData) { point in
                    LineMark(
                        x: .value("Time", point.timestamp),
                        y: .value("Price", point.price)
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.green, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .lineStyle(StrokeStyle(lineWidth: 2))
                }
                .chartYAxis {
                    AxisMarks(position: .trailing)
                }
                .chartXAxis {
                    AxisMarks(values: .stride(by: .hour, count: 2)) { _ in
                        AxisGridLine()
                        AxisTick()
                        AxisValueLabel(format: .dateTime.hour())
                    }
                }
            }
        }
        .onAppear {
            loadPriceData()
        }
        .onChange(of: symbol) {
            loadPriceData()
        }
        .onChange(of: timeframe) {
            loadPriceData()
        }
    }

    private func loadPriceData() {
        isLoading = true

        // Simulate loading real-time data
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            priceData = generateMockPriceData()
            isLoading = false
        }
    }

    private func generateMockPriceData() -> [PricePoint] {
        let basePrice = Double.random(in: 150...200)
        var data: [PricePoint] = []
        let now = Date()

        for i in 0..<24 {
            let timestamp = Calendar.current.date(byAdding: .hour, value: -i, to: now) ?? now
            let price = basePrice + Double.random(in: -10...10)
            data.append(PricePoint(timestamp: timestamp, price: price))
        }

        return data.reversed()
    }
}

struct PricePoint: Identifiable {
    let id = UUID()
    let timestamp: Date
    let price: Double
}

struct OrderRowView: View {
    let order: RealOrder

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(order.symbol)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Text(order.side.rawValue.uppercased())
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(order.side.rawValue == "buy" ? .green : .red)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill((order.side.rawValue == "buy" ? Color.green : Color.red).opacity(0.2))
                        )
                }

                Text("\(order.qty.formatted()) shares")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text(order.status.capitalized)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(statusColor(order.status))

                if let filledPrice = order.filledAvgPrice {
                    Text(filledPrice.formatted(.currency(code: "USD")))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(.ultraThinMaterial)
        )
    }

    private func statusColor(_ status: String) -> Color {
        switch status.lowercased() {
        case "filled": return .green
        case "pending", "new": return .orange
        case "cancelled", "rejected": return .red
        default: return .secondary
        }
    }
}

#Preview {
    RealTimeTradingView()
        .environmentObject(RealTradingManager())
        .environmentObject(SubscriptionManager())
}
