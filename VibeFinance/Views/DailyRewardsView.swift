//
//  DailyRewardsView.swift
//  VibeFinance - Daily Rewards & Streak System
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Models
struct DailyReward: Identifiable, Codable {
    let id: String
    let day: Int
    let type: RewardType
    let amount: Int
    let title: String
    let description: String
    let rarity: RewardRarity
    let icon: String
    var claimed: Bool
    var locked: Bool
    
    enum RewardType: String, CaseIterable, Codable {
        case coins = "coins"
        case boost = "boost"
        case premium = "premium"
        case special = "special"
    }
    
    enum RewardRarity: String, CaseIterable, Codable {
        case common = "common"
        case rare = "rare"
        case epic = "epic"
        case legendary = "legendary"
        
        var colors: [Color] {
            switch self {
            case .common: return [.gray.opacity(0.6), .gray]
            case .rare: return [.blue.opacity(0.6), .blue]
            case .epic: return [.purple.opacity(0.6), .purple]
            case .legendary: return [.yellow.opacity(0.6), .orange]
            }
        }
        
        var glowColor: Color {
            switch self {
            case .common: return .gray.opacity(0.3)
            case .rare: return .blue.opacity(0.4)
            case .epic: return .purple.opacity(0.5)
            case .legendary: return .yellow.opacity(0.6)
            }
        }
    }
}

struct UserRewardStats: Codable {
    var currentStreak: Int
    var longestStreak: Int
    var totalRewards: Int
    var freezeTokens: Int
    var level: Int
    var xp: Int
    var nextLevelXp: Int
}

// MARK: - Main Daily Rewards View
struct DailyRewardsView: View {
    @StateObject private var rewardsManager = DailyRewardsManager()
    @State private var selectedReward: DailyReward?
    @State private var showClaimAnimation = false
    @State private var animatingReward: DailyReward?
    @State private var particlesVisible = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Stats Header
                    statsHeader
                    
                    // Title Section
                    titleSection
                    
                    // Rewards Grid
                    rewardsGrid
                    
                    // Action Buttons
                    actionButtons
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
            }
            .background(
                ZStack {
                    // Background Gradient
                    LinearGradient(
                        colors: [
                            Color.black.opacity(0.9),
                            Color.purple.opacity(0.3),
                            Color.black.opacity(0.9)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .ignoresSafeArea()
                    
                    // Animated Particles
                    if particlesVisible {
                        ParticleSystemView()
                            .opacity(0.3)
                    }
                }
            )
            .navigationTitle("Daily Rewards")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
        }
        .sheet(item: $selectedReward) { reward in
            RewardDetailSheet(
                reward: reward,
                onClaim: { claimReward(reward) },
                onDismiss: { selectedReward = nil }
            )
        }
        .overlay(
            // Claim Animation Overlay
            Group {
                if showClaimAnimation, let reward = animatingReward {
                    ClaimAnimationView(reward: reward) {
                        withAnimation {
                            showClaimAnimation = false
                            animatingReward = nil
                        }
                    }
                }
            }
        )
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0).delay(0.5)) {
                particlesVisible = true
            }
        }
    }
    
    // MARK: - Stats Header
    private var statsHeader: some View {
        VStack(spacing: 16) {
            // Main Stats Grid
            HStack(spacing: 16) {
                RewardStatCard(
                    icon: "flame.fill",
                    value: "\(rewardsManager.userStats.currentStreak)",
                    label: "Current Streak",
                    color: .orange
                )

                RewardStatCard(
                    icon: "trophy.fill",
                    value: "\(rewardsManager.userStats.longestStreak)",
                    label: "Best Streak",
                    color: .yellow
                )

                RewardStatCard(
                    icon: "dollarsign.circle.fill",
                    value: "\(rewardsManager.userStats.totalRewards)",
                    label: "Total Coins",
                    color: .green
                )

                RewardStatCard(
                    icon: "snowflake",
                    value: "\(rewardsManager.userStats.freezeTokens)",
                    label: "Freeze Tokens",
                    color: .blue
                )
            }
            
            // Level Progress
            VStack(spacing: 8) {
                HStack {
                    Text("Level \(rewardsManager.userStats.level)")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text("\(rewardsManager.userStats.xp)/\(rewardsManager.userStats.nextLevelXp) XP")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                ProgressView(
                    value: Double(rewardsManager.userStats.xp),
                    total: Double(rewardsManager.userStats.nextLevelXp)
                )
                .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                .scaleEffect(y: 1.5)
            }
            .padding(.top, 8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .background(.ultraThinMaterial)
        .clipShape(RoundedRectangle(cornerRadius: 20))
    }
    
    // MARK: - Title Section
    private var titleSection: some View {
        VStack(spacing: 12) {
            Text("Daily Rewards")
                .font(.system(size: 36, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.purple, .pink, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
            
            Text("Check in daily to earn coins, boosts, and exclusive rewards. Build your streak for bigger rewards! 🎁")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
    }
    
    // MARK: - Rewards Grid
    private var rewardsGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            ForEach(Array(rewardsManager.rewards.enumerated()), id: \.element.id) { index, reward in
                DailyRewardCard(
                    reward: reward,
                    isToday: reward.day == rewardsManager.currentDay,
                    animationDelay: Double(index) * 0.1
                ) {
                    selectedReward = reward
                }
            }
        }
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        VStack(spacing: 16) {
            // Claim Today's Reward Button
            if let todaysReward = rewardsManager.todaysReward, !todaysReward.claimed {
                Button(action: {
                    claimReward(todaysReward)
                }) {
                    HStack(spacing: 12) {
                        Image(systemName: "gift.fill")
                            .font(.headline)
                        Text("Claim Today's Reward")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Image(systemName: "chevron.right")
                            .font(.headline)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .shadow(color: .purple.opacity(0.4), radius: 8)
                }
                .scaleEffect(1.0)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: todaysReward.claimed)
            }
            
            // Use Freeze Token Button
            Button(action: {
                rewardsManager.useFreezeToken()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "snowflake")
                        .font(.headline)
                    Text("Use Freeze Token (\(rewardsManager.userStats.freezeTokens))")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                )
            }
            .disabled(rewardsManager.userStats.freezeTokens == 0)
            .opacity(rewardsManager.userStats.freezeTokens == 0 ? 0.5 : 1.0)
        }
    }
    
    // MARK: - Actions
    private func claimReward(_ reward: DailyReward) {
        guard !reward.claimed && !reward.locked else { return }
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            animatingReward = reward
            showClaimAnimation = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            rewardsManager.claimReward(reward.id)
        }
    }
}

// MARK: - Supporting Components
struct RewardStatCard: View {
    let icon: String
    let value: String
    let label: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Text(label)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.6))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct DailyRewardCard: View {
    let reward: DailyReward
    let isToday: Bool
    let animationDelay: Double
    let onTap: () -> Void

    @State private var isVisible = false
    @State private var isPulsing = false

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Day Number
                Text("Day \(reward.day)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.7))

                // Icon
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: reward.rarity.colors,
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 48, height: 48)
                        .shadow(color: reward.rarity.glowColor, radius: isPulsing ? 8 : 4)

                    if reward.locked {
                        Image(systemName: "lock.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                    } else {
                        Image(systemName: reward.icon)
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                }

                // Title and Description
                VStack(spacing: 4) {
                    Text(reward.title)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .lineLimit(1)

                    Text(reward.description)
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        reward.claimed ?
                        Color.green.opacity(0.2) :
                        Color.white.opacity(0.1)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                reward.claimed ?
                                Color.green.opacity(0.4) :
                                reward.rarity.glowColor,
                                lineWidth: 1
                            )
                    )
            )
            .overlay(
                // Status Indicators
                Group {
                    if reward.claimed {
                        VStack {
                            HStack {
                                Spacer()
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.title3)
                                    .foregroundColor(.green)
                                    .background(Circle().fill(Color.black))
                            }
                            Spacer()
                        }
                        .padding(8)
                    } else if isToday && !reward.locked {
                        VStack {
                            HStack {
                                Image(systemName: "sparkles")
                                    .font(.caption)
                                    .foregroundColor(.yellow)
                                    .background(Circle().fill(Color.purple).frame(width: 20, height: 20))
                                Spacer()
                            }
                            Spacer()
                        }
                        .padding(8)
                    }
                }
            )
            .scaleEffect(isVisible ? 1.0 : 0.8)
            .opacity(isVisible ? 1.0 : 0.0)
            .scaleEffect(isPulsing && isToday ? 1.05 : 1.0)
            .disabled(reward.locked)
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            withAnimation(
                .spring(response: 0.6, dampingFraction: 0.8)
                .delay(animationDelay)
            ) {
                isVisible = true
            }

            if isToday && !reward.claimed {
                withAnimation(
                    .easeInOut(duration: 2.0)
                    .repeatForever(autoreverses: true)
                ) {
                    isPulsing = true
                }
            }
        }
    }
}
