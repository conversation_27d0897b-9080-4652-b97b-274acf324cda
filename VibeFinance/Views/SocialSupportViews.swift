//
//  SocialSupportViews.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI

// MARK: - Enhanced Squad Card
struct EnhancedSquadCard: View {
    let squad: Squad
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                // Squad emoji and info
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(squad.emoji)
                            .font(.title2)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(squad.name)
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Text("\(squad.members.count) members")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    Text(squad.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // Performance indicator
                VStack(alignment: .trailing, spacing: 4) {
                    Text("+\(String(format: "%.1f", squad.performancePercentage))%")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(squad.performancePercentage >= 0 ? .green : .red)
                    
                    HStack {
                        Image(systemName: "person.2.fill")
                            .font(.caption)
                        Text("\(squad.members.count)")
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(.purple.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
}

// MARK: - Trending Squad Card
struct TrendingSquadCard: View {
    let squad: Squad
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Trending indicator
                HStack {
                    Image(systemName: "flame.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    Text("TRENDING")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Spacer()
                }
                
                // Squad info
                VStack(spacing: 8) {
                    Text(squad.emoji)
                        .font(.largeTitle)
                    
                    Text(squad.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text("\(squad.members.count) members")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("+\(String(format: "%.1f", squad.performancePercentage))%")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.green.opacity(0.2))
                        )
                }
            }
            .frame(width: 140, height: 160)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(.orange.opacity(0.5), lineWidth: 2)
                    )
            )
        }
    }
}

// MARK: - Recommended Squad Card
struct RecommendedSquadCard: View {
    let squad: Squad
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                // Squad info
                HStack {
                    Text(squad.emoji)
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(squad.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(squad.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                        
                        HStack {
                            Image(systemName: "person.2.fill")
                                .font(.caption)
                            Text("\(squad.members.count) members")
                                .font(.caption)
                            
                            Spacer()
                            
                            Text("95% match")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.purple)
                        }
                        .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // Join button
                Button("Join") {
                    // Handle join action
                }
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.purple)
                )
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(.purple.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
}

// MARK: - Empty Squads View
struct EmptySquadsView: View {
    let onCreateSquad: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.3.fill")
                .font(.system(size: 50))
                .foregroundColor(.purple.opacity(0.6))
            
            Text("No Squads Yet")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("Join or create your first investment squad to start collaborating with others!")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: onCreateSquad) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Create Squad")
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.purple)
                )
            }
        }
        .frame(maxWidth: .infinity)
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.purple.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(.purple.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Live Activity Feed
struct LiveActivityFeed: View {
    let activities: [SocialActivity]
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Live Activity 📡")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Circle()
                    .fill(.green)
                    .frame(width: 8, height: 8)
                    .scaleEffect(1.0)
                    .animation(.easeInOut(duration: 1).repeatForever(), value: true)
            }
            
            LazyVStack(spacing: 12) {
                ForEach(activities, id: \.id) { activity in
                    ActivityRow(activity: activity)
                }
            }
        }
    }
}

struct ActivityRow: View {
    let activity: SocialActivity
    
    var body: some View {
        HStack {
            Image(systemName: activity.icon)
                .foregroundColor(.purple)
                .font(.title3)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text(activity.user)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Text(activity.action)
                        .font(.subheadline)
                    Text(activity.target)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.purple)
                }
                
                Text(activity.timestamp.timeAgoDisplay)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

// MARK: - Social Achievements Section
struct SocialAchievementsSection: View {
    let achievements: [SocialAchievement]
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Social Achievements 🏆")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to all achievements
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(achievements, id: \.id) { achievement in
                        SocialAchievementBadge(achievement: achievement)
                    }
                }
                .padding(.horizontal, 16)
            }
        }
    }
}

struct SocialAchievementBadge: View {
    let achievement: SocialAchievement
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(achievement.color.opacity(0.2))
                    .frame(width: 60, height: 60)
                
                Image(systemName: achievement.icon)
                    .font(.title2)
                    .foregroundColor(achievement.color)
                
                if achievement.isUnlocked {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.caption)
                        .background(Circle().fill(.white))
                        .offset(x: 20, y: -20)
                }
            }
            
            Text(achievement.title)
                .font(.caption)
                .fontWeight(.semibold)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(width: 80)
        .opacity(achievement.isUnlocked ? 1.0 : 0.6)
    }
}

// MARK: - Squad Leaderboard View
struct SquadLeaderboardView: View {
    let leaderboards: [SquadLeaderboard]
    
    var body: some View {
        VStack(spacing: 16) {
            ForEach(leaderboards, id: \.id) { leaderboard in
                VStack(spacing: 12) {
                    HStack {
                        Text(leaderboard.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Spacer()
                        
                        Text(leaderboard.period)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    LazyVStack(spacing: 8) {
                        ForEach(leaderboard.squads, id: \.id) { ranking in
                            SquadRankingRow(ranking: ranking)
                        }
                    }
                }
            }
        }
    }
}

struct SquadRankingRow: View {
    let ranking: SquadRanking
    
    var body: some View {
        HStack {
            // Rank
            Text("#\(ranking.rank)")
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30, alignment: .leading)
            
            // Squad name
            Text(ranking.squad)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
            
            // Members
            HStack {
                Image(systemName: "person.2.fill")
                    .font(.caption)
                Text("\(ranking.members)")
                    .font(.caption)
            }
            .foregroundColor(.secondary)
            
            // Performance
            Text(ranking.performance)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.green)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
    
    private var rankColor: Color {
        switch ranking.rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .secondary
        }
    }
}

// MARK: - Extensions
extension Date {
    var timeAgoDisplay: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: self, relativeTo: Date())
    }
}

// MARK: - Social Rankings View
struct SocialRankingsView: View {
    let rankings: [SocialRanking]

    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Social Rankings 🌟")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("This Week")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            LazyVStack(spacing: 8) {
                ForEach(rankings, id: \.id) { ranking in
                    SocialRankingRow(ranking: ranking)
                }
            }
        }
    }
}

struct SocialRankingRow: View {
    let ranking: SocialRanking

    var body: some View {
        HStack {
            // Rank and badge
            HStack {
                Text("#\(ranking.rank)")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(rankColor)
                    .frame(width: 30, alignment: .leading)

                Text(ranking.badge)
                    .font(.title3)
            }

            // Username
            Text(ranking.username)
                .font(.subheadline)
                .fontWeight(.medium)

            Spacer()

            // Score and change
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(ranking.score)")
                    .font(.subheadline)
                    .fontWeight(.bold)

                Text(ranking.change)
                    .font(.caption)
                    .foregroundColor(ranking.change.hasPrefix("+") ? .green : .red)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }

    private var rankColor: Color {
        switch ranking.rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .secondary
        }
    }
}

// MARK: - Discover Squads View
struct DiscoverSquadsView: View {
    let featuredSquads: [Squad]
    let categories: [SquadCategory]

    var body: some View {
        VStack(spacing: 20) {
            // Featured squads
            featuredSquadsSection

            // Categories
            categoriesSection
        }
    }

    private var featuredSquadsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Featured Squads ⭐")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            LazyVStack(spacing: 12) {
                ForEach(featuredSquads, id: \.id) { squad in
                    FeaturedSquadCard(squad: squad)
                }
            }
        }
    }

    private var categoriesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Browse Categories 📂")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(categories, id: \.id) { category in
                    CategoryCard(category: category)
                }
            }
        }
    }
}

struct FeaturedSquadCard: View {
    let squad: Squad

    var body: some View {
        HStack {
            // Featured badge
            VStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
                    .font(.title3)
                Text("FEATURED")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.yellow)
            }
            .frame(width: 60)

            // Squad info
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(squad.emoji)
                        .font(.title2)
                    Text(squad.name)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }

                Text(squad.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)

                HStack {
                    Text("\(squad.members.count) members")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()

                    Text("+\(String(format: "%.1f", squad.performancePercentage))%")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }
            }

            Spacer()

            Button("Join") {
                // Handle join action
            }
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.yellow)
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.yellow.opacity(0.5), lineWidth: 2)
                )
        )
    }
}

struct CategoryCard: View {
    let category: SquadCategory

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: category.icon)
                .foregroundColor(category.color)
                .font(.title2)

            Text(category.name)
                .font(.subheadline)
                .fontWeight(.semibold)

            Text("\(category.squadCount) squads")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(category.color.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(category.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Social Challenges View
struct SocialChallengesView: View {
    let challenges: [SocialChallenge]

    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Social Challenges 🎯")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("Limited Time")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.orange.opacity(0.2))
                    )
            }

            LazyVStack(spacing: 12) {
                ForEach(challenges, id: \.id) { challenge in
                    EnhancedSocialChallengeCard(challenge: challenge)
                }
            }
        }
    }
}

struct EnhancedSocialChallengeCard: View {
    let challenge: SocialChallenge

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(challenge.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)

                    Text(challenge.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text(challenge.reward)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.purple)

                    Text("\(challenge.participants) joined")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Progress
            VStack(spacing: 8) {
                HStack {
                    Text("Progress: \(challenge.progress)/\(challenge.target)")
                        .font(.caption)
                        .fontWeight(.medium)

                    Spacer()

                    Text(challenge.deadline.timeUntilDisplay)
                        .font(.caption)
                        .foregroundColor(.orange)
                }

                ProgressView(value: challenge.progressPercentage)
                    .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                    .scaleEffect(y: 2)
            }

            if challenge.isCompleted {
                Text("✅ Completed!")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(.purple.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Extensions
extension Date {
    var timeUntilDisplay: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .short
        return formatter.localizedString(for: Date(), relativeTo: self)
    }
}

extension Squad {
    var performancePercentage: Double {
        // Mock performance calculation
        return Double.random(in: -5.0...15.0)
    }
}
