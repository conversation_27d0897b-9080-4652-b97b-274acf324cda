//
//  BuffettQuestsView.swift
//  VibeFinance - <PERSON> Inspired Financial Education
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct BuffettQuestsView: View {
    @State private var dailyQuests: [BuffettQuest] = []
    @State private var weeklyChallenge: BuffettQuest?
    @State private var userXP = 2847
    @State private var userLevel = 12
    @State private var streakDays = 23
    @Environment(\.theme) var theme
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // <PERSON>'s Progress Card
                BuffettProgressCard(
                    xp: userXP,
                    level: userLevel,
                    streakDays: streakDays
                )
                .padding(.horizontal, 16)
                
                // Weekly Buffett Challenge
                if let challenge = weeklyChallenge {
                    BuffettChallengeCard(quest: challenge)
                        .padding(.horizontal, 16)
                }
                
                // Daily Quests Section
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("📚 Today's Wealth Building Lessons")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(theme.onBackground)

                        Spacer()

                        Text("\(dailyQuests.filter { $0.isCompleted }.count)/\(dailyQuests.count)")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(theme.accent)
                    }
                    .padding(.horizontal, 16)
                    
                    ForEach(dailyQuests) { quest in
                        BuffettQuestCard(quest: quest) {
                            completeQuest(quest)
                        }
                        .padding(.horizontal, 16)
                    }
                }
            }
            .padding(.vertical, 16)
        }
        .onAppear {
            loadMockQuestData()
        }
    }
    
    private func loadMockQuestData() {
        dailyQuests = BuffettQuest.mockDailyQuests
        weeklyChallenge = BuffettQuest.mockWeeklyChallenge
    }
    
    private func completeQuest(_ quest: BuffettQuest) {
        if let index = dailyQuests.firstIndex(where: { $0.id == quest.id }) {
            dailyQuests[index].isCompleted = true
            userXP += quest.xpReward
            
            // Check for level up
            let newLevel = (userXP / 250) + 1
            if newLevel > userLevel {
                userLevel = newLevel
                // Show level up animation
            }
        }
    }
}

// MARK: - Buffett Progress Card
struct BuffettProgressCard: View {
    let xp: Int
    let level: Int
    let streakDays: Int
    @Environment(\.theme) var theme
    
    var progressToNextLevel: Double {
        let currentLevelXP = (level - 1) * 250
        let nextLevelXP = level * 250
        let progress = Double(xp - currentLevelXP) / Double(nextLevelXP - currentLevelXP)
        return min(max(progress, 0), 1)
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("🎓 Wealth Builder Level \(level)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)

                    Text("Following Warren's principles")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(xp) XP")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(theme.accent)

                    Text("\(streakDays) day streak 🔥")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.warning)
                }
            }
                
            // Progress bar
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Progress to Level \(level + 1)")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))

                    Spacer()

                    Text("\(Int(progressToNextLevel * 100))%")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.accent)
                }

                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(theme.onSurface.opacity(0.2))
                        .frame(height: 8)

                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [theme.accent, theme.warning],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: max(8, progressToNextLevel * UIScreen.main.bounds.width * 0.8), height: 8)
                }
            }
        }
        .padding(20)
        .glassmorphicCard(theme: theme, cornerRadius: 20)
    }
}

// MARK: - Buffett Challenge Card
struct BuffettChallengeCard: View {
    let quest: BuffettQuest
    @Environment(\.theme) var theme

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("👑 Warren's Weekly Challenge")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(theme.accent)
                    
                    Spacer()
                    
                    Text("5 days left")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }

                Text(quest.title)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(theme.onSurface)

                Text(quest.description)
                    .font(.subheadline)
                    .foregroundColor(theme.onSurface.opacity(0.8))

                HStack {
                    Text("🏆 \(quest.xpReward) XP")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.accent)

                    Spacer()

                    Button(action: {}) {
                        Text("Start Challenge")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(theme.accent)
                            )
                    }
                }
            }
            .padding(16)
        }
        .glassmorphicCard(theme: theme)
    }
}

// MARK: - Buffett Quest Card
struct BuffettQuestCard: View {
    let quest: BuffettQuest
    let onComplete: () -> Void
    @Environment(\.theme) var theme

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            
            HStack(spacing: 12) {
                // Quest icon and difficulty
                VStack(spacing: 4) {
                    Text(quest.emoji)
                        .font(.title2)
                    
                    HStack(spacing: 2) {
                        ForEach(0..<quest.difficulty, id: \.self) { _ in
                            Circle()
                                .fill(theme.accent)
                                .frame(width: 4, height: 4)
                        }
                    }
                }

                // Quest content
                VStack(alignment: .leading, spacing: 4) {
                    Text(quest.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface)
                        .strikethrough(quest.isCompleted)

                    Text(quest.description)
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                        .lineLimit(2)

                    Text("🏆 \(quest.xpReward) XP")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.accent)
                }

                Spacer()

                // Complete button
                Button(action: onComplete) {
                    Image(systemName: quest.isCompleted ? "checkmark.circle.fill" : "circle")
                        .font(.title2)
                        .foregroundColor(quest.isCompleted ? theme.success : theme.onSurface.opacity(0.6))
                }
                .disabled(quest.isCompleted)
            }
            .padding(12)
        }
        .glassmorphicCard(theme: theme)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(
                    quest.isCompleted ? theme.success.opacity(0.5) : Color.clear,
                    lineWidth: quest.isCompleted ? 2 : 0
                )
        )
    }
}

// MARK: - Buffett Quest Model
struct BuffettQuest: Identifiable {
    let id = UUID()
    let emoji: String
    let title: String
    let description: String
    let difficulty: Int // 1-3 stars
    let xpReward: Int
    var isCompleted: Bool = false
    
    static let mockDailyQuests: [BuffettQuest] = [
        BuffettQuest(
            emoji: "📖",
            title: "Read Warren's Letter",
            description: "Read a section from Berkshire Hathaway's annual shareholder letter",
            difficulty: 2,
            xpReward: 50
        ),
        BuffettQuest(
            emoji: "🔍",
            title: "Analyze a Company",
            description: "Research a company's fundamentals using Warren's criteria",
            difficulty: 3,
            xpReward: 75
        ),
        BuffettQuest(
            emoji: "💰",
            title: "Calculate Intrinsic Value",
            description: "Practice valuing a stock using discounted cash flow",
            difficulty: 3,
            xpReward: 100
        ),
        BuffettQuest(
            emoji: "📊",
            title: "Study Financial Ratios",
            description: "Learn about ROE, debt-to-equity, and profit margins",
            difficulty: 1,
            xpReward: 25
        )
    ]
    
    static let mockWeeklyChallenge: BuffettQuest = BuffettQuest(
        emoji: "🏆",
        title: "Build Your First Portfolio",
        description: "Create a diversified portfolio of 5-10 stocks following Warren Buffett's investment principles",
        difficulty: 3,
        xpReward: 500
    )
}

// MARK: - Buffett Squads View
struct BuffettSquadsView: View {
    @State private var featuredSquads: [BuffettSquad] = []
    @State private var userSquads: [BuffettSquad] = []
    @State private var showingCreateSquad = false
    @Environment(\.theme) var theme

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Warren's Investment Philosophy Card
                BuffettPhilosophyCard()
                    .padding(.horizontal, 16)

                // User's Squads
                if !userSquads.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("👥 Your Investment Circles")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(theme.onBackground)
                            .padding(.horizontal, 16)

                        ForEach(userSquads) { squad in
                            BuffettSquadCard(squad: squad, isJoined: true)
                                .padding(.horizontal, 16)
                        }
                    }
                }

                // Featured Squads
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("🌟 Warren-Inspired Investment Squads")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(theme.onBackground)

                        Spacer()

                        Button(action: { showingCreateSquad = true }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.title2)
                                .foregroundColor(theme.accent)
                        }
                    }
                    .padding(.horizontal, 16)

                    ForEach(featuredSquads) { squad in
                        BuffettSquadCard(squad: squad, isJoined: false)
                            .padding(.horizontal, 16)
                    }
                }
            }
            .padding(.vertical, 16)
        }
        .onAppear {
            loadMockSquadData()
        }
        .sheet(isPresented: $showingCreateSquad) {
            CreateSquadView()
        }
    }

    private func loadMockSquadData() {
        featuredSquads = BuffettSquad.mockFeaturedSquads
        userSquads = BuffettSquad.mockUserSquads
    }
}

// MARK: - Buffett Philosophy Card
struct BuffettPhilosophyCard: View {
    @Environment(\.theme) var theme

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🧠 Warren's Investment Philosophy")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(theme.onSurface)

            Text("\"Wide diversification is only required when investors do not understand what they are doing.\"")
                .font(.subheadline)
                .foregroundColor(theme.onSurface.opacity(0.9))
                .italic()

            Text("Join squads that focus on understanding businesses deeply rather than spreading investments thin.")
                .font(.caption)
                .foregroundColor(theme.onSurface.opacity(0.7))
        }
        .padding(16)
        .glassmorphicCard(theme: theme, cornerRadius: 20)
    }
}

// MARK: - Buffett Squad Card
struct BuffettSquadCard: View {
    let squad: BuffettSquad
    let isJoined: Bool
    @State private var showingDetail = false
    @Environment(\.theme) var theme

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(squad.emoji)
                    .font(.title)

                VStack(alignment: .leading, spacing: 2) {
                    Text(squad.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)

                    Text(squad.focus)
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(squad.memberCount)")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(theme.accent)

                    Text("members")
                        .font(.caption2)
                        .foregroundColor(theme.onSurface.opacity(0.6))
                }
            }

            Text(squad.description)
                .font(.subheadline)
                .foregroundColor(theme.onSurface.opacity(0.8))
                .lineLimit(2)

            HStack {
                Text("📈 \(squad.performance)")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(theme.success)

                Spacer()

                Button(action: { showingDetail = true }) {
                    Text(isJoined ? "View Squad" : "Join Squad")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(isJoined ? theme.primary : theme.accent)
                        )
                }
            }
        }
        .padding(16)
        .glassmorphicCard(theme: theme, cornerRadius: 16)
        .onTapGesture {
            showingDetail = true
        }
    }
}

// MARK: - Buffett Squad Model
struct BuffettSquad: Identifiable {
    let id = UUID()
    let emoji: String
    let name: String
    let focus: String
    let description: String
    let memberCount: Int
    let performance: String

    static let mockFeaturedSquads: [BuffettSquad] = [
        BuffettSquad(
            emoji: "🏦",
            name: "Value Hunters",
            focus: "Deep Value Investing",
            description: "Finding undervalued companies with strong fundamentals, just like Warren taught us.",
            memberCount: 1247,
            performance: "+23.4% this year"
        ),
        BuffettSquad(
            emoji: "🛡️",
            name: "Moat Defenders",
            focus: "Economic Moats",
            description: "Investing in companies with wide economic moats and sustainable competitive advantages.",
            memberCount: 892,
            performance: "+18.7% this year"
        ),
        BuffettSquad(
            emoji: "💎",
            name: "Forever Holdings",
            focus: "Buy & Hold Forever",
            description: "Building a portfolio of wonderful companies at fair prices for the long term.",
            memberCount: 2156,
            performance: "+31.2% this year"
        )
    ]

    static let mockUserSquads: [BuffettSquad] = [
        BuffettSquad(
            emoji: "🎓",
            name: "Berkshire Disciples",
            focus: "Learning from the Master",
            description: "Young investors learning Warren Buffett's investment principles together.",
            memberCount: 456,
            performance: "+15.8% this year"
        )
    ]
}

#Preview {
    BuffettQuestsView()
}
