//
//  SubscriptionManagementView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct SubscriptionManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var showingCancelConfirmation = false
    @State private var showingPauseConfirmation = false
    @State private var showingChangePlan = false
    @State private var showingPaymentMethods = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Current Subscription Details
                    currentSubscriptionSection
                    
                    // Subscription Actions
                    subscriptionActionsSection
                    
                    // Payment Methods
                    paymentMethodsSection
                    
                    // Usage Statistics
                    usageStatisticsSection
                    
                    // Support Section
                    supportSection
                }
                .padding()
            }
            .navigationTitle("Manage Subscription")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .alert("Cancel Subscription", isPresented: $showingCancelConfirmation) {
                Button("Keep Subscription", role: .cancel) { }
                But<PERSON>("Cancel", role: .destructive) {
                    Task {
                        await cancelSubscription()
                    }
                }
            } message: {
                Text("Are you sure you want to cancel your subscription? You'll lose access to Pro features at the end of your current billing period.")
            }
            .alert("Pause Subscription", isPresented: $showingPauseConfirmation) {
                Button("Keep Active", role: .cancel) { }
                Button("Pause", role: .destructive) {
                    Task {
                        await pauseSubscription()
                    }
                }
            } message: {
                Text("Your subscription will be paused and you can resume it anytime. You won't be charged during the pause period.")
            }
            .sheet(isPresented: $showingChangePlan) {
                EnhancedSubscriptionView()
            }
            .sheet(isPresented: $showingPaymentMethods) {
                PaymentMethodsManagementView()
            }
            .onAppear {
                Task {
                    await paymentManager.loadSubscriptionDetails()
                    await paymentManager.loadPaymentMethods()
                }
            }
        }
    }
    
    // MARK: - Current Subscription Section
    private var currentSubscriptionSection: some View {
        VStack(spacing: 16) {
            Text("Current Subscription")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if let details = paymentManager.subscriptionDetails,
               let subscription = details.currentSubscription {
                
                VStack(spacing: 16) {
                    // Plan Info
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(subscription.tier.displayName)
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Text(details.billingCycle.displayName)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 4) {
                            HStack(spacing: 4) {
                                Circle()
                                    .fill(Color(subscription.status.color))
                                    .frame(width: 8, height: 8)
                                Text(subscription.status.displayName)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }
                            
                            if subscription.cancelAtPeriodEnd {
                                Text("Expires \(subscription.currentPeriodEnd, style: .date)")
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    
                    // Billing Info
                    VStack(spacing: 8) {
                        HStack {
                            Text("Current Period")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("\(subscription.currentPeriodStart, style: .date) - \(subscription.currentPeriodEnd, style: .date)")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        
                        if let nextBilling = details.nextBillingDate {
                            HStack {
                                Text("Next Billing")
                                    .foregroundColor(.secondary)
                                Spacer()
                                Text(nextBilling, style: .date)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }
                        }
                        
                        HStack {
                            Text("Amount")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("$\(subscription.tier.monthlyPrice, specifier: "%.2f")/month")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.purple)
                        }
                    }
                    
                    // Renewal Warning
                    if subscription.isExpiringSoon && !subscription.cancelAtPeriodEnd {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                            Text("Your subscription renews in \(details.daysUntilRenewal ?? 0) days")
                                .font(.caption)
                                .foregroundColor(.orange)
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.orange.opacity(0.1))
                        )
                    }
                }
            } else {
                Text("No active subscription")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Subscription Actions Section
    private var subscriptionActionsSection: some View {
        VStack(spacing: 16) {
            Text("Subscription Actions")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                ActionButton(
                    icon: "arrow.up.circle.fill",
                    title: "Change Plan",
                    description: "Upgrade or downgrade your subscription",
                    color: .blue
                ) {
                    showingChangePlan = true
                }
                
                if let subscription = paymentManager.subscriptionDetails?.currentSubscription,
                   subscription.status == .active {
                    
                    ActionButton(
                        icon: "pause.circle.fill",
                        title: "Pause Subscription",
                        description: "Temporarily pause your subscription",
                        color: .orange
                    ) {
                        showingPauseConfirmation = true
                    }
                    
                    ActionButton(
                        icon: "xmark.circle.fill",
                        title: "Cancel Subscription",
                        description: "Cancel at the end of current period",
                        color: .red
                    ) {
                        showingCancelConfirmation = true
                    }
                    
                } else if let subscription = paymentManager.subscriptionDetails?.currentSubscription,
                          subscription.status == .paused {
                    
                    ActionButton(
                        icon: "play.circle.fill",
                        title: "Resume Subscription",
                        description: "Resume your paused subscription",
                        color: .green
                    ) {
                        Task {
                            await resumeSubscription()
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Payment Methods Section
    private var paymentMethodsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Payment Methods")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Button("Manage") {
                    showingPaymentMethods = true
                }
                .font(.subheadline)
                .foregroundColor(.purple)
            }
            
            if paymentManager.paymentMethods.isEmpty {
                Text("No payment methods added")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                VStack(spacing: 8) {
                    ForEach(paymentManager.paymentMethods.prefix(2)) { method in
                        PaymentMethodSummaryRow(method: method)
                    }
                    
                    if paymentManager.paymentMethods.count > 2 {
                        Text("+ \(paymentManager.paymentMethods.count - 2) more")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Usage Statistics Section
    private var usageStatisticsSection: some View {
        VStack(spacing: 16) {
            Text("Usage This Month")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                UsageStatCard(
                    title: "AI Chat Messages",
                    value: "247",
                    limit: "Unlimited",
                    color: .blue
                )
                
                UsageStatCard(
                    title: "Quests Completed",
                    value: "18",
                    limit: "Unlimited",
                    color: .green
                )
                
                UsageStatCard(
                    title: "Feed Posts Viewed",
                    value: "1,234",
                    limit: "Unlimited",
                    color: .purple
                )
                
                UsageStatCard(
                    title: "Trades Executed",
                    value: "12",
                    limit: "Unlimited",
                    color: .orange
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Support Section
    private var supportSection: some View {
        VStack(spacing: 16) {
            Text("Need Help?")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                SupportButton(
                    icon: "questionmark.circle.fill",
                    title: "FAQ",
                    description: "Common questions about subscriptions"
                ) {
                    // Open FAQ
                }
                
                SupportButton(
                    icon: "message.circle.fill",
                    title: "Contact Support",
                    description: "Get help from our support team"
                ) {
                    // Open support chat
                }
                
                SupportButton(
                    icon: "doc.text.fill",
                    title: "Terms & Privacy",
                    description: "Review our terms and privacy policy"
                ) {
                    // Open terms
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func cancelSubscription() async {
        // Implementation would cancel the subscription
    }
    
    private func pauseSubscription() async {
        let success = await paymentManager.pauseSubscription()
        if success {
            // Show success message
        }
    }
    
    private func resumeSubscription() async {
        let success = await paymentManager.resumeSubscription()
        if success {
            // Show success message
        }
    }
}

// MARK: - Supporting Components

struct ActionButton: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(color.opacity(0.1))
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct PaymentMethodSummaryRow: View {
    let method: PaymentMethod
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: method.displayIcon)
                .font(.title3)
                .foregroundColor(.purple)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(method.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                if let lastFour = method.lastFour {
                    Text("•••• \(lastFour)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if method.isDefault {
                Text("DEFAULT")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.purple.opacity(0.1))
                    )
            }
        }
    }
}

struct UsageStatCard: View {
    let title: String
    let value: String
    let limit: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text("of \(limit)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
        )
    }
}

struct SupportButton: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.blue)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.blue.opacity(0.1))
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Payment Methods Management View
struct PaymentMethodsManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var showingAddPaymentMethod = false
    
    var body: some View {
        NavigationView {
            VStack {
                if paymentManager.paymentMethods.isEmpty {
                    VStack(spacing: 20) {
                        Spacer()
                        
                        Image(systemName: "creditcard")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("No Payment Methods")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("Add a payment method to manage your subscription")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        Button("Add Payment Method") {
                            showingAddPaymentMethod = true
                        }
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.purple)
                        )
                        .padding(.horizontal)
                        
                        Spacer()
                    }
                } else {
                    List {
                        ForEach(paymentManager.paymentMethods) { method in
                            PaymentMethodDetailRow(method: method)
                        }
                        .onDelete(perform: deletePaymentMethod)
                    }
                }
            }
            .navigationTitle("Payment Methods")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        showingAddPaymentMethod = true
                    }
                }
            }
            .sheet(isPresented: $showingAddPaymentMethod) {
                AddPaymentMethodView()
            }
        }
    }
    
    private func deletePaymentMethod(at offsets: IndexSet) {
        for index in offsets {
            let method = paymentManager.paymentMethods[index]
            Task {
                await paymentManager.removePaymentMethod(method.id)
            }
        }
    }
}

struct PaymentMethodDetailRow: View {
    let method: PaymentMethod
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: method.displayIcon)
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.purple.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(method.displayName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                if let lastFour = method.lastFour {
                    Text("•••• \(lastFour)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                if let expiry = method.expiryDate {
                    Text("Expires \(expiry)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if method.isDefault {
                Text("DEFAULT")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.purple.opacity(0.1))
                    )
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    SubscriptionManagementView()
        .environmentObject(SubscriptionManager())
        .environmentObject(PaymentManager(subscriptionManager: SubscriptionManager()))
}
