//
//  RevenueDashboardView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct RevenueDashboardView: View {
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var revenueMetrics: RevenueMetrics?
    @State private var selectedTimeframe: RevenueTimeframe = .monthly
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Timeframe Selector
                    timeframeSelector
                    
                    // Key Metrics
                    if let metrics = revenueMetrics {
                        keyMetricsSection(metrics)
                        
                        // Revenue Breakdown
                        revenueBreakdownSection(metrics)
                        
                        // Subscription Analytics
                        subscriptionAnalyticsSection(metrics)
                        
                        // Growth Metrics
                        growthMetricsSection(metrics)
                    } else if isLoading {
                        loadingState
                    } else {
                        errorState
                    }
                }
                .padding()
            }
            .navigationTitle("Revenue Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await loadRevenueMetrics()
            }
            .onAppear {
                Task {
                    await loadRevenueMetrics()
                }
            }
        }
    }
    
    // MARK: - Timeframe Selector
    private var timeframeSelector: some View {
        HStack(spacing: 12) {
            ForEach(RevenueTimeframe.allCases, id: \.self) { timeframe in
                Button(action: {
                    selectedTimeframe = timeframe
                    Task {
                        await loadRevenueMetrics()
                    }
                }) {
                    Text(timeframe.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(selectedTimeframe == timeframe ? .white : .primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(selectedTimeframe == timeframe ? Color.purple : Color(.systemGray6))
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    // MARK: - Key Metrics Section
    private func keyMetricsSection(_ metrics: RevenueMetrics) -> some View {
        VStack(spacing: 16) {
            Text("Key Metrics")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                RevenueMetricCard(
                    title: "Monthly Recurring Revenue",
                    value: metrics.formattedMRR,
                    change: "+12.5%",
                    changeColor: .green,
                    icon: "chart.line.uptrend.xyaxis"
                )
                
                RevenueMetricCard(
                    title: "Annual Recurring Revenue",
                    value: metrics.formattedARR,
                    change: "+15.2%",
                    changeColor: .green,
                    icon: "calendar"
                )
                
                RevenueMetricCard(
                    title: "Average Revenue Per User",
                    value: metrics.formattedARPU,
                    change: "+3.1%",
                    changeColor: .green,
                    icon: "person.circle"
                )
                
                RevenueMetricCard(
                    title: "Customer Lifetime Value",
                    value: metrics.formattedLTV,
                    change: "+8.7%",
                    changeColor: .green,
                    icon: "heart.circle"
                )
            }
        }
    }
    
    // MARK: - Revenue Breakdown Section
    private func revenueBreakdownSection(_ metrics: RevenueMetrics) -> some View {
        VStack(spacing: 16) {
            Text("Revenue Breakdown")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                RevenueBreakdownRow(
                    tier: "Pro Subscriptions",
                    amount: "$1,498,500",
                    percentage: 75.0,
                    color: .purple
                )
                
                RevenueBreakdownRow(
                    tier: "Basic Subscriptions",
                    amount: "$499,750",
                    percentage: 25.0,
                    color: .blue
                )
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6))
            )
        }
    }
    
    // MARK: - Subscription Analytics Section
    private func subscriptionAnalyticsSection(_ metrics: RevenueMetrics) -> some View {
        VStack(spacing: 16) {
            Text("Subscription Analytics")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                AnalyticsCard(
                    title: "Conversion Rate",
                    value: "\(String(format: "%.1f", metrics.conversionRate))%",
                    subtitle: "Free to Paid",
                    color: .green
                )
                
                AnalyticsCard(
                    title: "Churn Rate",
                    value: "\(String(format: "%.1f", metrics.churnRate))%",
                    subtitle: "Monthly",
                    color: .red
                )
                
                AnalyticsCard(
                    title: "Active Subscribers",
                    value: "175,000",
                    subtitle: "Total",
                    color: .blue
                )
                
                AnalyticsCard(
                    title: "New Subscribers",
                    value: "12,500",
                    subtitle: "This Month",
                    color: .orange
                )
            }
        }
    }
    
    // MARK: - Growth Metrics Section
    private func growthMetricsSection(_ metrics: RevenueMetrics) -> some View {
        VStack(spacing: 16) {
            Text("Growth Metrics")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                GrowthMetricRow(
                    title: "Revenue Growth Rate",
                    value: "+15.2%",
                    period: "Month over Month",
                    color: .green
                )
                
                GrowthMetricRow(
                    title: "User Growth Rate",
                    value: "+22.8%",
                    period: "Month over Month",
                    color: .blue
                )
                
                GrowthMetricRow(
                    title: "Upgrade Rate",
                    value: "+8.5%",
                    period: "Basic to Pro",
                    color: .purple
                )
                
                GrowthMetricRow(
                    title: "Retention Rate",
                    value: "94.8%",
                    period: "12 Month",
                    color: .orange
                )
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6))
            )
        }
    }
    
    // MARK: - Loading State
    private var loadingState: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)
            
            Text("Loading revenue metrics...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Error State
    private var errorState: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.orange)
            
            Text("Failed to Load Metrics")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Unable to load revenue metrics. Please try again.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("Retry") {
                Task {
                    await loadRevenueMetrics()
                }
            }
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.purple)
            )
        }
        .padding()
    }
    
    // MARK: - Helper Methods
    private func loadRevenueMetrics() async {
        isLoading = true
        revenueMetrics = await paymentManager.getRevenueMetrics()
        isLoading = false
    }
}

// MARK: - Supporting Components

struct RevenueMetricCard: View {
    let title: String
    let value: String
    let change: String
    let changeColor: Color
    let icon: String
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.purple)
                Spacer()
                Text(change)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(changeColor)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

struct RevenueBreakdownRow: View {
    let tier: String
    let amount: String
    let percentage: Double
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(tier)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Spacer()
                Text(amount)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(color)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 8)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * (percentage / 100), height: 8)
                        .cornerRadius(4)
                        .animation(.easeInOut(duration: 0.5), value: percentage)
                }
            }
            .frame(height: 8)
        }
    }
}

struct AnalyticsCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(subtitle)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct GrowthMetricRow: View {
    let title: String
    let value: String
    let period: String
    let color: Color
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(period)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Revenue Timeframe
enum RevenueTimeframe: String, CaseIterable {
    case daily = "daily"
    case weekly = "weekly"
    case monthly = "monthly"
    case yearly = "yearly"
    
    var displayName: String {
        switch self {
        case .daily: return "Daily"
        case .weekly: return "Weekly"
        case .monthly: return "Monthly"
        case .yearly: return "Yearly"
        }
    }
}

#Preview {
    RevenueDashboardView()
        .environmentObject(PaymentManager(subscriptionManager: SubscriptionManager()))
}
