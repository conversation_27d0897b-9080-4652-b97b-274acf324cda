//
//  AdvancedGamificationView.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI

struct AdvancedGamificationView: View {
    @Environment(\.theme) var theme
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @StateObject private var gamificationManager = GamificationManager()
    @State private var selectedTab: GamificationTab = .overview
    @State private var showingThemeSelector = false
    @State private var showingAchievementDetail: GamificationAchievement?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with user stats
                gamificationHeader
                
                // Tab selector
                tabSelector
                
                // Content based on selected tab
                ScrollView {
                    VStack(spacing: 20) {
                        switch selectedTab {
                        case .overview:
                            overviewContent
                        case .achievements:
                            achievementsContent
                        case .rewards:
                            rewardsContent
                        case .leaderboard:
                            leaderboardContent
                        }
                    }
                    .padding(16)
                }
            }
            .navigationTitle("Level Up! 🚀")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                Task {
                    await gamificationManager.loadUserData()
                }
            }
            .sheet(isPresented: $showingThemeSelector) {
                ThemeSelectorView()
            }
            .sheet(item: $showingAchievementDetail) { achievement in
                GamificationAchievementDetailView(achievement: achievement)
            }
        }
    }
    
    // MARK: - Gamification Header
    private var gamificationHeader: some View {
        VStack(spacing: 16) {
            // User level and XP
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Level \(userManager.user?.level ?? 1)")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.purple, .pink, .orange],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                        
                        Image(systemName: "crown.fill")
                            .foregroundColor(.orange)
                            .font(.title2)
                    }
                    
                    Text("\(userManager.user?.xp ?? 0) XP")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Streak indicator
                VStack(alignment: .trailing, spacing: 4) {
                    HStack {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.orange)
                        Text("\(gamificationManager.currentStreak)")
                            .font(.title2)
                            .fontWeight(.bold)
                    }
                    
                    Text("Day Streak")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // XP Progress bar
            VStack(spacing: 8) {
                HStack {
                    Text("Progress to Level \((userManager.user?.level ?? 1) + 1)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Text("\(userManager.user?.xpToNextLevel ?? 0) XP to go")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: userManager.user?.levelProgress ?? 0)
                    .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                    .scaleEffect(y: 2)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [.purple.opacity(0.5), .pink.opacity(0.5)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - Tab Selector
    private var tabSelector: some View {
        HStack(spacing: 0) {
            ForEach(GamificationTab.allCases, id: \.self) { tab in
                Button(action: {
                    selectedTab = tab
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.icon)
                            .font(.title3)
                        Text(tab.title)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .purple : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedTab == tab ? .purple.opacity(0.2) : .clear)
                    )
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - Overview Content
    private var overviewContent: some View {
        VStack(spacing: 20) {
            // Daily challenges
            dailyChallengesSection
            
            // Recent achievements
            recentAchievementsSection
            
            // Weekly goals
            weeklyGoalsSection
            
            // Quick stats
            quickStatsSection
        }
    }
    
    // MARK: - Daily Challenges Section
    private var dailyChallengesSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Today's Challenges 🎯")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("Resets in 4h 23m")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.orange.opacity(0.2))
                    )
            }
            
            LazyVStack(spacing: 12) {
                ForEach(gamificationManager.dailyChallenges, id: \.id) { challenge in
                    DailyChallengeCard(challenge: challenge)
                }
            }
        }
    }
    
    // MARK: - Recent Achievements Section
    private var recentAchievementsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recent Achievements 🏆")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    selectedTab = .achievements
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(gamificationManager.recentAchievements.prefix(5), id: \.id) { achievement in
                        GamificationAchievementBadge(achievement: achievement) {
                            showingAchievementDetail = achievement
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
        }
    }
    
    // MARK: - Weekly Goals Section
    private var weeklyGoalsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Weekly Goals 📈")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("5 days left")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            VStack(spacing: 12) {
                WeeklyGoalCard(
                    title: "Complete 5 Quests",
                    progress: 3,
                    total: 5,
                    reward: "50 XP + Theme Unlock",
                    icon: "target"
                )
                
                WeeklyGoalCard(
                    title: "7-Day Login Streak",
                    progress: 4,
                    total: 7,
                    reward: "100 XP + Badge",
                    icon: "flame.fill"
                )
                
                WeeklyGoalCard(
                    title: "Join a Squad",
                    progress: 0,
                    total: 1,
                    reward: "Social Badge + 25 XP",
                    icon: "person.3.fill"
                )
            }
        }
    }
    
    // MARK: - Quick Stats Section
    private var quickStatsSection: some View {
        VStack(spacing: 16) {
            Text("Your Stats 📊")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                GamificationStatCard(
                    title: "Quests Completed",
                    value: "\(gamificationManager.questsCompleted)",
                    icon: "checkmark.circle.fill",
                    color: .green
                )

                GamificationStatCard(
                    title: "Achievements",
                    value: "\(gamificationManager.achievementsUnlocked)",
                    icon: "trophy.fill",
                    color: .orange
                )

                GamificationStatCard(
                    title: "Days Active",
                    value: "\(gamificationManager.daysActive)",
                    icon: "calendar.circle.fill",
                    color: .blue
                )

                GamificationStatCard(
                    title: "Rank",
                    value: "#\(gamificationManager.userRank)",
                    icon: "star.fill",
                    color: .purple
                )
            }
        }
    }
    
    // MARK: - Achievements Content
    private var achievementsContent: some View {
        VStack(spacing: 20) {
            // Achievement categories
            AchievementCategoriesView(
                achievements: gamificationManager.allAchievements,
                onAchievementTap: { achievement in
                    showingAchievementDetail = achievement
                }
            )
        }
    }
    
    // MARK: - Rewards Content
    private var rewardsContent: some View {
        VStack(spacing: 20) {
            // Unlockable themes
            UnlockableThemesView(
                themes: gamificationManager.availableThemes,
                onThemeSelect: { theme in
                    showingThemeSelector = true
                }
            )
            
            // Premium rewards
            PremiumRewardsView(
                rewards: gamificationManager.premiumRewards,
                canAccess: subscriptionManager.canAccessFeature(.premiumRewards)
            )
        }
    }
    
    // MARK: - Leaderboard Content
    private var leaderboardContent: some View {
        VStack(spacing: 20) {
            LeaderboardView(
                leaderboard: gamificationManager.leaderboard,
                userRank: gamificationManager.userRank
            )
        }
    }
}

enum GamificationTab: String, CaseIterable {
    case overview = "overview"
    case achievements = "achievements"
    case rewards = "rewards"
    case leaderboard = "leaderboard"
    
    var title: String {
        switch self {
        case .overview: return "Overview"
        case .achievements: return "Badges"
        case .rewards: return "Rewards"
        case .leaderboard: return "Ranks"
        }
    }
    
    var icon: String {
        switch self {
        case .overview: return "house.fill"
        case .achievements: return "trophy.fill"
        case .rewards: return "gift.fill"
        case .leaderboard: return "chart.bar.fill"
        }
    }
}

// MARK: - Supporting Views
struct DailyChallengeCard: View {
    let challenge: DailyChallenge

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(challenge.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(challenge.description)
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack {
                    Image(systemName: "star.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    Text("\(challenge.xpReward) XP")
                        .font(.caption)
                        .fontWeight(.medium)
                }
            }

            Spacer()

            VStack {
                if challenge.isCompleted {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.title2)
                } else {
                    Button("Start") {
                        // Handle challenge start
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.purple)
                    )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct GamificationAchievementBadge: View {
    let achievement: GamificationAchievement
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Image(systemName: achievement.icon)
                    .font(.title2)
                    .foregroundColor(achievement.rarity.color)
                    .frame(width: 50, height: 50)
                    .background(
                        Circle()
                            .fill(achievement.rarity.color.opacity(0.2))
                    )

                Text(achievement.title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(width: 80)
        }
    }
}

struct WeeklyGoalCard: View {
    let title: String
    let progress: Int
    let total: Int
    let reward: String
    let icon: String

    private var progressPercentage: Double {
        Double(progress) / Double(total)
    }

    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.purple)
                .font(.title2)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)

                Text(reward)
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack {
                    ProgressView(value: progressPercentage)
                        .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                        .scaleEffect(y: 1.5)

                    Text("\(progress)/\(total)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.purple)
                }
            }

            Spacer()

            if progress >= total {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

struct GamificationStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title2)

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

#Preview {
    AdvancedGamificationView()
        .environmentObject(UserManager())
        .environmentObject(SubscriptionManager())
}
