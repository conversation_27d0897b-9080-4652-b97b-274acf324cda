//
//  ViralChallengeView.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI

struct ViralChallengeView: View {
    @Environment(\.theme) var theme
    @Environment(\.dismiss) private var dismiss
    @State private var selectedChallenge: ViralChallenge?
    @State private var showingCreateChallenge = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Header
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            VStack(alignment: .leading) {
                                Text("🔥 Viral Challenges")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(theme.onBackground)
                                Text("Join trending finance challenges and compete with your squad!")
                                    .font(.subheadline)
                                    .foregroundColor(theme.onBackground.opacity(0.7))
                            }
                            Spacer()
                        }
                    }
                    .padding(.horizontal, 16)
                    
                    // Trending challenges
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Text("🔥 Trending Now")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(theme.onBackground)
                            Spacer()
                            Text("LIVE")
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.red)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(.red.opacity(0.15))
                                )
                        }
                        .padding(.horizontal, 16)
                        
                        ForEach(mockTrendingChallenges) { challenge in
                            SocialChallengeCard(challenge: challenge) {
                                selectedChallenge = challenge
                            }
                            .padding(.horizontal, 16)
                        }
                    }
                    
                    // Squad challenges
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Text("👥 Squad Challenges")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(theme.onBackground)
                            Spacer()
                        }
                        .padding(.horizontal, 16)
                        
                        ForEach(mockSquadChallenges) { challenge in
                            SocialChallengeCard(challenge: challenge) {
                                selectedChallenge = challenge
                            }
                            .padding(.horizontal, 16)
                        }
                    }
                }
                .padding(.vertical, 16)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingCreateChallenge = true }) {
                        Image(systemName: "plus")
                            .foregroundColor(.purple)
                    }
                }
            }
        }
        .sheet(item: $selectedChallenge) { challenge in
            ChallengeDetailView(challenge: challenge)
        }
        .sheet(isPresented: $showingCreateChallenge) {
            CreateChallengeView()
        }
    }
    
    private var mockTrendingChallenges: [ViralChallenge] {
        [
            ViralChallenge(
                title: "💎 Diamond Hands Challenge",
                description: "Hold your investments for 30 days without selling! Show your diamond hands energy 💅",
                emoji: "💎",
                participants: 2847,
                timeLeft: "2 days left",
                reward: "500 XP + Diamond Badge",
                difficulty: .medium,
                isViral: true
            ),
            ViralChallenge(
                title: "🚀 Rocket Portfolio Challenge",
                description: "Achieve 10% portfolio growth this month. Let's get this bag bestie! 💰",
                emoji: "🚀",
                participants: 1523,
                timeLeft: "1 week left",
                reward: "1000 XP + Rocket Theme",
                difficulty: .hard,
                isViral: true
            )
        ]
    }
    
    private var mockSquadChallenges: [ViralChallenge] {
        [
            ViralChallenge(
                title: "📚 Finance Education Sprint",
                description: "Complete 5 finance lessons with your squad. Knowledge is power periodt! ✨",
                emoji: "📚",
                participants: 456,
                timeLeft: "3 days left",
                reward: "300 XP + Scholar Badge",
                difficulty: .easy,
                isViral: false
            ),
            ViralChallenge(
                title: "🎯 Investment Accuracy Challenge",
                description: "Make 3 profitable trades in a row. It's giving main character energy! 👑",
                emoji: "🎯",
                participants: 789,
                timeLeft: "5 days left",
                reward: "750 XP + Sniper Badge",
                difficulty: .hard,
                isViral: false
            )
        ]
    }
}

struct ViralChallenge: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let emoji: String
    let participants: Int
    let timeLeft: String
    let reward: String
    let difficulty: ChallengeDifficulty
    let isViral: Bool
    
    enum ChallengeDifficulty: String, CaseIterable {
        case easy = "Easy vibes"
        case medium = "Mid energy"
        case hard = "Boss level"
        
        var color: Color {
            switch self {
            case .easy: return .green
            case .medium: return .orange
            case .hard: return .red
            }
        }
    }
}

struct SocialChallengeCard: View {
    let challenge: ViralChallenge
    let onJoin: () -> Void
    @Environment(\.theme) var theme
    @State private var isLiked = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Text(challenge.emoji)
                    .font(.title)
                    .frame(width: 50, height: 50)
                    .background(
                        Circle()
                            .fill(theme.accent.opacity(0.1))
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(challenge.title)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(theme.onSurface)
                        
                        if challenge.isViral {
                            Text("🔥")
                                .font(.caption)
                        }
                    }
                    
                    Text(challenge.description)
                        .font(.subheadline)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                        .lineLimit(2)
                }
                
                Spacer()
            }
            
            // Stats
            HStack(spacing: 20) {
                VStack(alignment: .leading) {
                    Text("Participants")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(challenge.participants)")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface)
                }
                
                VStack(alignment: .leading) {
                    Text("Difficulty")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(challenge.difficulty.rawValue)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(challenge.difficulty.color)
                }
                
                VStack(alignment: .leading) {
                    Text("Time Left")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(challenge.timeLeft)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
            }
            
            // Reward
            HStack {
                Image(systemName: "gift.fill")
                    .foregroundColor(.purple)
                Text("Reward: \(challenge.reward)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(theme.onSurface)
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(.purple.opacity(0.1))
                    .stroke(.purple.opacity(0.3), lineWidth: 1)
            )
            
            // Actions
            HStack(spacing: 12) {
                Button(action: { 
                    withAnimation(.spring(response: 0.3)) {
                        isLiked.toggle()
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: isLiked ? "heart.fill" : "heart")
                            .foregroundColor(isLiked ? .red : .gray)
                            .scaleEffect(isLiked ? 1.2 : 1.0)
                        Text("\(challenge.participants + (isLiked ? 1 : 0))")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                
                Button(action: {}) {
                    HStack(spacing: 4) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.gray)
                        Text("Share")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                
                Spacer()
                
                Button(action: onJoin) {
                    Text("Join Challenge! 🚀")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(
                            LinearGradient(
                                colors: [.purple, .pink],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(20)
                }
            }
        }
        .padding(16)
        .glassmorphicCard(theme: theme)
    }
}

struct ChallengeDetailView: View {
    let challenge: ViralChallenge
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Challenge details coming soon! 🚀")
                        .font(.headline)
                        .padding()
                }
            }
            .navigationTitle(challenge.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct CreateChallengeView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Create your own viral challenge! 💅")
                        .font(.headline)
                        .padding()
                }
            }
            .navigationTitle("Create Challenge")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ViralChallengeView()
}
