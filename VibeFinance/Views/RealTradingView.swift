//
//  RealTradingView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct RealTradingView: View {
    @EnvironmentObject var realTradingManager: RealTradingManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @State private var selectedTab = 0
    @State private var showingConnectionSheet = false
    @State private var showingUpgradeSheet = false
    
    var body: some View {
        VStack {
            if !realTradingManager.isConnected {
                // Brokerage Connection Required
                RealTradingConnectionView {
                    showingConnectionSheet = true
                }
            } else {
                // Real Trading Interface
                RealTradingMainView(selectedTab: $selectedTab)
            }
        }
        .sheet(isPresented: $showingConnectionSheet) {
            BrokerageConnectionSheet()
        }
        .sheet(isPresented: $showingUpgradeSheet) {
            SubscriptionUpgradeView()
        }
    }
}

// MARK: - Upgrade Required View
struct RealTradingUpgradeView: View {
    let onUpgrade: () -> Void
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 20) {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .font(.system(size: 80))
                    .foregroundColor(.purple)
                
                Text("Real Trading")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Trade with real money and build actual wealth")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 16) {
                FeatureRow(
                    icon: "dollarsign.circle.fill",
                    title: "Live Trading",
                    description: "Buy and sell real stocks with commission-free trading"
                )
                
                FeatureRow(
                    icon: "shield.checkered",
                    title: "SIPC Protected",
                    description: "Your investments are protected up to $500,000"
                )
                
                FeatureRow(
                    icon: "chart.bar.fill",
                    title: "Real Portfolio",
                    description: "Track your actual investment performance"
                )
                
                FeatureRow(
                    icon: "brain.head.profile",
                    title: "AI Guidance",
                    description: "Get personalized investment recommendations"
                )
            }
            .padding(.horizontal)
            
            Spacer()
            
            VStack(spacing: 16) {
                Button(action: onUpgrade) {
                    HStack {
                        Text("Upgrade to Pro")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Text("$19.99/month")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
                
                Text("Start with paper trading to practice risk-free")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal)
        }
        .padding()
    }
}

// MARK: - Connection Required View
struct RealTradingConnectionView: View {
    let onConnect: () -> Void
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 20) {
                Image(systemName: "link.circle.fill")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)
                
                Text("Connect Your Brokerage")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Link your Alpaca account to start trading")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 16) {
                SecurityFeature(
                    icon: "lock.shield.fill",
                    title: "Bank-Level Security",
                    description: "Your credentials are encrypted and never stored"
                )
                
                SecurityFeature(
                    icon: "checkmark.seal.fill",
                    title: "SEC Regulated",
                    description: "Alpaca is a registered broker-dealer with the SEC"
                )
                
                SecurityFeature(
                    icon: "eye.slash.fill",
                    title: "Read-Only Access",
                    description: "We only access your account for trading, never personal data"
                )
            }
            .padding(.horizontal)
            
            Spacer()
            
            VStack(spacing: 16) {
                Button(action: onConnect) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                        Text("Connect Alpaca Account")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue)
                    )
                }
                
                HStack {
                    Text("Don't have an account?")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Button("Sign up with Alpaca") {
                        if let url = URL(string: "https://alpaca.markets") {
                            UIApplication.shared.open(url)
                        }
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding(.horizontal)
        }
        .padding()
    }
}

// MARK: - Main Trading View
struct RealTradingMainView: View {
    @Binding var selectedTab: Int
    @EnvironmentObject var realTradingManager: RealTradingManager
    
    var body: some View {
        VStack(spacing: 0) {
            // Portfolio Summary Header
            if let performance = realTradingManager.getPortfolioPerformance() {
                RealPortfolioSummaryCard(performance: performance)
                    .padding()
            }
            
            // Tab Selector
            RealTradingTabSelector(selectedTab: $selectedTab)
            
            // Tab Content
            TabView(selection: $selectedTab) {
                RealPortfolioTab()
                    .tag(0)
                
                RealOrdersTab()
                    .tag(1)
                
                RealTradingTab()
                    .tag(2)
                
                RealAnalyticsTab()
                    .tag(3)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
        }
        .refreshable {
            await realTradingManager.refreshData()
        }
    }
}

// MARK: - Portfolio Summary Card
struct RealPortfolioSummaryCard: View {
    let performance: RealPortfolioPerformance
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Portfolio Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("$\(performance.totalValue, specifier: "%.2f")")
                        .font(.title)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: performance.isDayPositive ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        Text("$\(abs(performance.dayChange), specifier: "%.2f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(performance.isDayPositive ? .green : .red)
                    
                    Text("\(performance.dayChangePercent >= 0 ? "+" : "")\(performance.dayChangePercent, specifier: "%.2f")%")
                        .font(.caption)
                        .foregroundColor(performance.isDayPositive ? .green : .red)
                }
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total Gain/Loss")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 4) {
                        Text("$\(performance.totalGainLoss, specifier: "%.2f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(performance.totalGainLossPercent >= 0 ? "+" : "")\(performance.totalGainLossPercent, specifier: "%.2f")%)")
                            .font(.caption)
                    }
                    .foregroundColor(performance.isPositive ? .green : .red)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Buying Power")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("$\(performance.buyingPower, specifier: "%.2f")")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Tab Selector
struct RealTradingTabSelector: View {
    @Binding var selectedTab: Int
    
    private let tabs = [
        ("Portfolio", "chart.pie.fill"),
        ("Orders", "list.bullet"),
        ("Trade", "plus.circle.fill"),
        ("Analytics", "chart.bar.fill")
    ]
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                Button(action: {
                    selectedTab = index
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.1)
                            .font(.system(size: 16, weight: .medium))
                        
                        Text(tab.0)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(Color(.systemGray6))
    }
}

// MARK: - Feature Row
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.purple.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Security Feature
struct SecurityFeature: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.green)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.green.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Portfolio Tab
struct RealPortfolioTab: View {
    @EnvironmentObject var realTradingManager: RealTradingManager

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(realTradingManager.realHoldings) { holding in
                    RealHoldingCard(holding: holding)
                }

                if realTradingManager.realHoldings.isEmpty {
                    EmptyPortfolioView()
                }
            }
            .padding()
        }
    }
}

// MARK: - Orders Tab
struct RealOrdersTab: View {
    @EnvironmentObject var realTradingManager: RealTradingManager
    @State private var selectedFilter = "All"

    private let filters = ["All", "Open", "Filled", "Cancelled"]

    var filteredOrders: [RealOrder] {
        switch selectedFilter {
        case "Open":
            return realTradingManager.pendingOrders.filter { $0.isActive }
        case "Filled":
            return realTradingManager.pendingOrders.filter { $0.isFilled }
        case "Cancelled":
            return realTradingManager.pendingOrders.filter { $0.status == "cancelled" }
        default:
            return realTradingManager.pendingOrders
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Filter Selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(filters, id: \.self) { filter in
                        Button(filter) {
                            selectedFilter = filter
                        }
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(selectedFilter == filter ? .white : .primary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(selectedFilter == filter ? Color.purple : Color(.systemGray6))
                        )
                    }
                }
                .padding(.horizontal)
            }
            .padding(.vertical)

            // Orders List
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(filteredOrders, id: \.id) { order in
                        RealOrderCard(order: order)
                    }

                    if filteredOrders.isEmpty {
                        EmptyOrdersView(filter: selectedFilter)
                    }
                }
                .padding()
            }
        }
    }
}

// MARK: - Trading Tab
struct RealTradingTab: View {
    @EnvironmentObject var realTradingManager: RealTradingManager
    @State private var searchText = ""
    @State private var selectedStock: StockPrice?
    @State private var showingTradeSheet = false

    var body: some View {
        VStack {
            // Search Bar
            SearchBar(text: $searchText, placeholder: "Search stocks...")
                .padding()

            // Quick Actions
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    QuickTradeButton(title: "Buy AAPL", symbol: "AAPL", side: .buy) {
                        // Handle quick buy
                    }

                    QuickTradeButton(title: "Buy TSLA", symbol: "TSLA", side: .buy) {
                        // Handle quick buy
                    }

                    QuickTradeButton(title: "Buy NVDA", symbol: "NVDA", side: .buy) {
                        // Handle quick buy
                    }
                }
                .padding(.horizontal)
            }

            // Holdings for Quick Sell
            if !realTradingManager.realHoldings.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Your Holdings")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .padding(.horizontal)

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(realTradingManager.realHoldings) { holding in
                                QuickSellCard(holding: holding) {
                                    // Handle quick sell
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }

            Spacer()
        }
        .sheet(isPresented: $showingTradeSheet) {
            if let stock = selectedStock {
                RealTradeSheet(stock: stock)
            }
        }
    }
}

// MARK: - Analytics Tab
struct RealAnalyticsTab: View {
    @EnvironmentObject var realTradingManager: RealTradingManager

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                if let performance = realTradingManager.getPortfolioPerformance() {
                    // Performance Summary
                    PerformanceSummaryCard(performance: performance)

                    // Best/Worst Performers
                    if let best = performance.bestPerformer, let worst = performance.worstPerformer {
                        PerformersCard(best: best, worst: worst)
                    }

                    // Asset Allocation
                    AssetAllocationCard(holdings: realTradingManager.realHoldings)
                }
            }
            .padding()
        }
    }
}

#Preview {
    RealTradingView()
        .environmentObject(RealTradingManager())
        .environmentObject(SubscriptionManager())
}
