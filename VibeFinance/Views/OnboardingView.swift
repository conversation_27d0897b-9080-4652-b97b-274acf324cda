//
//  OnboardingView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct OnboardingView: View {
    @EnvironmentObject var authManager: AuthManager
    @EnvironmentObject var userManager: UserManager
    @State private var currentStep = 0
    @State private var showSignIn = false
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    Color.purple.opacity(0.8),
                    Color.pink.opacity(0.6),
                    Color.orange.opacity(0.4)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            if showSignIn {
                AuthView()
                    .transition(.move(edge: .trailing))
            } else {
                OnboardingStepsView(currentStep: $currentStep, showSignIn: $showSignIn)
                    .transition(.move(edge: .leading))
            }
        }
        .animation(.easeInOut(duration: 0.5), value: showSignIn)
    }
}

struct OnboardingStepsView: View {
    @Binding var currentStep: Int
    @Binding var showSignIn: Bool
    
    private let steps = [
        OnboardingStep(
            title: "Welcome to WealthVibe! 🚀",
            subtitle: "Make finance fun and build wealth your way",
            description: "Join thousands of Gen Z and millennials who are vibing their way to financial freedom",
            emoji: "💎",
            color: .purple
        ),
        OnboardingStep(
            title: "Your Personal Finance Feed 📱",
            subtitle: "Curated content that actually matters to you",
            description: "Get daily finance news, tips, and investment ideas tailored to your interests and goals",
            emoji: "📈",
            color: .blue
        ),
        OnboardingStep(
            title: "Level Up with Quests 🎮",
            subtitle: "Learn finance through gamified challenges",
            description: "Complete daily quests, earn XP, and unlock new levels while mastering money skills",
            emoji: "🎯",
            color: .green
        ),
        OnboardingStep(
            title: "Squad Up & Invest Together 👥",
            subtitle: "Join investment squads with friends",
            description: "Pool money, vote on investments, and learn from your community",
            emoji: "🤝",
            color: .orange
        ),
        OnboardingStep(
            title: "Practice with Virtual Money 💰",
            subtitle: "Risk-free investment simulator",
            description: "Build your portfolio with $10K virtual money and real market data",
            emoji: "🎮",
            color: .pink
        )
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // Progress indicator
            HStack {
                ForEach(0..<steps.count, id: \.self) { index in
                    Circle()
                        .fill(index <= currentStep ? Color.white : Color.white.opacity(0.3))
                        .frame(width: 8, height: 8)
                        .scaleEffect(index == currentStep ? 1.2 : 1.0)
                        .animation(.spring(response: 0.3), value: currentStep)
                }
            }
            .padding(.top, 60)
            .padding(.bottom, 40)
            
            // Content
            TabView(selection: $currentStep) {
                ForEach(0..<steps.count, id: \.self) { index in
                    OnboardingStepView(step: steps[index])
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .animation(.easeInOut, value: currentStep)
            
            // Navigation buttons
            VStack(spacing: 16) {
                if currentStep < steps.count - 1 {
                    Button(action: {
                        withAnimation {
                            currentStep += 1
                        }
                    }) {
                        Text("Continue")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 56)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white.opacity(0.2))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 16)
                                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    )
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    Button(action: {
                        withAnimation {
                            showSignIn = true
                        }
                    }) {
                        Text("Get Started")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity)
                            .frame(height: 56)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white)
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                if currentStep > 0 {
                    Button(action: {
                        withAnimation {
                            currentStep -= 1
                        }
                    }) {
                        Text("Back")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 32)
            .padding(.bottom, 50)
        }
    }
}

struct OnboardingStepView: View {
    let step: OnboardingStep
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            // Emoji
            Text(step.emoji)
                .font(.system(size: 80))
                .scaleEffect(1.0)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: step.emoji)
            
            VStack(spacing: 16) {
                // Title
                Text(step.title)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                // Subtitle
                Text(step.subtitle)
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                
                // Description
                Text(step.description)
                    .font(.body)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
                    .padding(.horizontal, 16)
            }
            
            Spacer()
        }
        .padding(.horizontal, 32)
    }
}

struct OnboardingStep {
    let title: String
    let subtitle: String
    let description: String
    let emoji: String
    let color: Color
}

#Preview {
    OnboardingView()
        .environmentObject(AuthManager())
        .environmentObject(UserManager())
}
