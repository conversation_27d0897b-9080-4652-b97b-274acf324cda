//
//  LeaderboardView.swift
//  VibeFinance - Social Leaderboards & Competition System
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Models
struct LeaderboardUser: Identifiable, Codable {
    let id: String
    let name: String
    let username: String
    let avatar: String
    let points: Int
    let weeklyPoints: Int
    let monthlyPoints: Int
    let rank: Int
    let previousRank: Int
    let badges: [String]
    let level: Int
    let streak: Int
}

struct Competition: Identifiable, Codable {
    let id: String
    let title: String
    let description: String
    let type: CompetitionType
    let startDate: Date
    let endDate: Date
    let participants: Int
    let prize: String
    let isActive: Bool
    
    enum CompetitionType: String, CaseIterable, Codable {
        case weekly = "weekly"
        case monthly = "monthly"
    }
}

struct LeaderboardAchievement: Identifiable, Codable {
    let id: String
    let title: String
    let description: String
    let icon: String
    let rarity: AchievementRarity
    let unlockedAt: Date?
    let progress: Int?
    let maxProgress: Int?

    enum AchievementRarity: String, CaseIterable, Codable {
        case common = "common"
        case rare = "rare"
        case epic = "epic"
        case legendary = "legendary"

        var color: Color {
            switch self {
            case .common: return .gray
            case .rare: return .blue
            case .epic: return .purple
            case .legendary: return .yellow
            }
        }

        var gradient: [Color] {
            switch self {
            case .common: return [.gray.opacity(0.6), .gray]
            case .rare: return [.blue.opacity(0.6), .cyan]
            case .epic: return [.purple.opacity(0.6), .blue]
            case .legendary: return [.purple, .pink]
            }
        }
    }
}

enum LeaderboardTimeframe: String, CaseIterable {
    case weekly = "Weekly"
    case monthly = "Monthly"
    case allTime = "All Time"
}

enum LeaderboardTab: String, CaseIterable {
    case leaderboard = "Leaderboard"
    case competitions = "Competitions"
    case achievements = "Achievements"
    
    var icon: String {
        switch self {
        case .leaderboard: return "trophy.fill"
        case .competitions: return "target"
        case .achievements: return "award.fill"
        }
    }
}

// MARK: - Main Leaderboard View
struct LeaderboardView: View {
    @StateObject private var leaderboardManager = LeaderboardManager()
    @State private var selectedTab: LeaderboardTab = .leaderboard
    @State private var selectedTimeframe: LeaderboardTimeframe = .weekly
    @State private var animateRanks = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Glassmorphic Header
                    headerSection
                    
                    // Tab Content
                    switch selectedTab {
                    case .leaderboard:
                        leaderboardContent
                    case .competitions:
                        competitionsContent
                    case .achievements:
                        achievementsContent
                    }
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
            }
            .background(
                LinearGradient(
                    colors: [
                        Color.black.opacity(0.9),
                        Color.blue.opacity(0.3),
                        Color.purple.opacity(0.3)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
            .navigationTitle("Leaderboard")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
        }
        .onAppear {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2)) {
                animateRanks = true
            }
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 20) {
            // Title and Stats
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Finance Leaderboard")
                            .font(.system(size: 28, weight: .bold, design: .rounded))
                            .foregroundStyle(
                                LinearGradient(
                                    colors: [.blue, .purple],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                        
                        Text("Compete with friends and climb the ranks! 🚀")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    HStack(spacing: 8) {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.orange)
                        Text("1,247 active")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.9))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                            )
                    )
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .background(.ultraThinMaterial)
            .clipShape(RoundedRectangle(cornerRadius: 20))
            
            // Tab Navigation
            HStack(spacing: 4) {
                ForEach(LeaderboardTab.allCases, id: \.self) { tab in
                    Button(action: {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            selectedTab = tab
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: tab.icon)
                                .font(.caption)
                            Text(tab.rawValue)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(selectedTab == tab ? .black : .white.opacity(0.7))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedTab == tab ? Color.white : Color.white.opacity(0.1))
                                .shadow(color: selectedTab == tab ? .white.opacity(0.3) : .clear, radius: 4)
                        )
                    }
                }
            }
            .padding(4)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Leaderboard Content
    private var leaderboardContent: some View {
        VStack(spacing: 20) {
            // Timeframe Selector
            timeframeSelector
            
            // Top 3 Podium
            topThreePodium
            
            // Full Leaderboard
            fullLeaderboard
        }
    }
    
    private var timeframeSelector: some View {
        HStack(spacing: 4) {
            ForEach(LeaderboardTimeframe.allCases, id: \.self) { timeframe in
                Button(action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        selectedTimeframe = timeframe
                    }
                }) {
                    Text(timeframe.rawValue)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(selectedTimeframe == timeframe ? .white : .white.opacity(0.7))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedTimeframe == timeframe ? Color.blue : Color.white.opacity(0.1))
                        )
                }
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
        )
    }

    private var topThreePodium: some View {
        HStack(spacing: 12) {
            ForEach(Array(leaderboardManager.topUsers.prefix(3).enumerated()), id: \.element.id) { index, user in
                VStack(spacing: 12) {
                    // Rank Icon
                    ZStack {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: index == 0 ? [.yellow, .orange] :
                                           index == 1 ? [.gray.opacity(0.8), .gray] :
                                           [.orange.opacity(0.8), .red],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 32, height: 32)

                        Image(systemName: index == 0 ? "crown.fill" :
                                         index == 1 ? "medal.fill" : "trophy.fill")
                            .font(.headline)
                            .foregroundColor(.white)
                    }

                    // Avatar
                    AsyncImage(url: URL(string: user.avatar)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .overlay(
                                Text(user.name.prefix(1))
                                    .font(.headline)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            )
                    }
                    .frame(width: 60, height: 60)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    )

                    // User Info
                    VStack(spacing: 4) {
                        Text(user.name)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .lineLimit(1)

                        Text("@\(user.username)")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                            .lineLimit(1)

                        // Points
                        VStack(spacing: 2) {
                            Text("\(getPoints(for: user).formatted())")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                            Text("points")
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.6))
                        }

                        // Streak
                        HStack(spacing: 4) {
                            Image(systemName: "flame.fill")
                                .font(.caption)
                                .foregroundColor(.orange)
                            Text("\(user.streak)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                colors: index == 0 ? [Color.yellow.opacity(0.2), Color.orange.opacity(0.2)] :
                                       index == 1 ? [Color.gray.opacity(0.2), Color.gray.opacity(0.3)] :
                                       [Color.orange.opacity(0.2), Color.red.opacity(0.2)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(
                                    index == 0 ? Color.yellow.opacity(0.4) :
                                    index == 1 ? Color.gray.opacity(0.4) :
                                    Color.orange.opacity(0.4),
                                    lineWidth: 1
                                )
                        )
                )
                .scaleEffect(animateRanks ? 1.0 : 0.8)
                .opacity(animateRanks ? 1.0 : 0.0)
                .animation(
                    .spring(response: 0.6, dampingFraction: 0.8)
                    .delay(Double(index) * 0.1),
                    value: animateRanks
                )
            }
        }
    }

    private var fullLeaderboard: some View {
        LazyVStack(spacing: 12) {
            ForEach(Array(leaderboardManager.allUsers.enumerated()), id: \.element.id) { index, user in
                LeaderboardRow(
                    user: user,
                    timeframe: selectedTimeframe,
                    animationDelay: Double(index) * 0.05
                )
                .scaleEffect(animateRanks ? 1.0 : 0.9)
                .opacity(animateRanks ? 1.0 : 0.0)
                .animation(
                    .spring(response: 0.5, dampingFraction: 0.8)
                    .delay(Double(index) * 0.05),
                    value: animateRanks
                )
            }
        }
    }

    // MARK: - Competitions Content
    private var competitionsContent: some View {
        LazyVStack(spacing: 16) {
            ForEach(leaderboardManager.competitions) { competition in
                CompetitionCard(competition: competition)
            }
        }
    }

    // MARK: - Achievements Content
    private var achievementsContent: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            ForEach(leaderboardManager.achievements) { achievement in
                AchievementCard(achievement: achievement)
            }
        }
    }

    // MARK: - Helper Methods
    private func getPoints(for user: LeaderboardUser) -> Int {
        switch selectedTimeframe {
        case .weekly:
            return user.weeklyPoints
        case .monthly:
            return user.monthlyPoints
        case .allTime:
            return user.points
        }
    }
}
