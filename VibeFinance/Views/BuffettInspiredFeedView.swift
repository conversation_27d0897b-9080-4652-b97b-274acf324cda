//
//  BuffettInspiredFeedView.swift
//  VibeFinance - <PERSON> Inspired Financial Education
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct BuffettInspiredFeedView: View {
    @State private var feedItems: [BuffettFeedItem] = []
    @State private var isRefreshing = false
    @Environment(\.theme) var theme
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Portfolio Performance Summary Card
                BuffettPortfolioCard()
                    .padding(.horizontal, 16)

                // Market Insights from Warren's Perspective
                ForEach(feedItems) { item in
                    BuffettFeedCard(item: item)
                        .padding(.horizontal, 16)
                }

                // Add loading state or placeholder content if empty
                if feedItems.isEmpty {
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.2)
                            .tint(.yellow)

                        Text("Loading Warren's insights...")
                            .font(.subheadline)
                            .foregroundColor(theme.onBackground.opacity(0.7))
                    }
                    .frame(height: 200)
                    .padding(.horizontal, 16)
                }

                // Add bottom padding to ensure content doesn't get cut off
                Spacer(minLength: 100)
            }
            .padding(.vertical, 16)
        }
        .refreshable {
            await refreshFeed()
        }
        .onAppear {
            loadRealFeedData()
        }
    }
    
    private func loadRealFeedData() {
        Task {
            await generateRealFeedContent()
        }
    }

    private func refreshFeed() async {
        isRefreshing = true
        await generateRealFeedContent()
        isRefreshing = false
    }

    private func generateRealFeedContent() async {
        // Generate real feed content using AI and market data
        var realFeedItems: [BuffettFeedItem] = []

        // Always start with daily wisdom to ensure content exists
        let dailyWisdom = BuffettFeedItem(
            emoji: "💡",
            title: "Daily Wisdom from Warren",
            category: "Education",
            content: "\"Time is the friend of the wonderful company, the enemy of the mediocre.\" Today's market volatility reminds us that patience and quality selection are key to long-term wealth building.",
            buffettInsight: "Focus on companies with strong competitive advantages and excellent management.",
            timeAgo: "Today",
            likes: Int.random(in: 100...300),
            hasInvestmentOpportunity: false
        )
        realFeedItems.append(dailyWisdom)

        // 1. Get real market data for Warren Buffett's top holdings
        let buffettStocks = ["BRK.A", "AAPL", "BAC", "KO", "AXP"]

        for symbol in buffettStocks.prefix(3) { // Limit to 3 to avoid too much content
            if let stockData = await MarketService.shared.getStockData(symbol: symbol) {
                // Generate AI-powered Warren Buffett style analysis
                let analysis = await generateBuffettAnalysis(for: stockData)

                let feedItem = BuffettFeedItem(
                    emoji: "📈",
                    title: "Warren's Take: \(stockData.symbol)",
                    category: "Market Insight",
                    content: analysis,
                    buffettInsight: getRelevantBuffettQuote(for: stockData),
                    timeAgo: "Just now",
                    likes: Int.random(in: 50...200),
                    hasInvestmentOpportunity: true
                )
                realFeedItems.append(feedItem)
            }
        }

        // 2. Get real financial news and add Warren's perspective
        do {
            let news = try await NewsService.shared.fetchFinanceNews()
            for article in news.prefix(3) {
                let buffettPerspective = await generateBuffettPerspective(for: article)

                let feedItem = BuffettFeedItem(
                    emoji: "📰",
                    title: "Market News: \(article.title)",
                    category: "News",
                    content: buffettPerspective,
                    buffettInsight: getNewsRelevantQuote(),
                    timeAgo: "1h ago",
                    likes: Int.random(in: 20...100),
                    hasInvestmentOpportunity: false
                )
                realFeedItems.append(feedItem)
            }
        } catch {
            print("Failed to fetch news: \(error)")
        }

        // 3. Add educational content
        let educationalContent = BuffettFeedItem(
            emoji: "📚",
            title: "Investment Education",
            category: "Education",
            content: getDailyBuffettWisdom(),
            buffettInsight: getTodaysBuffettQuote(),
            timeAgo: "Today",
            likes: Int.random(in: 100...300),
            hasInvestmentOpportunity: false
        )
        realFeedItems.append(educationalContent)

        await MainActor.run {
            self.feedItems = realFeedItems
        }
    }

    private func generateBuffettAnalysis(for stock: StockPrice) async -> String {
        // Use AI to generate Warren Buffett style analysis
        let prompt = """
        Analyze \(stock.symbol) from Warren Buffett's investment perspective.
        Current price: $\(stock.price), Change: \(stock.changePercent)%

        Provide a brief analysis in Warren's style focusing on:
        - Business fundamentals
        - Long-term value
        - Competitive moats
        - Management quality

        Keep it under 150 words and write as if Warren is speaking.
        """

        do {
            let aiService = GeminiAIService()
            let response = try await aiService.generateResponse(prompt: prompt)
            return response.text
        } catch {
            return "This company continues to demonstrate the qualities I look for: strong fundamentals, excellent management, and a durable competitive advantage. The current price movement is just market noise - focus on the long-term business value."
        }
    }

    private func generateBuffettPerspective(for article: NewsArticle) async -> String {
        let prompt = """
        Provide Warren Buffett's perspective on this financial news:
        Title: \(article.title)
        Summary: \(article.content)

        Write a brief response (under 100 words) as if Warren is commenting on this news,
        focusing on long-term investing principles and how this affects value investors.
        """

        do {
            let aiService = GeminiAIService()
            let response = try await aiService.generateResponse(prompt: prompt)
            return response.text
        } catch {
            return "Remember, market news creates opportunities for patient investors. Focus on businesses you understand, with strong competitive positions and excellent management. Short-term volatility is the friend of the long-term value investor."
        }
    }

    private func getRelevantBuffettQuote(for stock: StockPrice) -> String {
        let quotes = [
            "Price is what you pay. Value is what you get.",
            "It's far better to buy a wonderful company at a fair price than a fair company at a wonderful price.",
            "Our favorite holding period is forever.",
            "Risk comes from not knowing what you're doing.",
            "The stock market is a voting machine in the short run, but a weighing machine in the long run."
        ]
        return quotes.randomElement()!
    }

    private func getNewsRelevantQuote() -> String {
        return "Be fearful when others are greedy and greedy when others are fearful."
    }

    private func getTodaysBuffettQuote() -> String {
        let dailyQuotes = [
            "Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1.",
            "Someone's sitting in the shade today because someone planted a tree a long time ago.",
            "The most important investment you can make is in yourself.",
            "Diversification is protection against ignorance. It makes little sense if you know what you are doing.",
            "Time is the friend of the wonderful business, the enemy of the mediocre."
        ]
        return dailyQuotes.randomElement()!
    }

    private func getDailyBuffettWisdom() -> String {
        let wisdomPieces = [
            "Focus on companies with strong competitive advantages - what I call 'economic moats.' These businesses can maintain pricing power and market share over decades.",
            "Look for businesses that are so simple that even a fool can run them, because eventually one will. The best investments are in companies with sustainable competitive advantages.",
            "When you find a truly exceptional business, the price you pay becomes less important than the quality of what you're buying. Time will reward patience with great companies.",
            "The key to successful investing is not predicting the future, but finding businesses that will thrive regardless of economic conditions. Focus on necessity, not luxury.",
            "Your best investment is always in developing your own skills and knowledge. The more you learn about business and investing, the better your decisions will become."
        ]
        return wisdomPieces.randomElement()!
    }

    private func generateActionItems(for stock: StockPrice) -> [String] {
        return [
            "Research the company's annual report",
            "Analyze competitive position",
            "Review management track record",
            "Consider long-term growth prospects"
        ]
    }
}

// MARK: - Buffett Portfolio Card
struct BuffettPortfolioCard: View {
    @Environment(\.theme) var theme

    var body: some View {
        VStack(spacing: 16) {
            // Header with chart icon
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .font(.title2)
                    .foregroundColor(theme.accent)

                Text("Your Wealth Journey")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(theme.onSurface)

                Spacer()
            }

            // Portfolio Value Section
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Building wealth the Buffett way")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))

                    Text("$12,847.50")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("+$847.50 (7.1%)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.success)
                }
            }

            // Stats Section
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Time in Market")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.6))
                    Text("247 days")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Compound Growth")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.6))
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.accent)
                }
            }
        }
        .padding(20)
        .glassmorphicCard(theme: theme, cornerRadius: 20)
    }
}

// MARK: - Buffett Feed Card
struct BuffettFeedCard: View {
    let item: BuffettFeedItem
    @State private var isLiked = false
    @State private var isBookmarked = false
    @Environment(\.theme) var theme

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    HStack(spacing: 8) {
                        Text(item.emoji)
                            .font(.title2)

                        VStack(alignment: .leading, spacing: 2) {
                            Text(item.title)
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(theme.onSurface)

                            Text(item.category)
                                .font(.caption)
                                .foregroundColor(theme.onSurface.opacity(0.7))
                        }
                    }

                    Spacer()

                    Text(item.timeAgo)
                        .font(.caption2)
                        .foregroundColor(theme.onSurface.opacity(0.6))
                }

                // Content
                Text(item.content)
                    .font(.subheadline)
                    .foregroundColor(theme.onSurface.opacity(0.9))
                    .lineLimit(4)
                
                // Warren's Insight
                if let insight = item.buffettInsight {
                    HStack(spacing: 8) {
                        Image(systemName: "lightbulb.fill")
                            .font(.caption)
                            .foregroundColor(theme.accent)

                        Text("Warren's Take: \(insight)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(theme.onSurface)
                            .italic()
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(theme.accent.opacity(0.1))
                            .stroke(theme.accent.opacity(0.3), lineWidth: 1)
                    )
                }
                
                // Actions
                HStack(spacing: 20) {
                    Button(action: { isLiked.toggle() }) {
                        HStack(spacing: 4) {
                            Image(systemName: isLiked ? "heart.fill" : "heart")
                                .foregroundColor(isLiked ? .red : theme.onSurface.opacity(0.6))
                            Text("\(item.likes + (isLiked ? 1 : 0))")
                                .font(.caption)
                                .foregroundColor(theme.onSurface.opacity(0.6))
                        }
                    }

                    Button(action: { isBookmarked.toggle() }) {
                        Image(systemName: isBookmarked ? "bookmark.fill" : "bookmark")
                            .foregroundColor(isBookmarked ? theme.accent : theme.onSurface.opacity(0.6))
                    }

                    Button(action: {}) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(theme.onSurface.opacity(0.6))
                    }

                    Spacer()

                    if item.hasInvestmentOpportunity {
                        Button(action: {}) {
                            Text("Invest")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.black)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(theme.success)
                                )
                        }
                    }
                }
            }
            .padding(16)
        }
        .glassmorphicCard(theme: theme)
    }
}

// MARK: - Buffett Feed Item Model
struct BuffettFeedItem: Identifiable {
    let id = UUID()
    let emoji: String
    let title: String
    let category: String
    let content: String
    let buffettInsight: String?
    let timeAgo: String
    let likes: Int
    let hasInvestmentOpportunity: Bool
    
    static let mockData: [BuffettFeedItem] = [
        BuffettFeedItem(
            emoji: "🍎",
            title: "Apple Inc. (AAPL)",
            category: "Value Investment",
            content: "Apple continues to demonstrate exceptional brand loyalty and ecosystem strength. The company's services revenue grew 16% YoY, showing the power of recurring revenue streams.",
            buffettInsight: "I love businesses with wide economic moats. Apple's ecosystem creates incredible customer stickiness.",
            timeAgo: "2h ago",
            likes: 247,
            hasInvestmentOpportunity: true
        ),
        BuffettFeedItem(
            emoji: "🏦",
            title: "Banking Sector Analysis",
            category: "Market Insight",
            content: "Regional banks are showing strong fundamentals with improving net interest margins. However, commercial real estate exposure remains a concern for some institutions.",
            buffettInsight: "Banking is a business where you can't afford to make big mistakes. Stick to the well-capitalized ones.",
            timeAgo: "4h ago",
            likes: 156,
            hasInvestmentOpportunity: false
        ),
        BuffettFeedItem(
            emoji: "⚡",
            title: "Renewable Energy Boom",
            category: "Future Trends",
            content: "Solar and wind energy costs have dropped 70% in the past decade. Berkshire Hathaway Energy continues to invest heavily in renewable infrastructure.",
            buffettInsight: "The best time to plant a tree was 20 years ago. The second best time is now. Same with energy infrastructure.",
            timeAgo: "6h ago",
            likes: 312,
            hasInvestmentOpportunity: true
        ),
        BuffettFeedItem(
            emoji: "📚",
            title: "The Power of Reading",
            category: "Education",
            content: "Warren Buffett reads 500 pages a day. Knowledge compounds just like money. Start building your investment knowledge library today.",
            buffettInsight: "Read everything you can about businesses and investing. Knowledge is the best investment you can make.",
            timeAgo: "8h ago",
            likes: 428,
            hasInvestmentOpportunity: false
        )
    ]
}

#Preview {
    BuffettInspiredFeedView()
}
