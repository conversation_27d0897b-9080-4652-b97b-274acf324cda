//
//  BuffettInspiredFeedView.swift
//  VibeFinance - <PERSON> Inspired Financial Education
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct BuffettInspiredFeedView: View {
    @State private var feedItems: [BuffettFeedItem] = []
    @State private var isRefreshing = false
    @Environment(\.theme) var theme
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Portfolio Performance Summary Card
                BuffettPortfolioCard()
                    .padding(.horizontal, 16)

                // TikTok-style Feed Header
                VStack(spacing: 12) {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Your Finance Feed 🔥")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(theme.onBackground)
                            Text("Trending financial content just for you")
                                .font(.subheadline)
                                .foregroundColor(theme.onBackground.opacity(0.7))
                        }
                        Spacer()

                        // Trending indicator
                        HStack(spacing: 4) {
                            Image(systemName: "flame.fill")
                                .foregroundColor(.orange)
                            Text("LIVE")
                                .font(.caption)
                                .fontWeight(.bold)
                                .foregroundColor(.orange)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.orange.opacity(0.15))
                        )
                    }
                }
                .padding(.horizontal, 16)

                // Market Insights with TikTok-style spacing
                ForEach(feedItems) { item in
                    BuffettFeedCard(item: item)
                        .padding(.horizontal, 16)
                }

                // Add loading state or placeholder content if empty
                if feedItems.isEmpty {
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.2)
                            .tint(theme.accent)

                        Text("Loading your personalized feed...")
                            .font(.subheadline)
                            .foregroundColor(theme.onBackground.opacity(0.7))

                        Text("Getting the latest financial vibes! ✨")
                            .font(.caption)
                            .foregroundColor(theme.onBackground.opacity(0.5))
                    }
                    .frame(height: 200)
                    .padding(.horizontal, 16)
                }

                // Add bottom padding to ensure content doesn't get cut off
                Spacer(minLength: 100)
            }
            .padding(.vertical, 16)
        }
        .refreshable {
            await refreshFeed()
        }
        .onAppear {
            loadRealFeedData()
        }
    }
    
    private func loadRealFeedData() {
        Task {
            await generateRealFeedContent()
        }
    }

    private func refreshFeed() async {
        isRefreshing = true
        await generateRealFeedContent()
        isRefreshing = false
    }

    private func generateRealFeedContent() async {
        // Generate real feed content using AI and market data
        var realFeedItems: [BuffettFeedItem] = []

        // Always start with daily wisdom to ensure content exists
        let dailyWisdom = BuffettFeedItem(
            emoji: "✨",
            title: "Daily Vibe Check",
            category: "Motivation",
            content: "Bestie, investing is literally just buying pieces of companies you believe in! 🚀 Don't let market drama stress you out - think long-term and stay consistent. You're building your future empire one investment at a time! 💅👑",
            buffettInsight: "Consistency > perfection. Small steps lead to big wins! 🎯",
            timeAgo: "Today",
            likes: Int.random(in: 100...300),
            hasInvestmentOpportunity: false,
            hashtags: ["#VibeCheck", "#Investing", "#GenZMoney", "#FinanceBuddy", "#MainCharacterEnergy"],
            isViralContent: true,
            trendingScore: 95,
            comments: Int.random(in: 20...50),
            shares: Int.random(in: 10...30)
        )
        realFeedItems.append(dailyWisdom)

        // 1. Get real market data for Warren Buffett's top holdings
        let buffettStocks = ["BRK.A", "AAPL", "BAC", "KO", "AXP"]

        for symbol in buffettStocks.prefix(3) { // Limit to 3 to avoid too much content
            if let stockData = await MarketService.shared.getStockData(symbol: symbol) {
                // Generate AI-powered Finance Buddy style analysis
                let analysis = await generateFinanceBuddyAnalysis(for: stockData)

                let feedItem = BuffettFeedItem(
                    emoji: "📈",
                    title: "Finance Buddy's Take: \(stockData.symbol)",
                    category: "Market Vibes",
                    content: analysis,
                    buffettInsight: getGenZInsight(for: stockData),
                    timeAgo: "Just now",
                    likes: Int.random(in: 50...200),
                    hasInvestmentOpportunity: true,
                    hashtags: generateStockHashtags(for: stockData.symbol),
                    isViralContent: stockData.changePercent > 5 || stockData.changePercent < -5,
                    trendingScore: Int(abs(stockData.changePercent) * 10),
                    comments: Int.random(in: 5...25),
                    shares: Int.random(in: 2...15)
                )
                realFeedItems.append(feedItem)
            }
        }

        // 2. Get real financial news and add Warren's perspective
        do {
            let news = try await NewsService.shared.fetchFinanceNews()
            for article in news.prefix(3) {
                let financeBuddyPerspective = await generateFinanceBuddyPerspective(for: article)

                let feedItem = BuffettFeedItem(
                    emoji: "📰",
                    title: "News Tea: \(article.title)",
                    category: "News",
                    content: financeBuddyPerspective,
                    buffettInsight: getGenZNewsInsight(),
                    timeAgo: "1h ago",
                    likes: Int.random(in: 20...100),
                    hasInvestmentOpportunity: false,
                    hashtags: generateNewsHashtags(for: article.title),
                    isViralContent: article.title.lowercased().contains("crypto") || article.title.lowercased().contains("tesla") || article.title.lowercased().contains("bitcoin"),
                    trendingScore: Int.random(in: 30...80),
                    comments: Int.random(in: 8...30),
                    shares: Int.random(in: 5...20)
                )
                realFeedItems.append(feedItem)
            }
        } catch {
            print("Failed to fetch news: \(error)")
        }

        // 3. Add educational content
        let educationalContent = BuffettFeedItem(
            emoji: "📚",
            title: "Investment Education",
            category: "Education",
            content: getDailyBuffettWisdom(),
            buffettInsight: getTodaysBuffettQuote(),
            timeAgo: "Today",
            likes: Int.random(in: 100...300),
            hasInvestmentOpportunity: false
        )
        realFeedItems.append(educationalContent)

        await MainActor.run {
            self.feedItems = realFeedItems
        }
    }

    private func generateFinanceBuddyAnalysis(for stock: StockPrice) async -> String {
        // Use AI to generate Finance Buddy style analysis
        let prompt = """
        Give me the tea on \(stock.symbol)! 📈
        Current price: $\(stock.price), Change: \(stock.changePercent)%

        Break it down like you're explaining to your bestie:
        - Is this stock giving main character energy? 💅
        - What's the vibe check on this company?
        - Should we be paying attention or nah? 👀
        - Any red flags or green flags? 🚩🟢

        Keep it under 100 words and make it actually fun to read!
        """

        do {
            let aiService = GeminiAIService()
            let response = try await aiService.generateChatResponse(message: prompt, personality: .financeBuddy)
            return response
        } catch {
            return "Okay bestie, \(stock.symbol) is giving mixed vibes rn 📊 The price moved \(stock.changePercent > 0 ? "up" : "down") \(abs(stock.changePercent))% but that's just market drama 💅 Focus on the long game and do your research before making moves! 🎯✨"
        }
    }

    private func generateFinanceBuddyPerspective(for article: NewsArticle) async -> String {
        let prompt = """
        Give me your hot take on this financial news bestie! 📰
        Title: \(article.title)
        Summary: \(article.content)

        Break it down in under 80 words:
        - What's the tea? ☕
        - Should we care or nah? 🤷‍♀️
        - Any opportunities for us? 👀
        - Keep it real but fun! ✨
        """

        do {
            let aiService = GeminiAIService()
            let response = try await aiService.generateChatResponse(message: prompt, personality: .financeBuddy)
            return response
        } catch {
            return "Okay so this news is giving mixed energy 📰 Market drama is normal bestie, don't let it stress you! Focus on your long-term goals and keep building that portfolio one step at a time 💪✨"
        }
    }

    private func getRelevantBuffettQuote(for stock: StockPrice) -> String {
        let quotes = [
            "Price is what you pay. Value is what you get.",
            "It's far better to buy a wonderful company at a fair price than a fair company at a wonderful price.",
            "Our favorite holding period is forever.",
            "Risk comes from not knowing what you're doing.",
            "The stock market is a voting machine in the short run, but a weighing machine in the long run."
        ]
        return quotes.randomElement()!
    }

    private func getNewsRelevantQuote() -> String {
        return "Be fearful when others are greedy and greedy when others are fearful."
    }

    private func getGenZInsight(for stock: StockPrice) -> String {
        let insights = [
            "Remember: you're not just buying stocks, you're buying your future! 🚀",
            "Investing is like leveling up in a game - patience = better rewards! 🎮",
            "Don't let FOMO make your financial decisions, bestie! 💅",
            "Small investments today = big flex tomorrow! 💰",
            "Research first, invest second. That's the vibe! 📚✨",
            "Your portfolio is your main character moment! 👑",
            "Consistency beats perfection every single time! 🎯"
        ]
        return insights.randomElement() ?? insights[0]
    }

    private func getGenZNewsInsight() -> String {
        let newsInsights = [
            "Market drama is just noise - stay focused on your goals! 🎯",
            "News moves fast, but good investments move slow! 🐌💰",
            "Don't let headlines make you panic sell, bestie! 💅",
            "Every market dip is a potential opportunity! 📉➡️📈",
            "Stay informed but don't let news control your emotions! 🧘‍♀️",
            "Remember: you're investing for years, not days! ⏰",
            "Market volatility = normal. Your strategy = consistent! ✨"
        ]
        return newsInsights.randomElement() ?? newsInsights[0]
    }

    private func generateStockHashtags(for symbol: String) -> [String] {
        let baseHashtags = ["#\(symbol)", "#Stocks", "#Investing", "#FinanceBuddy"]
        let trendingHashtags = ["#StockTok", "#InvestingTips", "#MarketVibes", "#MoneyMoves", "#PortfolioGoals"]
        let symbolSpecific: [String]

        switch symbol {
        case "AAPL": symbolSpecific = ["#Apple", "#Tech", "#iPhone", "#Innovation"]
        case "TSLA": symbolSpecific = ["#Tesla", "#ElonMusk", "#EV", "#FutureTech"]
        case "BRK.A": symbolSpecific = ["#Berkshire", "#ValueInvesting", "#WarrenBuffett"]
        case "NVDA": symbolSpecific = ["#NVIDIA", "#AI", "#Gaming", "#Chips"]
        default: symbolSpecific = ["#BlueChip", "#LongTerm"]
        }

        return (baseHashtags + trendingHashtags.shuffled().prefix(2) + symbolSpecific.prefix(2)).map { $0 }
    }

    private func generateNewsHashtags(for title: String) -> [String] {
        let baseHashtags = ["#FinanceNews", "#MarketUpdate", "#NewsTea"]
        var specificHashtags: [String] = []

        let lowercaseTitle = title.lowercased()
        if lowercaseTitle.contains("crypto") || lowercaseTitle.contains("bitcoin") {
            specificHashtags.append(contentsOf: ["#Crypto", "#Bitcoin", "#DeFi"])
        }
        if lowercaseTitle.contains("fed") || lowercaseTitle.contains("interest") {
            specificHashtags.append(contentsOf: ["#FedWatch", "#InterestRates", "#Economy"])
        }
        if lowercaseTitle.contains("inflation") {
            specificHashtags.append(contentsOf: ["#Inflation", "#Economy", "#CostOfLiving"])
        }
        if lowercaseTitle.contains("tech") || lowercaseTitle.contains("ai") {
            specificHashtags.append(contentsOf: ["#TechNews", "#AI", "#Innovation"])
        }

        let trendingHashtags = ["#Breaking", "#MarketMoves", "#FinTok", "#MoneyTalk"]
        return (baseHashtags + specificHashtags.prefix(2) + trendingHashtags.shuffled().prefix(2)).map { $0 }
    }

    private func getTodaysBuffettQuote() -> String {
        let dailyQuotes = [
            "Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1.",
            "Someone's sitting in the shade today because someone planted a tree a long time ago.",
            "The most important investment you can make is in yourself.",
            "Diversification is protection against ignorance. It makes little sense if you know what you are doing.",
            "Time is the friend of the wonderful business, the enemy of the mediocre."
        ]
        return dailyQuotes.randomElement()!
    }

    private func getDailyBuffettWisdom() -> String {
        let wisdomPieces = [
            "Focus on companies with strong competitive advantages - what I call 'economic moats.' These businesses can maintain pricing power and market share over decades.",
            "Look for businesses that are so simple that even a fool can run them, because eventually one will. The best investments are in companies with sustainable competitive advantages.",
            "When you find a truly exceptional business, the price you pay becomes less important than the quality of what you're buying. Time will reward patience with great companies.",
            "The key to successful investing is not predicting the future, but finding businesses that will thrive regardless of economic conditions. Focus on necessity, not luxury.",
            "Your best investment is always in developing your own skills and knowledge. The more you learn about business and investing, the better your decisions will become."
        ]
        return wisdomPieces.randomElement()!
    }

    private func generateActionItems(for stock: StockPrice) -> [String] {
        return [
            "Research the company's annual report",
            "Analyze competitive position",
            "Review management track record",
            "Consider long-term growth prospects"
        ]
    }
}

// MARK: - Buffett Portfolio Card
struct BuffettPortfolioCard: View {
    @Environment(\.theme) var theme

    var body: some View {
        VStack(spacing: 16) {
            // Header with chart icon
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .font(.title2)
                    .foregroundColor(theme.accent)

                Text("Your Wealth Journey")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(theme.onSurface)

                Spacer()
            }

            // Portfolio Value Section
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Building wealth the Buffett way")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))

                    Text("$12,847.50")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("+$847.50 (7.1%)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.success)
                }
            }

            // Stats Section
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Time in Market")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.6))
                    Text("247 days")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Compound Growth")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.6))
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.accent)
                }
            }
        }
        .padding(20)
        .glassmorphicCard(theme: theme, cornerRadius: 20)
    }
}

// MARK: - Buffett Feed Card
struct BuffettFeedCard: View {
    let item: BuffettFeedItem
    @State private var isLiked = false
    @State private var isBookmarked = false
    @Environment(\.theme) var theme

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            
            VStack(alignment: .leading, spacing: 12) {
                // Header
                HStack {
                    HStack(spacing: 8) {
                        Text(item.emoji)
                            .font(.title2)

                        VStack(alignment: .leading, spacing: 2) {
                            HStack {
                                Text(item.title)
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(theme.onSurface)

                                if item.isViralContent {
                                    Image(systemName: "flame.fill")
                                        .foregroundColor(.orange)
                                        .font(.caption)
                                }
                            }

                            HStack {
                                Text(item.category)
                                    .font(.caption)
                                    .foregroundColor(theme.onSurface.opacity(0.7))

                                if item.trendingScore > 70 {
                                    Text("• TRENDING")
                                        .font(.caption2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.pink)
                                }
                            }
                        }
                    }

                    Spacer()

                    Text(item.timeAgo)
                        .font(.caption2)
                        .foregroundColor(theme.onSurface.opacity(0.6))
                }

                // Content
                Text(item.content)
                    .font(.subheadline)
                    .foregroundColor(theme.onSurface.opacity(0.9))
                    .lineLimit(4)

                // Hashtags
                if !item.hashtags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(item.hashtags, id: \.self) { hashtag in
                                Text(hashtag)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(theme.accent)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(theme.accent.opacity(0.15))
                                    )
                            }
                        }
                        .padding(.horizontal, 1)
                    }
                }

                // Finance Buddy's Insight
                if let insight = item.buffettInsight {
                    HStack(spacing: 8) {
                        Image(systemName: "sparkles")
                            .font(.caption)
                            .foregroundColor(theme.accent)

                        Text("Finance Buddy Says: \(insight)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(theme.onSurface)
                            .italic()
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(theme.accent.opacity(0.1))
                            .stroke(theme.accent.opacity(0.3), lineWidth: 1)
                    )
                }
                
                // TikTok-style Actions
                HStack(spacing: 20) {
                    // Like button with animation
                    Button(action: {
                        withAnimation(.spring(response: 0.3)) {
                            isLiked.toggle()
                        }
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: isLiked ? "heart.fill" : "heart")
                                .foregroundColor(isLiked ? .red : theme.onSurface.opacity(0.6))
                                .font(.system(size: 16))
                                .scaleEffect(isLiked ? 1.2 : 1.0)
                            Text("\(item.likes + (isLiked ? 1 : 0))")
                                .font(.caption)
                                .foregroundColor(theme.onSurface.opacity(0.6))
                        }
                    }

                    // Comment button
                    Button(action: {}) {
                        HStack(spacing: 4) {
                            Image(systemName: "bubble.right")
                                .foregroundColor(theme.onSurface.opacity(0.6))
                                .font(.system(size: 16))
                            Text("\(item.comments)")
                                .font(.caption)
                                .foregroundColor(theme.onSurface.opacity(0.6))
                        }
                    }

                    // Share button
                    Button(action: {}) {
                        HStack(spacing: 4) {
                            Image(systemName: "arrowshape.turn.up.right")
                                .foregroundColor(theme.onSurface.opacity(0.6))
                                .font(.system(size: 16))
                            Text("\(item.shares)")
                                .font(.caption)
                                .foregroundColor(theme.onSurface.opacity(0.6))
                        }
                    }

                    Spacer()

                    // Bookmark button
                    Button(action: {
                        withAnimation(.spring(response: 0.3)) {
                            isBookmarked.toggle()
                        }
                    }) {
                        Image(systemName: isBookmarked ? "bookmark.fill" : "bookmark")
                            .foregroundColor(isBookmarked ? theme.accent : theme.onSurface.opacity(0.6))
                            .font(.system(size: 16))
                    }

                    // Investment opportunity
                    if item.hasInvestmentOpportunity {
                        Button(action: {}) {
                            Text("Invest")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.black)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(theme.success)
                                )
                        }
                    }
                }
            }
            .padding(16)
        }
        .glassmorphicCard(theme: theme)
    }
}

// MARK: - TikTok-Style Feed Item Model
struct BuffettFeedItem: Identifiable {
    let id = UUID()
    let emoji: String
    let title: String
    let category: String
    let content: String
    let buffettInsight: String?
    let timeAgo: String
    let likes: Int
    let hasInvestmentOpportunity: Bool

    // TikTok-style features
    let hashtags: [String]
    let isViralContent: Bool
    let trendingScore: Int
    let comments: Int
    let shares: Int
    let isLiked: Bool
    let isBookmarked: Bool

    init(emoji: String, title: String, category: String, content: String, buffettInsight: String?, timeAgo: String, likes: Int, hasInvestmentOpportunity: Bool, hashtags: [String] = [], isViralContent: Bool = false, trendingScore: Int = 0, comments: Int = 0, shares: Int = 0, isLiked: Bool = false, isBookmarked: Bool = false) {
        self.emoji = emoji
        self.title = title
        self.category = category
        self.content = content
        self.buffettInsight = buffettInsight
        self.timeAgo = timeAgo
        self.likes = likes
        self.hasInvestmentOpportunity = hasInvestmentOpportunity
        self.hashtags = hashtags
        self.isViralContent = isViralContent
        self.trendingScore = trendingScore
        self.comments = comments
        self.shares = shares
        self.isLiked = isLiked
        self.isBookmarked = isBookmarked
    }
    
    static let mockData: [BuffettFeedItem] = [
        BuffettFeedItem(
            emoji: "🍎",
            title: "Apple Inc. (AAPL)",
            category: "Value Investment",
            content: "Apple continues to demonstrate exceptional brand loyalty and ecosystem strength. The company's services revenue grew 16% YoY, showing the power of recurring revenue streams.",
            buffettInsight: "I love businesses with wide economic moats. Apple's ecosystem creates incredible customer stickiness.",
            timeAgo: "2h ago",
            likes: 247,
            hasInvestmentOpportunity: true
        ),
        BuffettFeedItem(
            emoji: "🏦",
            title: "Banking Sector Analysis",
            category: "Market Insight",
            content: "Regional banks are showing strong fundamentals with improving net interest margins. However, commercial real estate exposure remains a concern for some institutions.",
            buffettInsight: "Banking is a business where you can't afford to make big mistakes. Stick to the well-capitalized ones.",
            timeAgo: "4h ago",
            likes: 156,
            hasInvestmentOpportunity: false
        ),
        BuffettFeedItem(
            emoji: "⚡",
            title: "Renewable Energy Boom",
            category: "Future Trends",
            content: "Solar and wind energy costs have dropped 70% in the past decade. Berkshire Hathaway Energy continues to invest heavily in renewable infrastructure.",
            buffettInsight: "The best time to plant a tree was 20 years ago. The second best time is now. Same with energy infrastructure.",
            timeAgo: "6h ago",
            likes: 312,
            hasInvestmentOpportunity: true
        ),
        BuffettFeedItem(
            emoji: "📚",
            title: "The Power of Reading",
            category: "Education",
            content: "Warren Buffett reads 500 pages a day. Knowledge compounds just like money. Start building your investment knowledge library today.",
            buffettInsight: "Read everything you can about businesses and investing. Knowledge is the best investment you can make.",
            timeAgo: "8h ago",
            likes: 428,
            hasInvestmentOpportunity: false
        )
    ]
}

#Preview {
    BuffettInspiredFeedView()
}
