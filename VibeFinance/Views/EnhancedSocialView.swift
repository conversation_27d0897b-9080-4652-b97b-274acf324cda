//
//  EnhancedSocialView.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI

struct EnhancedSocialView: View {
    @Environment(\.theme) var theme
    @EnvironmentObject var squadManager: SquadManager
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @StateObject private var socialManager = SocialManager()
    @State private var selectedTab: SocialTab = .squads
    @State private var showingCreateSquad = false
    @State private var showingSquadDetail: Squad?
    @State private var showingInviteSheet = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Enhanced header with live activity
                socialHeader
                
                // Tab selector
                socialTabSelector
                
                // Content based on selected tab
                ScrollView {
                    VStack(spacing: 20) {
                        switch selectedTab {
                        case .squads:
                            squadsContent
                        case .activity:
                            activityContent
                        case .leaderboard:
                            leaderboardContent
                        case .discover:
                            discoverContent
                        }
                    }
                    .padding(16)
                }
            }
            .navigationTitle("Social Hub 👥")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingCreateSquad = true
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(.purple)
                            .font(.title2)
                    }
                }
            }
            .onAppear {
                Task {
                    await socialManager.loadSocialData()
                }
            }
            .sheet(isPresented: $showingCreateSquad) {
                Text("Create Squad Feature Coming Soon!")
                    .padding()
            }
            .sheet(item: $showingSquadDetail) { squad in
                EnhancedSquadDetailView(squad: squad)
            }
            .sheet(isPresented: $showingInviteSheet) {
                Text("Invite Friends Feature Coming Soon!")
                    .padding()
            }
        }
    }
    
    // MARK: - Social Header
    private var socialHeader: some View {
        VStack(spacing: 16) {
            // Live activity indicators
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Circle()
                            .fill(.green)
                            .frame(width: 8, height: 8)
                        Text("\(socialManager.onlineUsers) online")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    
                    Text("Active in \(socialManager.activeSquads) squads")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Quick actions
                HStack(spacing: 12) {
                    Button(action: {
                        showingInviteSheet = true
                    }) {
                        Image(systemName: "person.badge.plus")
                            .foregroundColor(.blue)
                            .font(.title3)
                    }
                    
                    Button(action: {
                        // Show notifications
                    }) {
                        ZStack {
                            Image(systemName: "bell")
                                .foregroundColor(.orange)
                                .font(.title3)
                            
                            if socialManager.unreadNotifications > 0 {
                                Circle()
                                    .fill(.red)
                                    .frame(width: 8, height: 8)
                                    .offset(x: 8, y: -8)
                            }
                        }
                    }
                }
            }
            
            // Social stats
            HStack(spacing: 20) {
                SocialStatCard(
                    title: "Squads",
                    value: "\(squadManager.userSquads.count)",
                    icon: "person.3.fill",
                    color: .purple
                )
                
                SocialStatCard(
                    title: "Followers",
                    value: "\(socialManager.followersCount)",
                    icon: "heart.fill",
                    color: .pink
                )
                
                SocialStatCard(
                    title: "Rank",
                    value: "#\(socialManager.socialRank)",
                    icon: "trophy.fill",
                    color: .orange
                )
                
                SocialStatCard(
                    title: "Streak",
                    value: "\(socialManager.socialStreak)",
                    icon: "flame.fill",
                    color: .red
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [.purple.opacity(0.5), .pink.opacity(0.5)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - Tab Selector
    private var socialTabSelector: some View {
        HStack(spacing: 0) {
            ForEach(SocialTab.allCases, id: \.self) { tab in
                Button(action: {
                    selectedTab = tab
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.icon)
                            .font(.title3)
                        Text(tab.title)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .purple : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedTab == tab ? .purple.opacity(0.2) : .clear)
                    )
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    // MARK: - Squads Content
    private var squadsContent: some View {
        VStack(spacing: 20) {
            // My squads section
            mySquadsSection
            
            // Trending squads
            trendingSquadsSection
            
            // Squad recommendations
            recommendedSquadsSection
        }
    }
    
    // MARK: - My Squads Section
    private var mySquadsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("My Squads 👥")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    // Navigate to all squads
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            if squadManager.userSquads.isEmpty {
                EmptySquadsView {
                    showingCreateSquad = true
                }
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(squadManager.userSquads.prefix(3), id: \.id) { squad in
                        EnhancedSquadCard(squad: squad) {
                            showingSquadDetail = squad
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Trending Squads Section
    private var trendingSquadsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Trending Squads 🔥")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("24h")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(.orange.opacity(0.2))
                    )
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(socialManager.trendingSquads, id: \.id) { squad in
                        TrendingSquadCard(squad: squad) {
                            showingSquadDetail = squad
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
        }
    }
    
    // MARK: - Recommended Squads Section
    private var recommendedSquadsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recommended for You ✨")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("Refresh") {
                    Task {
                        await socialManager.refreshRecommendations()
                    }
                }
                .font(.caption)
                .foregroundColor(.purple)
            }
            
            LazyVStack(spacing: 12) {
                ForEach(socialManager.recommendedSquads.prefix(5), id: \.id) { squad in
                    RecommendedSquadCard(squad: squad) {
                        showingSquadDetail = squad
                    }
                }
            }
        }
    }
    
    // MARK: - Activity Content
    private var activityContent: some View {
        VStack(spacing: 20) {
            Text("Activity Feed Coming Soon!")
                .font(.headline)
                .padding()
        }
    }
    
    // MARK: - Leaderboard Content
    private var leaderboardContent: some View {
        VStack(spacing: 20) {
            Text("Leaderboards Coming Soon!")
                .font(.headline)
                .padding()
        }
    }
    
    // MARK: - Discover Content
    private var discoverContent: some View {
        VStack(spacing: 20) {
            Text("Discover Coming Soon!")
                .font(.headline)
                .padding()
        }
    }
}

enum SocialTab: String, CaseIterable {
    case squads = "squads"
    case activity = "activity"
    case leaderboard = "leaderboard"
    case discover = "discover"
    
    var title: String {
        switch self {
        case .squads: return "Squads"
        case .activity: return "Activity"
        case .leaderboard: return "Rankings"
        case .discover: return "Discover"
        }
    }
    
    var icon: String {
        switch self {
        case .squads: return "person.3.fill"
        case .activity: return "bolt.fill"
        case .leaderboard: return "chart.bar.fill"
        case .discover: return "sparkles"
        }
    }
}

// MARK: - Supporting Views
struct SocialStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
}

#Preview {
    EnhancedSocialView()
        .environmentObject(SquadManager())
        .environmentObject(UserManager())
        .environmentObject(SubscriptionManager())
}
