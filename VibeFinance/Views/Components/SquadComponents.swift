//
//  SquadComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Squad Card
struct SquadCard: View {
    let squad: Squad
    let onJoin: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(squad.emoji)
                            .font(.title2)
                        Text(squad.name)
                            .font(.headline)
                            .fontWeight(.bold)
                    }

                    Text(squad.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }

                Spacer()

                // Member Count Badge
                Text("\(squad.memberCount) members")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple.opacity(0.2))
                    )
                    .foregroundColor(.purple)
            }
            
            // Stats Row
            HStack(spacing: 20) {
                VStack(alignment: .leading) {
                    Text("Total Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("$\(squad.totalValue, specifier: "%.0f")")
                        .font(.headline)
                        .fontWeight(.semibold)
                }

                VStack(alignment: .leading) {
                    Text("Return")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(squad.returnPercentage, specifier: "%.1f")%")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(squad.returnPercentage >= 0 ? .green : .red)
                }

                VStack(alignment: .leading) {
                    Text("Capacity")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(squad.memberCount)/\(squad.maxMembers)")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
            }

            // Investments
            VStack(alignment: .leading, spacing: 8) {
                Text("Active Investments")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                HStack {
                    ForEach(squad.investments.prefix(3), id: \.id) { investment in
                        Text(investment.symbol)
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color(.systemGray6))
                            )
                    }

                    if squad.investments.count > 3 {
                        Text("+\(squad.investments.count - 3)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
            }

            // Creator Info
            HStack {
                Image(systemName: "crown.fill")
                    .foregroundColor(.orange)
                    .font(.caption)
                Text("Created \(timeAgo(squad.createdAt))")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                // Capacity Indicator
                if squad.isFull {
                    HStack(spacing: 4) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                            .font(.caption)
                        Text("Full")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                } else if Double(squad.memberCount) / Double(squad.maxMembers) > 0.8 {
                    HStack(spacing: 4) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                        Text("Almost Full")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
            }
            
            // Join Button
            Button(action: onJoin) {
                Text("Join Squad")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private func timeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}



// MARK: - Stat Card
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 80)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - AI Squad Recommendation Card
struct AIRecommendationSquadCard: View {
    let title: String
    let description: String
    let expectedReturn: Double
    let memberCount: Int
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.purple)
                    .font(.title2)
                
                VStack(alignment: .leading) {
                    Text("AI Recommendation")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("\(expectedReturn, specifier: "%.1f")%")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Expected Return")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("AI Match")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("95%")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }

                Spacer()

                VStack(alignment: .trailing) {
                    Text("Members")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(memberCount)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
            }

            // Features
            HStack {
                Text("₿ Crypto")
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.orange.opacity(0.2))
                    )
                    .foregroundColor(.orange)

                Text("🚀 High Growth")
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.blue.opacity(0.2))
                    )
                    .foregroundColor(.blue)

                Spacer()
            }
            
            Button(action: {
                // Handle join action
            }) {
                Text("Join Recommended Squad")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Squad Leaderboard Row
struct SquadLeaderboardRow: View {
    let rank: Int
    let squad: Squad

    var body: some View {
        HStack(spacing: 12) {
            // Rank
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30)

            // Squad Info
            HStack(spacing: 8) {
                Text(squad.emoji)
                    .font(.title3)

                VStack(alignment: .leading, spacing: 2) {
                    Text(squad.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    Text("\(squad.memberCount) members")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // Return
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(squad.returnPercentage >= 0 ? "+" : "")\(squad.returnPercentage, specifier: "%.1f")%")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(squad.returnPercentage >= 0 ? .green : .red)
                Text("Return")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }

    private var rankColor: Color {
        switch rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .primary
        }
    }
}



#Preview {
    VStack(spacing: 20) {
        SquadCard(
            squad: Squad(
                name: "Tech Titans 💻",
                description: "Investing in the future of technology",
                emoji: "💻",
                creatorID: UUID(),
                isPublic: true,
                maxMembers: 500
            ),
            onJoin: {}
        )

        StatCard(title: "Total Value", value: "$4.2M", icon: "dollarsign.circle.fill", color: .green)
    }
    .padding()
}
