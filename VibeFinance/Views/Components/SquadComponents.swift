//
//  SquadComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Squad Card
struct SquadCard: View {
    let squad: Squad
    let onJoin: () -> Void
    @Environment(\.theme) var theme
    @State private var isLiked = false
    @State private var showingMembers = false

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(squad.emoji)
                            .font(.title2)
                        Text(squad.name)
                            .font(.headline)
                            .fontWeight(.bold)

                        // Viral indicators
                        if squad.memberCount > 100 {
                            Text("🔥")
                                .font(.caption)
                        }

                        if squad.totalValue > 10000 {
                            Text("💎")
                                .font(.caption)
                        }
                    }

                    Text(squad.description)
                        .font(.subheadline)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                        .lineLimit(2)
                }

                Spacer()

                // Member Count Badge with trending indicator
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(squad.memberCount) members")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(theme.primary.opacity(0.2))
                        )
                        .foregroundColor(theme.primary)

                    if squad.memberCount > squad.maxMembers * 3/4 {
                        Text("TRENDING")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                    }
                }
            }
            
            // Stats Row
            HStack(spacing: 20) {
                VStack(alignment: .leading) {
                    Text("Total Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("$\(squad.totalValue, specifier: "%.0f")")
                        .font(.headline)
                        .fontWeight(.semibold)
                }

                VStack(alignment: .leading) {
                    Text("Return")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(squad.returnPercentage, specifier: "%.1f")%")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(squad.returnPercentage >= 0 ? .green : .red)
                }

                VStack(alignment: .leading) {
                    Text("Capacity")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(squad.memberCount)/\(squad.maxMembers)")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
            }

            // Investments
            VStack(alignment: .leading, spacing: 8) {
                Text("Active Investments")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                HStack {
                    ForEach(squad.investments.prefix(3), id: \.id) { investment in
                        Text(investment.symbol)
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(Color(.systemGray6))
                            )
                    }

                    if squad.investments.count > 3 {
                        Text("+\(squad.investments.count - 3)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
            }

            // Creator Info
            HStack {
                Image(systemName: "crown.fill")
                    .foregroundColor(.orange)
                    .font(.caption)
                Text("Created \(timeAgo(squad.createdAt))")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                // Capacity Indicator
                if squad.isFull {
                    HStack(spacing: 4) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                            .font(.caption)
                        Text("Full")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                } else if Double(squad.memberCount) / Double(squad.maxMembers) > 0.8 {
                    HStack(spacing: 4) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                        Text("Almost Full")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
            }
            
            // Social Actions Row
            HStack(spacing: 12) {
                // Like button
                Button(action: {
                    withAnimation(.spring(response: 0.3)) {
                        isLiked.toggle()
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: isLiked ? "heart.fill" : "heart")
                            .foregroundColor(isLiked ? .red : .gray)
                            .font(.system(size: 16))
                            .scaleEffect(isLiked ? 1.2 : 1.0)
                        Text("\(squad.memberCount + (isLiked ? 1 : 0))")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }

                // Members button
                Button(action: { showingMembers = true }) {
                    HStack(spacing: 4) {
                        Image(systemName: "person.2")
                            .foregroundColor(.gray)
                            .font(.system(size: 16))
                        Text("Members")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }

                // Share button
                Button(action: {}) {
                    HStack(spacing: 4) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.gray)
                            .font(.system(size: 16))
                        Text("Share")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }

                Spacer()
            }

            // Join Button
            Button(action: onJoin) {
                Text(squad.isFull ? "Squad Full 😢" : "Join Squad! 🚀")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        LinearGradient(
                            colors: squad.isFull ? [.gray, .gray] : [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
            }
            .disabled(squad.isFull)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    private func timeAgo(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}



// MARK: - Squad Stat Card
struct SquadStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 80)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - AI Squad Recommendation Card
struct AIRecommendationSquadCard: View {
    let title: String
    let description: String
    let expectedReturn: Double
    let memberCount: Int
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.purple)
                    .font(.title2)
                
                VStack(alignment: .leading) {
                    Text("AI Recommendation")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("\(expectedReturn, specifier: "%.1f")%")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Expected Return")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("AI Match")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("95%")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }

                Spacer()

                VStack(alignment: .trailing) {
                    Text("Members")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(memberCount)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
            }

            // Features
            HStack {
                Text("₿ Crypto")
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.orange.opacity(0.2))
                    )
                    .foregroundColor(.orange)

                Text("🚀 High Growth")
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(Color.blue.opacity(0.2))
                    )
                    .foregroundColor(.blue)

                Spacer()
            }
            
            Button(action: {
                // Handle join action
            }) {
                Text("Join Recommended Squad")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Squad Leaderboard Row
struct SquadLeaderboardRow: View {
    let rank: Int
    let squad: Squad

    var body: some View {
        HStack(spacing: 12) {
            // Rank
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30)

            // Squad Info
            HStack(spacing: 8) {
                Text(squad.emoji)
                    .font(.title3)

                VStack(alignment: .leading, spacing: 2) {
                    Text(squad.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    Text("\(squad.memberCount) members")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // Return
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(squad.returnPercentage >= 0 ? "+" : "")\(squad.returnPercentage, specifier: "%.1f")%")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(squad.returnPercentage >= 0 ? .green : .red)
                Text("Return")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }

    private var rankColor: Color {
        switch rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .primary
        }
    }
}

// MARK: - Viral Squad Leaderboard
struct ViralSquadLeaderboard: View {
    @Environment(\.theme) var theme
    @State private var selectedPeriod: LeaderboardPeriod = .weekly

    enum LeaderboardPeriod: String, CaseIterable {
        case daily = "24H"
        case weekly = "7D"
        case monthly = "30D"
        case allTime = "ALL"
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading) {
                    Text("🔥 Viral Squad Rankings")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onBackground)
                    Text("Top performing squads this week")
                        .font(.subheadline)
                        .foregroundColor(theme.onBackground.opacity(0.7))
                }

                Spacer()

                // Period selector
                HStack(spacing: 4) {
                    ForEach(LeaderboardPeriod.allCases, id: \.self) { period in
                        Button(action: { selectedPeriod = period }) {
                            Text(period.rawValue)
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(selectedPeriod == period ? .white : theme.onBackground.opacity(0.7))
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(selectedPeriod == period ? .purple : .clear)
                                )
                        }
                    }
                }
            }

            // Top 3 squads
            VStack(spacing: 12) {
                ForEach(Array(mockTopSquads.enumerated()), id: \.element.id) { index, squad in
                    ViralSquadRankCard(squad: squad, rank: index + 1)
                }
            }

            // View all button
            Button(action: {}) {
                HStack {
                    Text("View Full Leaderboard")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    Image(systemName: "arrow.right")
                        .font(.caption)
                }
                .foregroundColor(.purple)
                .frame(maxWidth: .infinity)
                .frame(height: 40)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(.purple, lineWidth: 1)
                )
            }
        }
        .padding()
        .glassmorphicCard(theme: theme)
    }

    private var mockTopSquads: [Squad] {
        [
            Squad(name: "Crypto Queens 👑", description: "Dominating the crypto game", emoji: "👑", creatorID: UUID(), isPublic: true, maxMembers: 100),
            Squad(name: "Tech Titans 🚀", description: "Future tech investments", emoji: "🚀", creatorID: UUID(), isPublic: true, maxMembers: 150),
            Squad(name: "Green Energy Gang 🌱", description: "Sustainable investing", emoji: "🌱", creatorID: UUID(), isPublic: true, maxMembers: 80)
        ]
    }
}

struct ViralSquadRankCard: View {
    let squad: Squad
    let rank: Int
    @Environment(\.theme) var theme

    var body: some View {
        HStack(spacing: 12) {
            // Rank badge
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30, height: 30)
                .background(
                    Circle()
                        .fill(rankColor.opacity(0.2))
                )

            // Squad info
            HStack {
                Text(squad.emoji)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(squad.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(theme.onSurface)

                        if rank == 1 {
                            Text("🔥")
                                .font(.caption)
                        }
                    }

                    Text("+\(Int.random(in: 15...45))% this week")
                        .font(.caption)
                        .foregroundColor(.green)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text("$\(Int.random(in: 50000...200000))")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface)

                    Text("\(squad.memberCount) members")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(rank == 1 ? AnyShapeStyle(Color.purple.opacity(0.1)) : AnyShapeStyle(theme.surface))
                .stroke(rank == 1 ? Color.purple.opacity(0.3) : Color.clear, lineWidth: 1)
        )
    }

    private var rankColor: Color {
        switch rank {
        case 1: return .orange
        case 2: return .gray
        case 3: return .brown
        default: return .primary
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        SquadCard(
            squad: Squad(
                name: "Tech Titans 💻",
                description: "Investing in the future of technology",
                emoji: "💻",
                creatorID: UUID(),
                isPublic: true,
                maxMembers: 500
            ),
            onJoin: {}
        )

        SquadStatCard(title: "Total Value", value: "$4.2M", icon: "dollarsign.circle.fill", color: .green)
    }
    .padding()
}
