//
//  OptimizedAsyncImage.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct OptimizedAsyncImage<Content: View, Placeholder: View>: View {
    let url: URL?
    let targetSize: CGSize?
    let content: (Image) -> Content
    let placeholder: () -> Placeholder
    
    @State private var image: UIImage?
    @State private var isLoading = false
    @State private var loadingTask: Task<Void, Never>?
    
    private let imageCache = ImageCacheManager.shared
    
    init(
        url: URL?,
        targetSize: CGSize? = nil,
        @ViewBuilder content: @escaping (Image) -> Content,
        @ViewBuilder placeholder: @escaping () -> Placeholder
    ) {
        self.url = url
        self.targetSize = targetSize
        self.content = content
        self.placeholder = placeholder
    }
    
    var body: some View {
        Group {
            if let image = image {
                content(Image(uiImage: image))
            } else {
                placeholder()
                    .onAppear {
                        loadImage()
                    }
                    .onChange(of: url) {
                        loadImage()
                    }
            }
        }
        .onDisappear {
            cancelLoading()
        }
    }
    
    private func loadImage() {
        guard let url = url else { return }
        
        // Cancel any existing loading task
        cancelLoading()
        
        // Check if image is already loaded
        if image != nil { return }
        
        isLoading = true
        
        loadingTask = Task {
            let loadedImage = await imageCache.loadImage(from: url, size: targetSize)
            
            await MainActor.run {
                if !Task.isCancelled {
                    self.image = loadedImage
                    self.isLoading = false
                }
            }
        }
    }
    
    private func cancelLoading() {
        loadingTask?.cancel()
        loadingTask = nil
        isLoading = false
    }
}

// MARK: - Convenience Initializers
extension OptimizedAsyncImage where Content == Image, Placeholder == ProgressView<EmptyView, EmptyView> {
    init(url: URL?, targetSize: CGSize? = nil) {
        self.init(
            url: url,
            targetSize: targetSize,
            content: { $0 },
            placeholder: { ProgressView() }
        )
    }
}

extension OptimizedAsyncImage where Placeholder == ProgressView<EmptyView, EmptyView> {
    init(
        url: URL?,
        targetSize: CGSize? = nil,
        @ViewBuilder content: @escaping (Image) -> Content
    ) {
        self.init(
            url: url,
            targetSize: targetSize,
            content: content,
            placeholder: { ProgressView() }
        )
    }
}

// MARK: - Optimized Stock Logo Component
struct OptimizedStockLogo: View {
    let symbol: String
    let size: CGFloat
    
    private var logoURL: URL? {
        // This would typically use a stock logo API
        URL(string: "https://logo.clearbit.com/\(symbol.lowercased()).com")
    }
    
    var body: some View {
        OptimizedAsyncImage(
            url: logoURL,
            targetSize: CGSize(width: size, height: size)
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: size, height: size)
                .clipShape(RoundedRectangle(cornerRadius: size * 0.1))
        } placeholder: {
            RoundedRectangle(cornerRadius: size * 0.1)
                .fill(Color(.systemGray5))
                .frame(width: size, height: size)
                .overlay(
                    Text(String(symbol.prefix(2)))
                        .font(.system(size: size * 0.3, weight: .semibold))
                        .foregroundColor(.secondary)
                )
        }
    }
}

// MARK: - Optimized News Image Component
struct OptimizedNewsImage: View {
    let imageURL: URL?
    let width: CGFloat
    let height: CGFloat
    
    var body: some View {
        OptimizedAsyncImage(
            url: imageURL,
            targetSize: CGSize(width: width, height: height)
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: width, height: height)
                .clipped()
                .cornerRadius(12)
        } placeholder: {
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray5))
                .frame(width: width, height: height)
                .overlay(
                    Image(systemName: "photo")
                        .font(.title2)
                        .foregroundColor(.secondary)
                )
        }
    }
}

// MARK: - Optimized Profile Avatar Component
struct OptimizedProfileAvatar: View {
    let imageURL: URL?
    let size: CGFloat
    let fallbackText: String
    
    var body: some View {
        OptimizedAsyncImage(
            url: imageURL,
            targetSize: CGSize(width: size, height: size)
        ) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: size, height: size)
                .clipShape(Circle())
        } placeholder: {
            Circle()
                .fill(Color.purple.opacity(0.2))
                .frame(width: size, height: size)
                .overlay(
                    Text(String(fallbackText.prefix(1)).uppercased())
                        .font(.system(size: size * 0.4, weight: .semibold))
                        .foregroundColor(.purple)
                )
        }
    }
}

// MARK: - Preloading Image Grid
struct PreloadingImageGrid<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let columns: [GridItem]
    let imageURLKeyPath: KeyPath<Item, URL?>
    let targetSize: CGSize
    let content: (Item) -> Content
    
    @State private var preloadedItems: Set<Item.ID> = []
    
    init(
        items: [Item],
        columns: [GridItem],
        imageURL: KeyPath<Item, URL?>,
        targetSize: CGSize,
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.columns = columns
        self.imageURLKeyPath = imageURL
        self.targetSize = targetSize
        self.content = content
    }
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(items) { item in
                content(item)
                    .onAppear {
                        preloadImageIfNeeded(for: item)
                    }
            }
        }
        .onAppear {
            preloadVisibleImages()
        }
    }
    
    private func preloadImageIfNeeded(for item: Item) {
        guard !preloadedItems.contains(item.id),
              let url = item[keyPath: imageURLKeyPath] else { return }
        
        preloadedItems.insert(item.id)
        
        Task {
            await ImageCacheManager.shared.loadImage(from: url, size: targetSize)
        }
    }
    
    private func preloadVisibleImages() {
        // Preload first few images that will be visible
        let visibleItems = Array(items.prefix(6))
        let urls = visibleItems.compactMap { $0[keyPath: imageURLKeyPath] }
        
        Task {
            await ImageCacheManager.shared.preloadImages(
                urls: urls,
                sizes: Array(repeating: targetSize, count: urls.count)
            )
        }
    }
}

// MARK: - Performance-Optimized List
struct OptimizedList<Item: Identifiable, Content: View>: View {
    let items: [Item]
    let content: (Item) -> Content
    
    @State private var visibleItems: Set<Item.ID> = []
    private let visibilityThreshold = 10
    
    init(
        items: [Item],
        @ViewBuilder content: @escaping (Item) -> Content
    ) {
        self.items = items
        self.content = content
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(items) { item in
                    content(item)
                        .onAppear {
                            handleItemAppear(item)
                        }
                        .onDisappear {
                            handleItemDisappear(item)
                        }
                }
            }
            .padding()
        }
    }
    
    private func handleItemAppear(_ item: Item) {
        visibleItems.insert(item.id)
        
        // Trigger memory optimization if too many items are visible
        if visibleItems.count > visibilityThreshold {
            optimizeMemoryUsage()
        }
    }
    
    private func handleItemDisappear(_ item: Item) {
        visibleItems.remove(item.id)
    }
    
    private func optimizeMemoryUsage() {
        // Clear image cache for items that are no longer visible
        ImageCacheManager.shared.clearMemoryCache()
        
        // Notify performance manager
        NotificationCenter.default.post(name: .performanceOptimizationNeeded, object: nil)
    }
}

// MARK: - Optimized Feed Item Component
struct OptimizedFeedItem: View {
    let feedItem: FeedItem
    let onAction: (FeedAction) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with optimized avatar
            HStack {
                OptimizedProfileAvatar(
                    imageURL: nil, // Would come from user profile
                    size: 40,
                    fallbackText: "AI"
                )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("VibeFinance AI")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text(feedItem.createdAt, style: .relative)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                CategoryBadge(category: feedItem.content.category)
            }
            
            // Content
            VStack(alignment: .leading, spacing: 8) {
                Text(feedItem.content.summary)
                    .font(.body)
                    .lineLimit(nil)
                
                // Tags
                if !feedItem.content.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(feedItem.content.tags, id: \.self) { tag in
                                Text("#\(tag)")
                                    .font(.caption)
                                    .foregroundColor(.purple)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.purple.opacity(0.1))
                                    )
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
            
            // Actions
            HStack(spacing: 20) {
                FeedActionButton(
                    icon: "heart",
                    fillIcon: "heart.fill",
                    count: feedItem.getReactionCount(for: .fire),
                    isActive: feedItem.reactions.contains { $0.type == .fire }
                ) {
                    onAction(.like)
                }
                
                FeedActionButton(
                    icon: "bookmark",
                    fillIcon: "bookmark.fill",
                    isActive: feedItem.isBookmarked
                ) {
                    onAction(.bookmark)
                }
                
                FeedActionButton(
                    icon: "square.and.arrow.up",
                    fillIcon: "square.and.arrow.up",
                    count: 0,
                    isActive: false
                ) {
                    onAction(.share)
                }
                
                Spacer()
                
                VibeScoreView(score: feedItem.content.vibeScore)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

#Preview {
    VStack(spacing: 20) {
        OptimizedStockLogo(symbol: "AAPL", size: 60)
        
        OptimizedNewsImage(
            imageURL: URL(string: "https://example.com/news.jpg"),
            width: 300,
            height: 200
        )
        
        OptimizedProfileAvatar(
            imageURL: nil,
            size: 50,
            fallbackText: "JD"
        )
    }
    .padding()
}
