//
//  SimulatorComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Simulator Portfolio Header
struct SimulatorPortfolioHeader: View {
    let virtualBalance: Double
    let totalValue: Double
    let dailyChange: Double
    let dailyChangePercent: Double
    
    var body: some View {
        VStack(spacing: 16) {
            // Virtual Money Badge
            HStack {
                Image(systemName: "gamecontroller.fill")
                    .foregroundColor(.purple)
                Text("Virtual Trading")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                Spacer()
                Text("Practice Mode")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.purple.opacity(0.1))
            )
            
            // Portfolio Value
            VStack(spacing: 8) {
                Text("Total Portfolio Value")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text("$\(totalValue, specifier: "%.2f")")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                HStack(spacing: 8) {
                    Image(systemName: dailyChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                        .font(.caption)
                        .foregroundColor(dailyChange >= 0 ? .green : .red)
                    
                    Text("$\(abs(dailyChange), specifier: "%.2f")")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(dailyChange >= 0 ? .green : .red)
                    
                    Text("(\(dailyChangePercent >= 0 ? "+" : "")\(dailyChangePercent, specifier: "%.2f")%)")
                        .font(.caption)
                        .foregroundColor(dailyChange >= 0 ? .green : .red)
                }
            }
            
            // Quick Stats
            HStack(spacing: 20) {
                SimulatorStatCard(
                    title: "Cash",
                    value: String(format: "$%.2f", virtualBalance),
                    icon: "dollarsign.circle.fill",
                    color: .green
                )
                
                SimulatorStatCard(
                    title: "Invested",
                    value: String(format: "$%.2f", totalValue - virtualBalance),
                    icon: "chart.line.uptrend.xyaxis",
                    color: .blue
                )
                
                SimulatorStatCard(
                    title: "Return",
                    value: "\(dailyChangePercent >= 0 ? "+" : "")\(String(format: "%.1f", dailyChangePercent))%",
                    icon: "percent",
                    color: dailyChange >= 0 ? .green : .red
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

// MARK: - Simulator Stat Card
struct SimulatorStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
            
            Text(value)
                .font(.caption)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Simulator Tab Selector
struct SimulatorTabSelector: View {
    @Binding var selectedTab: Int
    
    private let tabs = [
        ("Portfolio", "chart.pie.fill"),
        ("Market", "chart.line.uptrend.xyaxis"),
        ("History", "clock.fill"),
        ("Leaderboard", "trophy.fill")
    ]
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = index
                    }
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.1)
                            .font(.caption)
                        Text(tab.0)
                            .font(.caption2)
                    }
                    .foregroundColor(selectedTab == index ? .purple : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(Color(.systemBackground))
        .overlay(
            Rectangle()
                .fill(Color.purple)
                .frame(height: 2)
                .offset(x: CGFloat(selectedTab) * (UIScreen.main.bounds.width / 4) - UIScreen.main.bounds.width / 2 + UIScreen.main.bounds.width / 8)
                .animation(.easeInOut(duration: 0.3), value: selectedTab),
            alignment: .bottom
        )
    }
}

// MARK: - Simulator Portfolio View
struct SimulatorPortfolioView: View {
    @EnvironmentObject var simulatorManager: SimulatorManager
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if simulatorManager.holdings.isEmpty {
                    SimulatorEmptyPortfolioView()
                } else {
                    ForEach(simulatorManager.holdings, id: \.symbol) { holding in
                        SimulatorHoldingCard(holding: holding)
                    }
                }
            }
            .padding()
        }
    }
}

// MARK: - Simulator Empty Portfolio View
struct SimulatorEmptyPortfolioView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "chart.pie")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("Start Your Investment Journey")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("You have $10,000 in virtual money to practice investing. Search for stocks and start building your portfolio!")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button(action: {
                // Trigger stock search
            }) {
                HStack {
                    Image(systemName: "magnifyingglass")
                    Text("Find Stocks to Invest")
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple)
                )
            }
        }
        .padding(40)
    }
}

// MARK: - Simulator Holding Card
struct SimulatorHoldingCard: View {
    let holding: SimulatorHolding
    
    var body: some View {
        HStack(spacing: 12) {
            // Stock Icon
            Circle()
                .fill(Color.blue.opacity(0.1))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(holding.symbol.prefix(2))
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                )
            
            // Stock Info
            VStack(alignment: .leading, spacing: 4) {
                Text(holding.symbol)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("\(Int(holding.shares)) shares")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Performance
            VStack(alignment: .trailing, spacing: 4) {
                Text("$\(holding.currentValue, specifier: "%.2f")")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                HStack(spacing: 4) {
                    Image(systemName: holding.gainLoss >= 0 ? "arrow.up.right" : "arrow.down.right")
                        .font(.caption)
                    Text("$\(abs(holding.gainLoss), specifier: "%.2f")")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(holding.gainLoss >= 0 ? .green : .red)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Simulator Market View
struct SimulatorMarketView: View {
    let onStockSelected: (StockPrice) -> Void
    @State private var marketData: [StockPrice] = []
    @State private var isLoading = true
    
    private let stockService = StockServiceManager.shared
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if isLoading {
                    ForEach(0..<5, id: \.self) { _ in
                        SimulatorMarketCardSkeleton()
                    }
                } else {
                    ForEach(marketData, id: \.symbol) { stock in
                        SimulatorMarketCard(stock: stock) {
                            onStockSelected(stock)
                        }
                    }
                }
            }
            .padding()
        }
        .onAppear {
            loadMarketData()
        }
    }
    
    private func loadMarketData() {
        Task {
            let symbols = ["AAPL", "TSLA", "NVDA", "GOOGL", "MSFT", "AMZN", "META", "NFLX"]
            var stocks: [StockPrice] = []
            
            for symbol in symbols {
                if let stock = await stockService.getStockData(symbol: symbol) {
                    stocks.append(stock)
                }
            }
            
            await MainActor.run {
                self.marketData = stocks
                self.isLoading = false
            }
        }
    }
}

// MARK: - Simulator Market Card
struct SimulatorMarketCard: View {
    let stock: StockPrice
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // Stock Icon
                Circle()
                    .fill(Color.purple.opacity(0.1))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(stock.symbol.prefix(2))
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.purple)
                    )
                
                // Stock Info
                VStack(alignment: .leading, spacing: 2) {
                    Text(stock.symbol)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(getCompanyName(for: stock.symbol))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Price Info
                VStack(alignment: .trailing, spacing: 2) {
                    Text("$\(stock.price, specifier: "%.2f")")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    HStack(spacing: 4) {
                        Image(systemName: stock.change >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        Text("\(stock.changePercent >= 0 ? "+" : "")\(stock.changePercent, specifier: "%.2f")%")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(stock.change >= 0 ? .green : .red)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func getCompanyName(for symbol: String) -> String {
        switch symbol {
        case "AAPL": return "Apple Inc."
        case "TSLA": return "Tesla Inc."
        case "NVDA": return "NVIDIA Corp."
        case "GOOGL": return "Alphabet Inc."
        case "MSFT": return "Microsoft Corp."
        case "AMZN": return "Amazon.com Inc."
        case "META": return "Meta Platforms Inc."
        case "NFLX": return "Netflix Inc."
        default: return "Company"
        }
    }
}

// MARK: - Simulator Market Card Skeleton
struct SimulatorMarketCardSkeleton: View {
    @State private var isAnimating = false
    
    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(Color(.systemGray5))
                .frame(width: 40, height: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 60, height: 16)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 100, height: 12)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 80, height: 16)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 60, height: 12)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .opacity(isAnimating ? 0.5 : 1.0)
        .animation(
            Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true),
            value: isAnimating
        )
        .onAppear {
            isAnimating = true
        }
    }
}
