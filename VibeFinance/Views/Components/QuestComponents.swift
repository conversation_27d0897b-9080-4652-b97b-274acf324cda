//
//  QuestComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Quest Card
struct QuestCard: View {
    let quest: Quest
    let onComplete: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                // Category Icon
                Text(quest.category.emoji)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(quest.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(quest.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // Difficulty Badge
                Text(quest.difficulty.rawValue.capitalized)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(difficultyColor.opacity(0.2))
                    )
                    .foregroundColor(difficultyColor)
            }
            
            // Progress Bar
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("Progress")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(quest.tasks.count) tasks")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: 0.6) // This would be calculated from actual progress
                    .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                    .scaleEffect(x: 1, y: 1.5, anchor: .center)
            }
            
            // Rewards
            HStack {
                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    Text("\(quest.xpReward) XP")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                if quest.estimatedTime > 0 {
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .foregroundColor(.secondary)
                            .font(.caption)
                        Text("\(quest.estimatedTime) min")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            // Action Button
            Button(action: onComplete) {
                Text("Start Quest")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 36)
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    private var difficultyColor: Color {
        switch quest.difficulty {
        case .beginner: return .green
        case .intermediate: return .orange
        case .advanced: return .red
        }
    }
}

// MARK: - Weekly Challenge Card
struct WeeklyChallengeCard: View {
    let challenge: WeeklyChallenge
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("🏆 Weekly Challenge")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(challenge.title)
                        .font(.title2)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Rank #\(challenge.currentRank)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                    Text("of \(challenge.participants)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Text(challenge.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Your Performance")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("+\(challenge.performance, specifier: "%.1f")%")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Time Remaining")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(challenge.timeRemaining)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                }
            }
            
            // Reward Info
            HStack {
                HStack(spacing: 4) {
                    Image(systemName: "dollarsign.circle.fill")
                        .foregroundColor(.green)
                    Text("\(challenge.reward) coins")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                
                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .foregroundColor(.orange)
                    Text("\(challenge.xpReward) XP")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [.purple.opacity(0.1), .pink.opacity(0.1)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    LinearGradient(
                        colors: [.purple, .pink],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .cornerRadius(16)
    }
}

// MARK: - Achievement Badge
struct AchievementBadge: View {
    let icon: String
    let title: String
    let description: String
    let isUnlocked: Bool
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(isUnlocked ? .orange : .gray)
                .frame(width: 40, height: 40)
            
            Text(title)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(isUnlocked ? .primary : .secondary)
                .multilineTextAlignment(.center)
            
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isUnlocked ? Color(.systemBackground) : Color(.secondarySystemBackground))
                .shadow(color: .black.opacity(isUnlocked ? 0.1 : 0.05), radius: 4, x: 0, y: 2)
        )
        .opacity(isUnlocked ? 1.0 : 0.6)
    }
}

// MARK: - Quest Category Filter
struct QuestCategoryFilter: View {
    @Binding var selectedCategory: QuestCategory?
    let categories = QuestCategory.allCases
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // All Categories Button
                Button(action: {
                    selectedCategory = nil
                }) {
                    Text("All")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(selectedCategory == nil ? Color.purple : Color(.secondarySystemBackground))
                        )
                        .foregroundColor(selectedCategory == nil ? .white : .primary)
                }
                
                ForEach(categories, id: \.self) { category in
                    Button(action: {
                        selectedCategory = category
                    }) {
                        HStack(spacing: 4) {
                            Text(category.emoji)
                            Text(category.rawValue.capitalized)
                                .font(.subheadline)
                                .fontWeight(.semibold)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(selectedCategory == category ? Color.purple : Color(.secondarySystemBackground))
                        )
                        .foregroundColor(selectedCategory == category ? .white : .primary)
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - Supporting Models for UI
struct WeeklyChallenge: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let reward: Int
    let xpReward: Int
    let participants: Int
    let timeRemaining: String
    let currentRank: Int
    let performance: Double
}

#Preview {
    VStack(spacing: 20) {
        QuestCard(
            quest: Quest(
                title: "Stock Market Basics",
                description: "Learn the fundamentals of stock market investing",
                category: .stocks,
                difficulty: .beginner,
                xpReward: 100,
                estimatedTime: 15,
                tasks: []
            ),
            onComplete: {}
        )
        
        WeeklyChallengeCard(
            challenge: WeeklyChallenge(
                title: "Beat the Market",
                description: "Outperform S&P 500 this week",
                reward: 5000,
                xpReward: 1000,
                participants: 1247,
                timeRemaining: "3d 14h",
                currentRank: 23,
                performance: 8.7
            )
        )
    }
    .padding()
}
