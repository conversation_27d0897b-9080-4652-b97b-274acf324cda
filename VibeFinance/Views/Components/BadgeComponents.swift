//
//  BadgeComponents.swift
//  VibeFinance - Badge Collection UI Components
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import SwiftUI

// MARK: - Badge Card Component

struct BadgeCard: View {
    let badge: Badge
    let onTap: () -> Void
    @Environment(\.theme) var theme
    @State private var isAnimating = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Badge Icon with Rarity Effects
                ZStack {
                    // Rarity background
                    Circle()
                        .fill(badge.rarityGradient)
                        .frame(width: 80, height: 80)
                        .overlay(
                            Circle()
                                .stroke(badge.rarityColor, lineWidth: 2)
                        )
                        .scaleEffect(isAnimating ? 1.05 : 1.0)
                        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: isAnimating)
                    
                    // Sparkle effects for rare badges
                    if badge.rarity.sparkleCount > 0 {
                        ForEach(0..<badge.rarity.sparkleCount, id: \.self) { index in
                            Image(systemName: "sparkle")
                                .font(.caption)
                                .foregroundColor(.white)
                                .offset(
                                    x: cos(Double(index) * 2 * .pi / Double(badge.rarity.sparkleCount)) * 45,
                                    y: sin(Double(index) * 2 * .pi / Double(badge.rarity.sparkleCount)) * 45
                                )
                                .opacity(badge.isUnlocked ? 1.0 : 0.3)
                        }
                    }
                    
                    // Main badge icon
                    Image(systemName: badge.icon)
                        .font(.title)
                        .foregroundColor(.white)
                        .opacity(badge.isUnlocked ? 1.0 : 0.4)
                }
                
                // Badge Info
                VStack(spacing: 4) {
                    Text(badge.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(badge.isUnlocked ? theme.onSurface : theme.onSurface.opacity(0.6))
                        .lineLimit(1)
                    
                    Text(badge.description)
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                        .lineLimit(2)
                        .multilineTextAlignment(.center)
                    
                    // Rarity indicator
                    Text(badge.rarity.displayName.uppercased())
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(badge.rarityColor)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(badge.rarityColor.opacity(0.2))
                        )
                }
                
                // Progress bar for locked badges
                if !badge.isUnlocked, let progress = badge.progress {
                    VStack(spacing: 4) {
                        HStack {
                            Text("\(progress.current)/\(progress.target)")
                                .font(.caption2)
                                .foregroundColor(theme.onSurface.opacity(0.8))
                            Spacer()
                            Text("\(Int(progress.percentage * 100))%")
                                .font(.caption2)
                                .fontWeight(.semibold)
                                .foregroundColor(badge.rarityColor)
                        }
                        
                        ProgressView(value: progress.percentage)
                            .progressViewStyle(LinearProgressViewStyle(tint: badge.rarityColor))
                            .scaleEffect(y: 0.8)
                    }
                }
            }
            .padding(16)
            .frame(width: 160, height: 200)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(badge.isUnlocked ? theme.surface : theme.surface.opacity(0.5))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(badge.isUnlocked ? badge.rarityColor.opacity(0.5) : theme.onSurface.opacity(0.1), lineWidth: 1)
                    )
                    .shadow(
                        color: badge.isUnlocked ? badge.rarityColor.opacity(0.3) : .clear,
                        radius: badge.isUnlocked ? 8 : 0,
                        x: 0,
                        y: 4
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            if badge.isUnlocked && badge.rarity.sparkleCount > 2 {
                isAnimating = true
            }
        }
    }
}

// MARK: - Badge Collection Header

struct BadgeCollectionHeader: View {
    let collection: BadgeCollection
    @Environment(\.theme) var theme
    
    var body: some View {
        VStack(spacing: 16) {
            // Collection Stats
            HStack(spacing: 20) {
                VStack {
                    Text("\(collection.unlockedBadges)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(theme.primary)
                    Text("Unlocked")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }
                
                VStack {
                    Text("\(collection.totalBadges)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)
                    Text("Total")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }
                
                VStack {
                    Text("\(Int(collection.completionPercentage * 100))%")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Complete")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }
            }
            
            // Progress Bar
            VStack(spacing: 8) {
                HStack {
                    Text("Collection Progress")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface)
                    Spacer()
                    Text("\(collection.unlockedBadges)/\(collection.totalBadges)")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }
                
                ProgressView(value: collection.completionPercentage)
                    .progressViewStyle(LinearProgressViewStyle(tint: theme.primary))
                    .scaleEffect(y: 1.5)
            }
            
            // Rarity Breakdown
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(BadgeRarity.allCases, id: \.self) { rarity in
                    let count = collection.rarityBreakdown[rarity] ?? 0
                    VStack(spacing: 4) {
                        Text("\(count)")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(rarity == .common ? .gray : 
                                           rarity == .uncommon ? .green :
                                           rarity == .rare ? .blue :
                                           rarity == .epic ? .purple :
                                           rarity == .legendary ? .orange : .pink)
                        
                        Text(rarity.displayName)
                            .font(.caption2)
                            .foregroundColor(theme.onSurface.opacity(0.7))
                    }
                    .padding(8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(theme.surface.opacity(0.5))
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(theme.onSurface.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

// MARK: - Recently Unlocked Badges

struct RecentlyUnlockedBadges: View {
    let badges: [Badge]
    @Environment(\.theme) var theme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("🎉 Recently Unlocked")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(theme.onSurface)
                
                Spacer()
                
                if badges.count > 3 {
                    Button("View All") {
                        // Handle view all
                    }
                    .font(.caption)
                    .foregroundColor(theme.primary)
                }
            }
            
            if badges.isEmpty {
                Text("No badges unlocked yet. Complete quests to earn your first badge! 🚀")
                    .font(.subheadline)
                    .foregroundColor(theme.onSurface.opacity(0.7))
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(theme.surface.opacity(0.5))
                    )
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(badges.prefix(5)) { badge in
                            BadgeCard(badge: badge) {
                                // Handle badge tap
                            }
                        }
                    }
                    .padding(.horizontal)
                }
            }
        }
    }
}
