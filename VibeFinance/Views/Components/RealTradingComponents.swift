//
//  RealTradingComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Real Holding Card
struct RealHoldingCard: View {
    let holding: RealHolding
    @State private var showingDetail = false
    
    var body: some View {
        Button(action: {
            showingDetail = true
        }) {
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(holding.symbol)
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        Text("\(String(format: "%.0f", holding.quantity)) shares")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("$\(String(format: "%.2f", holding.marketValue))")
                            .font(.headline)
                            .fontWeight(.semibold)

                        HStack(spacing: 4) {
                            Image(systemName: holding.unrealizedPL >= 0 ? "arrow.up.right" : "arrow.down.right")
                                .font(.caption)
                            Text("$\(String(format: "%.2f", abs(holding.unrealizedPL)))")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(holding.unrealizedPL >= 0 ? .green : .red)
                    }
                }
                
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Price")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("$\(String(format: "%.2f", holding.currentPrice))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Avg Cost")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("$\(String(format: "%.2f", holding.averagePrice))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Return")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(holding.unrealizedPLPercent >= 0 ? "+" : "")\(String(format: "%.2f", holding.unrealizedPLPercent))%")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(holding.unrealizedPL >= 0 ? .green : .red)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDetail) {
            RealHoldingDetailView(holding: holding)
        }
    }
}

// MARK: - Real Order Card
struct RealOrderCard: View {
    let order: RealOrder
    @EnvironmentObject var realTradingManager: RealTradingManager
    @State private var showingCancelAlert = false
    @Environment(\.theme) var theme
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Text(order.side.displayName)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(order.side == .buy ? Color.green : Color.red)
                            )
                        
                        Text(order.symbol)
                            .font(.headline)
                            .fontWeight(.bold)
                    }
                    
                    Text("\(String(format: "%.0f", order.qty)) shares • \(order.type.displayName)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(order.status.capitalized)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(statusColor(order.status))
                    
                    if let price = order.filledAvgPrice {
                        Text("$\(String(format: "%.2f", price))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    } else if let limitPrice = order.limitPrice {
                        Text("$\(String(format: "%.2f", limitPrice))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                }
            }
            
            if order.isActive {
                HStack {
                    Text("Filled: \(String(format: "%.0f", order.filledQty)) / \(String(format: "%.0f", order.qty))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Button("Cancel") {
                        showingCancelAlert = true
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.red)
                }
            }
        }
        .padding()
        .glassmorphicCard(theme: theme)
        .alert("Cancel Order", isPresented: $showingCancelAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Confirm", role: .destructive) {
                Task {
                    await realTradingManager.cancelOrder(order.id)
                }
            }
        } message: {
            Text("Are you sure you want to cancel this order?")
        }
    }
    
    private func statusColor(_ status: String) -> Color {
        switch status.lowercased() {
        case "filled": return theme.success
        case "cancelled": return theme.error
        case "new", "pending_new": return theme.primary
        case "partially_filled": return theme.warning
        default: return theme.onSurface.opacity(0.6)
        }
    }
}

// MARK: - Quick Trade Button
struct QuickTradeButton: View {
    let title: String
    let symbol: String
    let side: OrderSide
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: side == .buy ? "plus.circle.fill" : "minus.circle.fill")
                    .font(.title2)
                    .foregroundColor(side == .buy ? .green : .red)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Quick Sell Card
struct QuickSellCard: View {
    let holding: RealHolding
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(holding.symbol)
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text("\(String(format: "%.0f", holding.quantity)) shares")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("$\(String(format: "%.2f", holding.currentPrice))")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack(spacing: 4) {
                    Image(systemName: "minus.circle.fill")
                        .font(.caption)
                    Text("Sell")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.red)
            }
            .padding()
            .frame(width: 120)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Performance Summary Card
struct PerformanceSummaryCard: View {
    let performance: RealPortfolioPerformance
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Performance Summary")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    MetricRow(title: "Total Value", value: "$\(String(format: "%.2f", performance.totalValue))")
                    MetricRow(title: "Total Gain/Loss", value: "$\(String(format: "%.2f", performance.totalGainLoss))", color: performance.isPositive ? .green : .red)
                    MetricRow(title: "Cash Balance", value: "$\(String(format: "%.2f", performance.cashBalance))")
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 8) {
                    MetricRow(title: "Day Change", value: "$\(String(format: "%.2f", performance.dayChange))", color: performance.isDayPositive ? .green : .red, alignment: .trailing)
                    MetricRow(title: "Return %", value: "\(performance.totalGainLossPercent >= 0 ? "+" : "")\(String(format: "%.2f", performance.totalGainLossPercent))%", color: performance.isPositive ? .green : .red, alignment: .trailing)
                    MetricRow(title: "Buying Power", value: "$\(String(format: "%.2f", performance.buyingPower))", alignment: .trailing)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Performers Card
struct PerformersCard: View {
    let best: RealHolding
    let worst: RealHolding
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Top Performers")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Best")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(best.symbol)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text("+\(String(format: "%.2f", best.unrealizedPLPercent))%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    Text("Worst")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(worst.symbol)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text("\(String(format: "%.2f", worst.unrealizedPLPercent))%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Asset Allocation Card
struct AssetAllocationCard: View {
    let holdings: [RealHolding]
    
    var totalValue: Double {
        holdings.reduce(0) { $0 + $1.marketValue }
    }
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Asset Allocation")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                ForEach(holdings.prefix(5), id: \.id) { holding in
                    let percentage = totalValue > 0 ? (holding.marketValue / totalValue) * 100 : 0
                    
                    HStack {
                        Text(holding.symbol)
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Text("\(String(format: "%.1f", percentage))%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    ProgressView(value: percentage / 100)
                        .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Metric Row
struct MetricRow: View {
    let title: String
    let value: String
    var color: Color = .primary
    var alignment: HorizontalAlignment = .leading
    
    var body: some View {
        VStack(alignment: alignment, spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

// MARK: - Empty States
struct EmptyPortfolioView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "chart.pie")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Holdings Yet")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Start investing to see your portfolio here")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

struct EmptyOrdersView: View {
    let filter: String
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "list.bullet")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No \(filter) Orders")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Your \(filter.lowercased()) orders will appear here")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

// MARK: - Search Bar
struct SearchBar: View {
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField(placeholder, text: $text)
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6))
        )
    }
}
