//
//  PortfolioComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Quick Action Card
struct QuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        Button(action: {
            // Handle action
        }) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 100)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Market Mover Card
struct MarketMoverCard: View {
    let symbol: String
    let name: String
    let price: Double
    let change: Double
    let changePercent: Double
    
    var body: some View {
        HStack {
            // Stock Symbol
            VStack(alignment: .leading, spacing: 4) {
                Text(symbol)
                    .font(.headline)
                    .fontWeight(.bold)
                Text(name)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Price and Change
            VStack(alignment: .trailing, spacing: 4) {
                Text("$\(price, specifier: "%.2f")")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                HStack(spacing: 4) {
                    Image(systemName: change >= 0 ? "arrow.up.right" : "arrow.down.right")
                        .font(.caption)
                    Text("\(change >= 0 ? "+" : "")\(change, specifier: "%.2f") (\(changePercent >= 0 ? "+" : "")\(changePercent, specifier: "%.2f")%)")
                        .font(.caption)
                }
                .foregroundColor(change >= 0 ? .green : .red)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

// MARK: - AI Investment Recommendation Card
struct AIRecommendationCard: View {
    let title: String
    let description: String
    let confidence: Double
    let expectedReturn: Double
    let riskLevel: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.purple)
                    .font(.title2)
                
                VStack(alignment: .leading) {
                    Text("AI Recommendation")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("\(confidence * 100, specifier: "%.0f")%")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Confidence")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Expected Return")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(expectedReturn, specifier: "%.1f")%")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Risk Level")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(riskLevel)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(riskLevel == "Low" ? .green : riskLevel == "Medium" ? .orange : .red)
                }
            }
            
            Button(action: {
                // Handle investment action
            }) {
                Text("Invest Now")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Portfolio Performance Chart
struct PortfolioPerformanceChart: View {
    let data: [Double] = [100, 105, 103, 108, 112, 109, 115, 118, 125]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Portfolio Performance")
                .font(.headline)
                .fontWeight(.bold)
            
            ZStack {
                // Chart background
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.secondarySystemBackground))
                    .frame(height: 200)
                
                // Simple line chart representation
                HStack(alignment: .bottom, spacing: 4) {
                    ForEach(0..<data.count, id: \.self) { index in
                        RoundedRectangle(cornerRadius: 2)
                            .fill(
                                LinearGradient(
                                    colors: [.purple.opacity(0.8), .purple.opacity(0.3)],
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                            .frame(height: CGFloat(data[index] - 90) * 2)
                            .animation(.easeInOut(duration: 0.5).delay(Double(index) * 0.1), value: data[index])
                    }
                }
                .padding()
            }
            
            HStack {
                Text("Last 30 days")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("+25.8%")
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
            }
        }
    }
}

// MARK: - Investment Goal Card
struct InvestmentGoalCard: View {
    let title: String
    let targetAmount: Double
    let currentAmount: Double
    let deadline: String
    
    var progress: Double {
        currentAmount / targetAmount
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text(deadline)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("$\(currentAmount, specifier: "%.0f")")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    Text("of $\(targetAmount, specifier: "%.0f")")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                    .scaleEffect(x: 1, y: 2, anchor: .center)
                
                Text("\(progress * 100, specifier: "%.1f")% complete")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
    }
}

#Preview {
    VStack(spacing: 20) {
        QuickActionCard(
            icon: "plus.circle.fill",
            title: "Invest Now",
            subtitle: "Start building wealth",
            color: .green
        )
        
        MarketMoverCard(
            symbol: "AAPL",
            name: "Apple Inc.",
            price: 175.43,
            change: 2.34,
            changePercent: 1.35
        )
        
        AIRecommendationCard(
            title: "Tech Growth ETF",
            description: "Based on your risk profile and market analysis, this ETF offers strong growth potential.",
            confidence: 0.87,
            expectedReturn: 12.5,
            riskLevel: "Medium"
        )
    }
    .padding()
}
