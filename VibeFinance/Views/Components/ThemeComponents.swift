//
//  ThemeComponents.swift
//  VibeFinance - Theme Customization Components
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import SwiftUI

// MARK: - Themes Tab Content

struct ThemesTabContent: View {
    let themes: [UnlockableAppTheme]
    @Binding var selectedCategory: ThemeCategory?
    let activeTheme: UnlockableAppTheme?
    let onThemeSelect: (UnlockableAppTheme) -> Void
    let onThemePreview: (UnlockableAppTheme) -> Void
    @Environment(\.theme) var theme
    
    var body: some View {
        VStack(spacing: 20) {
            // Category Filter
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    CategoryFilterChip(
                        title: "All",
                        isSelected: selectedCategory == nil,
                        onTap: { selectedCategory = nil }
                    )
                    
                    ForEach(ThemeCategory.allCases, id: \.self) { category in
                        CategoryFilterChip(
                            title: "\(category.emoji) \(category.displayName)",
                            isSelected: selectedCategory == category,
                            onTap: { 
                                selectedCategory = selectedCategory == category ? nil : category
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
            
            // Themes Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(themes) { themeItem in
                    ThemeCard(
                        theme: themeItem,
                        isActive: activeTheme?.id == themeItem.id,
                        onSelect: { onThemeSelect(themeItem) },
                        onPreview: { onThemePreview(themeItem) }
                    )
                }
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - Theme Card

struct ThemeCard: View {
    let theme: UnlockableAppTheme
    let isActive: Bool
    let onSelect: () -> Void
    let onPreview: () -> Void
    @Environment(\.theme) var currentTheme
    @State private var isAnimating = false
    
    var body: some View {
        VStack(spacing: 12) {
            // Theme Preview
            ZStack {
                // Background preview
                RoundedRectangle(cornerRadius: 12)
                    .fill(theme.colors.backgroundColor)
                    .frame(height: 100)
                    .overlay(
                        // Surface preview
                        VStack(spacing: 4) {
                            RoundedRectangle(cornerRadius: 6)
                                .fill(theme.colors.surfaceColor)
                                .frame(height: 20)
                            
                            HStack(spacing: 4) {
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(theme.colors.primaryColor)
                                    .frame(height: 12)
                                
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(theme.colors.secondaryColor)
                                    .frame(height: 12)
                                
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(theme.colors.accentColor)
                                    .frame(height: 12)
                            }
                        }
                        .padding(8)
                    )
                
                // Effects overlay
                if let effects = theme.effects {
                    if effects.hasParticles {
                        ParticleEffectOverlay(particleType: effects.particleType ?? .sparkles)
                    }
                    
                    if effects.hasGlowEffects {
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(theme.colors.primaryColor, lineWidth: 2)
                            .blur(radius: isAnimating ? 4 : 2)
                            .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: isAnimating)
                    }
                }
                
                // Active indicator
                if isActive {
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(.green)
                                .background(Circle().fill(.white))
                        }
                        Spacer()
                    }
                    .padding(8)
                }
                
                // Lock overlay for locked themes
                if !theme.isUnlocked {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.black.opacity(0.6))
                        .overlay(
                            VStack(spacing: 4) {
                                Image(systemName: "lock.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                                
                                Text("Locked")
                                    .font(.caption)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            }
                        )
                }
            }
            .onTapGesture {
                if theme.isUnlocked {
                    onSelect()
                } else {
                    onPreview()
                }
            }
            
            // Theme Info
            VStack(spacing: 4) {
                Text(theme.name)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(theme.isUnlocked ? currentTheme.onSurface : currentTheme.onSurface.opacity(0.6))
                    .lineLimit(1)
                
                Text(theme.description)
                    .font(.caption)
                    .foregroundColor(currentTheme.onSurface.opacity(0.7))
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
                
                // Rarity badge
                Text(theme.rarity.displayName.uppercased())
                    .font(.caption2)
                    .fontWeight(.bold)
                    .foregroundColor(theme.rarityColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(theme.rarityColor.opacity(0.2))
                    )
            }
            
            // Action Buttons
            HStack(spacing: 8) {
                if theme.isUnlocked {
                    if isActive {
                        Text("ACTIVE")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.green.opacity(0.2))
                            )
                    } else {
                        Button("ACTIVATE") {
                            onSelect()
                        }
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(currentTheme.primary)
                        )
                    }
                } else {
                    Button("PREVIEW") {
                        onPreview()
                    }
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(currentTheme.primary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(currentTheme.primary.opacity(0.2))
                    )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.isUnlocked ? AnyShapeStyle(currentTheme.surface) : AnyShapeStyle(currentTheme.surface.opacity(0.5)))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isActive ? currentTheme.primary : currentTheme.onSurface.opacity(0.1), lineWidth: isActive ? 2 : 1)
                )
                .shadow(
                    color: isActive ? currentTheme.primary.opacity(0.3) : .clear,
                    radius: isActive ? 8 : 0,
                    x: 0,
                    y: 4
                )
        )
        .onAppear {
            if theme.effects?.hasGlowEffects == true {
                isAnimating = true
            }
        }
    }
}

// MARK: - Category Filter Chip

struct CategoryFilterChip: View {
    let title: String
    let isSelected: Bool
    let onTap: () -> Void
    @Environment(\.theme) var theme
    
    var body: some View {
        Button(action: onTap) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : theme.onSurface)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? AnyShapeStyle(theme.primary) : AnyShapeStyle(theme.surface))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(isSelected ? theme.primary : theme.onSurface.opacity(0.2), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Particle Effect Overlay

struct ParticleEffectOverlay: View {
    let particleType: ThemeEffects.ParticleType
    @State private var particles: [ParticleData] = []
    
    struct ParticleData: Identifiable {
        let id = UUID()
        var x: CGFloat
        var y: CGFloat
        var opacity: Double
        var scale: CGFloat
    }
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                Image(systemName: particleIcon)
                    .font(.caption2)
                    .foregroundColor(particleColor)
                    .opacity(particle.opacity)
                    .scaleEffect(particle.scale)
                    .position(x: particle.x, y: particle.y)
            }
        }
        .onAppear {
            generateParticles()
        }
    }
    
    private var particleIcon: String {
        switch particleType {
        case .stars: return "star.fill"
        case .sparkles: return "sparkle"
        case .bubbles: return "circle.fill"
        case .snow: return "snow"
        case .leaves: return "leaf.fill"
        case .fire: return "flame.fill"
        }
    }
    
    private var particleColor: Color {
        switch particleType {
        case .stars: return .yellow
        case .sparkles: return .white
        case .bubbles: return .blue
        case .snow: return .white
        case .leaves: return .green
        case .fire: return .orange
        }
    }
    
    private func generateParticles() {
        particles = (0..<8).map { _ in
            ParticleData(
                x: CGFloat.random(in: 10...90),
                y: CGFloat.random(in: 10...90),
                opacity: Double.random(in: 0.3...0.8),
                scale: CGFloat.random(in: 0.5...1.0)
            )
        }
    }
}

#Preview {
    ThemesTabContent(
        themes: [],
        selectedCategory: .constant(.neon),
        activeTheme: nil,
        onThemeSelect: { _ in },
        onThemePreview: { _ in }
    )
    .preferredColorScheme(.dark)
}
