//
//  RewardAnimationComponents.swift
//  VibeFinance - Reward Animation Components
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Claim Animation View
struct ClaimAnimationView: View {
    let reward: DailyReward
    let onComplete: () -> Void
    
    @State private var animationPhase = 0
    @State private var particles: [ParticleData] = []
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
                .ignoresSafeArea()
                .onTapGesture {
                    onComplete()
                }
            
            VStack(spacing: 24) {
                // Animated reward icon
                ZStack {
                    // Glow effect
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [
                                    reward.rarity.glowColor.opacity(0.8),
                                    reward.rarity.glowColor.opacity(0.4),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 20,
                                endRadius: 80
                            )
                        )
                        .frame(width: 160, height: 160)
                        .scaleEffect(animationPhase >= 1 ? 1.2 : 0.8)
                        .opacity(animationPhase >= 1 ? 0.8 : 0.0)
                    
                    // Main icon background
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: reward.rarity.colors,
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 100, height: 100)
                        .scaleEffect(animationPhase >= 1 ? 1.0 : 0.5)
                        .rotationEffect(.degrees(animationPhase >= 1 ? 360 : 0))
                    
                    // Icon
                    Image(systemName: reward.icon)
                        .font(.system(size: 40, weight: .bold))
                        .foregroundColor(.white)
                        .scaleEffect(animationPhase >= 1 ? 1.0 : 0.5)
                }
                
                // Text content
                VStack(spacing: 12) {
                    Text("Reward Claimed! 🎉")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .scaleEffect(animationPhase >= 2 ? 1.0 : 0.8)
                        .opacity(animationPhase >= 2 ? 1.0 : 0.0)
                    
                    Text(reward.title)
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white.opacity(0.9))
                        .scaleEffect(animationPhase >= 3 ? 1.0 : 0.8)
                        .opacity(animationPhase >= 3 ? 1.0 : 0.0)
                    
                    Text(reward.description)
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.7))
                        .multilineTextAlignment(.center)
                        .scaleEffect(animationPhase >= 4 ? 1.0 : 0.8)
                        .opacity(animationPhase >= 4 ? 1.0 : 0.0)
                }
                
                // Sparkle particles
                ZStack {
                    ForEach(particles.indices, id: \.self) { index in
                        Circle()
                            .fill(Color.yellow)
                            .frame(width: particles[index].size, height: particles[index].size)
                            .position(particles[index].position)
                            .opacity(particles[index].opacity)
                            .scaleEffect(particles[index].scale)
                    }
                }
                .frame(width: 200, height: 200)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 24)
                    .fill(Color.black.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 24)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .scaleEffect(animationPhase >= 1 ? 1.0 : 0.8)
        }
        .onAppear {
            startAnimation()
        }
    }
    
    private func startAnimation() {
        // Phase 1: Icon appears and rotates
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            animationPhase = 1
        }
        
        // Phase 2: Title appears
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationPhase = 2
            }
        }
        
        // Phase 3: Reward title appears
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationPhase = 3
            }
        }
        
        // Phase 4: Description appears
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.9) {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                animationPhase = 4
            }
            createParticles()
        }
        
        // Auto-dismiss after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            onComplete()
        }
    }
    
    private func createParticles() {
        particles = []
        for _ in 0..<20 {
            let particle = ParticleData(
                position: CGPoint(x: 100, y: 100),
                size: Double.random(in: 4...8),
                opacity: 1.0,
                scale: 1.0
            )
            particles.append(particle)
        }
        
        // Animate particles
        withAnimation(.easeOut(duration: 2.0)) {
            for i in 0..<particles.count {
                let angle = Double(i) * (360.0 / Double(particles.count)) * .pi / 180
                let radius = Double.random(in: 50...100)
                
                particles[i].position = CGPoint(
                    x: 100 + cos(angle) * radius,
                    y: 100 + sin(angle) * radius
                )
                particles[i].opacity = 0.0
                particles[i].scale = 0.0
            }
        }
    }
}

// MARK: - Particle Data
struct ParticleData {
    var position: CGPoint
    var size: Double
    var opacity: Double
    var scale: Double
}

// MARK: - Particle System View
struct ParticleSystemView: View {
    @State private var particles: [MovingParticle] = []
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ForEach(particles.indices, id: \.self) { index in
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.purple.opacity(0.8), .pink.opacity(0.6)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: particles[index].size, height: particles[index].size)
                        .position(particles[index].position)
                        .opacity(particles[index].opacity)
                        .blur(radius: particles[index].blur)
                }
            }
            .onAppear {
                createParticles(in: geometry.size)
                startAnimation()
            }
        }
    }
    
    private func createParticles(in size: CGSize) {
        particles = []
        for _ in 0..<30 {
            let particle = MovingParticle(
                position: CGPoint(
                    x: Double.random(in: 0...size.width),
                    y: Double.random(in: 0...size.height)
                ),
                size: Double.random(in: 2...6),
                opacity: Double.random(in: 0.3...0.8),
                blur: Double.random(in: 0...2),
                velocity: CGPoint(
                    x: Double.random(in: -0.5...0.5),
                    y: Double.random(in: -0.5...0.5)
                )
            )
            particles.append(particle)
        }
    }
    
    private func startAnimation() {
        Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { _ in
            updateParticles()
        }
    }
    
    private func updateParticles() {
        for i in 0..<particles.count {
            particles[i].position.x += particles[i].velocity.x
            particles[i].position.y += particles[i].velocity.y
            
            // Wrap around screen
            if particles[i].position.x < 0 {
                particles[i].position.x = UIScreen.main.bounds.width
            } else if particles[i].position.x > UIScreen.main.bounds.width {
                particles[i].position.x = 0
            }
            
            if particles[i].position.y < 0 {
                particles[i].position.y = UIScreen.main.bounds.height
            } else if particles[i].position.y > UIScreen.main.bounds.height {
                particles[i].position.y = 0
            }
        }
    }
}

// MARK: - Moving Particle Data
struct MovingParticle {
    var position: CGPoint
    var size: Double
    var opacity: Double
    var blur: Double
    var velocity: CGPoint
}

// MARK: - Reward Detail Sheet
struct RewardDetailSheet: View {
    let reward: DailyReward
    let onClaim: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            // Header
            VStack(spacing: 16) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: reward.rarity.colors,
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .shadow(color: reward.rarity.glowColor, radius: 12)
                    
                    Image(systemName: reward.icon)
                        .font(.largeTitle)
                        .foregroundColor(.white)
                }
                
                VStack(spacing: 8) {
                    Text(reward.title)
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(reward.description)
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
                
                // Rarity Badge
                Text(reward.rarity.rawValue.capitalized)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(
                                LinearGradient(
                                    colors: reward.rarity.colors,
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                    )
                    .foregroundColor(.white)
            }
            
            Spacer()
            
            // Action Buttons
            HStack(spacing: 16) {
                Button("Close") {
                    onDismiss()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.1))
                )
                
                if !reward.claimed && !reward.locked {
                    Button("Claim") {
                        onClaim()
                        onDismiss()
                    }
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(Color.black.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 24)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .background(.ultraThinMaterial)
        .presentationDetents([.height(400)])
        .presentationDragIndicator(.visible)
    }
}
