//
//  LeaderboardComponents.swift
//  VibeFinance - Leaderboard Supporting Components
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Leaderboard Row Component
struct LeaderboardRow: View {
    let user: LeaderboardUser
    let timeframe: LeaderboardTimeframe
    let animationDelay: Double
    
    @State private var isVisible = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Rank and Change Indicator
            HStack(spacing: 8) {
                // Rank
                ZStack {
                    if user.rank <= 3 {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: user.rank == 1 ? [.yellow, .orange] :
                                           user.rank == 2 ? [.gray.opacity(0.8), .gray] :
                                           [.orange.opacity(0.8), .red],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 32, height: 32)
                        
                        Image(systemName: user.rank == 1 ? "crown.fill" : 
                                         user.rank == 2 ? "medal.fill" : "trophy.fill")
                            .font(.caption)
                            .foregroundColor(.white)
                    } else {
                        Text("#\(user.rank)")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white.opacity(0.8))
                            .frame(width: 32)
                    }
                }
                
                // Rank Change Indicator
                if user.rank != user.previousRank {
                    Image(systemName: user.rank < user.previousRank ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(user.rank < user.previousRank ? .green : .red)
                }
            }
            .frame(width: 60, alignment: .leading)
            
            // Avatar
            AsyncImage(url: URL(string: user.avatar)) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Text(user.name.prefix(1))
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
            }
            .frame(width: 48, height: 48)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
            
            // User Info
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 8) {
                    Text(user.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text("Lv.\(user.level)")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.white.opacity(0.2))
                        )
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Text("@\(user.username)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
            }
            
            Spacer()
            
            // Points
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(getPoints().formatted())")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("points")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))
            }
            
            // Streak
            HStack(spacing: 4) {
                Image(systemName: "flame.fill")
                    .font(.caption)
                    .foregroundColor(.orange)
                Text("\(user.streak)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.orange.opacity(0.2))
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .scaleEffect(isVisible ? 1.0 : 0.95)
        .opacity(isVisible ? 1.0 : 0.0)
        .onAppear {
            withAnimation(
                .spring(response: 0.5, dampingFraction: 0.8)
                .delay(animationDelay)
            ) {
                isVisible = true
            }
        }
    }
    
    private func getPoints() -> Int {
        switch timeframe {
        case .weekly:
            return user.weeklyPoints
        case .monthly:
            return user.monthlyPoints
        case .allTime:
            return user.points
        }
    }
}

// MARK: - Competition Card Component
struct CompetitionCard: View {
    let competition: Competition
    @State private var timeRemaining = ""
    @State private var timer: Timer?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Text(competition.title)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        if competition.isActive {
                            HStack(spacing: 4) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 8, height: 8)
                                    .scaleEffect(1.0)
                                    .animation(
                                        .easeInOut(duration: 1.0).repeatForever(autoreverses: true),
                                        value: competition.isActive
                                    )
                                
                                Text("Live")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.green)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.green.opacity(0.2))
                            )
                        }
                    }
                    
                    Text(competition.description)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(competition.prize)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("Prize")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                }
            }
            
            // Stats
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(competition.participants)")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    Text("Participants")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(timeRemaining)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    Text("Time Left")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }
                
                Spacer()
                
                Button(action: {
                    // Join competition action
                }) {
                    Text("Join")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white)
                        )
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [Color.green.opacity(0.2), Color.blue.opacity(0.2)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
        .onAppear {
            startTimer()
        }
        .onDisappear {
            timer?.invalidate()
        }
    }
    
    private func startTimer() {
        updateTimeRemaining()
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            updateTimeRemaining()
        }
    }
    
    private func updateTimeRemaining() {
        let now = Date()
        let timeInterval = competition.endDate.timeIntervalSince(now)
        
        if timeInterval > 0 {
            let days = Int(timeInterval) / 86400
            let hours = Int(timeInterval) % 86400 / 3600
            let minutes = Int(timeInterval) % 3600 / 60
            
            if days > 0 {
                timeRemaining = "\(days)d \(hours)h"
            } else if hours > 0 {
                timeRemaining = "\(hours)h \(minutes)m"
            } else {
                timeRemaining = "\(minutes)m"
            }
        } else {
            timeRemaining = "Ended"
        }
    }
}

// MARK: - Achievement Card Component
struct AchievementCard: View {
    let achievement: LeaderboardAchievement
    
    var body: some View {
        VStack(spacing: 12) {
            // Icon and Rarity
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: achievement.rarity.gradient,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                
                Image(systemName: achievement.icon)
                    .font(.title2)
                    .foregroundColor(.white)
            }
            
            // Achievement Info
            VStack(spacing: 4) {
                Text(achievement.title)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                
                Text(achievement.description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            // Progress or Unlock Status
            if let progress = achievement.progress, let maxProgress = achievement.maxProgress {
                VStack(spacing: 4) {
                    ProgressView(value: Double(progress), total: Double(maxProgress))
                        .progressViewStyle(LinearProgressViewStyle(tint: achievement.rarity.color))
                        .scaleEffect(y: 0.8)
                    
                    Text("\(progress)/\(maxProgress)")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                }
            } else if achievement.unlockedAt != nil {
                Text("Unlocked! 🎉")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.green.opacity(0.2))
                    )
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(achievement.rarity.color.opacity(0.4), lineWidth: 1)
                )
        )
    }
}
