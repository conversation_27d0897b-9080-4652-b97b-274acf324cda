//
//  BillingHistoryView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct BillingHistoryView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var selectedYear = Calendar.current.component(.year, from: Date())
    @State private var showingInvoiceShare = false
    @State private var selectedInvoice: BillingRecord?
    
    private var availableYears: [Int] {
        let currentYear = Calendar.current.component(.year, from: Date())
        return Array((currentYear-2)...currentYear).reversed()
    }
    
    private var filteredBillingHistory: [BillingRecord] {
        return paymentManager.billingHistory.filter { record in
            Calendar.current.component(.year, from: record.date) == selectedYear
        }
    }
    
    private var totalSpent: Decimal {
        return filteredBillingHistory
            .filter { $0.status == .completed }
            .reduce(0) { $0 + $1.amount }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Summary Header
                summaryHeader
                
                // Year Selector
                yearSelector
                
                // Billing History List
                if filteredBillingHistory.isEmpty {
                    emptyState
                } else {
                    billingHistoryList
                }
            }
            .navigationTitle("Billing History")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Export All") {
                            exportAllInvoices()
                        }
                        
                        Button("Download Tax Summary") {
                            downloadTaxSummary()
                        }
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
            .sheet(isPresented: $showingInvoiceShare) {
                if let invoice = selectedInvoice {
                    InvoiceShareSheet(invoice: invoice)
                }
            }
            .onAppear {
                Task {
                    await paymentManager.loadBillingHistory()
                }
            }
        }
    }
    
    // MARK: - Summary Header
    private var summaryHeader: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total Spent (\(selectedYear))")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text(formatCurrency(totalSpent))
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Transactions")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\(filteredBillingHistory.count)")
                        .font(.title2)
                        .fontWeight(.semibold)
                }
            }
            
            // Monthly Breakdown
            if !filteredBillingHistory.isEmpty {
                monthlyBreakdown
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
        .padding()
    }
    
    private var monthlyBreakdown: some View {
        VStack(spacing: 8) {
            Text("Monthly Breakdown")
                .font(.subheadline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                ForEach(1...12, id: \.self) { month in
                    let monthlyTotal = getMonthlyTotal(month: month)
                    
                    VStack(spacing: 4) {
                        Text(DateFormatter().shortMonthSymbols[month - 1])
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(formatCurrency(monthlyTotal))
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(monthlyTotal > 0 ? .primary : .secondary)
                    }
                    .padding(.vertical, 8)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(monthlyTotal > 0 ? Color.purple.opacity(0.1) : Color(.systemGray5))
                    )
                }
            }
        }
    }
    
    // MARK: - Year Selector
    private var yearSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(availableYears, id: \.self) { year in
                    Button(action: {
                        selectedYear = year
                    }) {
                        Text("\(year)")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(selectedYear == year ? .white : .primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedYear == year ? Color.purple : Color(.systemGray6))
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal)
        }
        .padding(.bottom)
    }
    
    // MARK: - Billing History List
    private var billingHistoryList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredBillingHistory) { record in
                    BillingRecordRow(
                        record: record,
                        onDownload: {
                            downloadInvoice(record)
                        },
                        onShare: {
                            selectedInvoice = record
                            showingInvoiceShare = true
                        }
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - Empty State
    private var emptyState: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "doc.text")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Billing History")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Your billing history for \(selectedYear) will appear here")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Spacer()
        }
        .padding()
    }
    
    // MARK: - Helper Methods
    private func getMonthlyTotal(month: Int) -> Decimal {
        return filteredBillingHistory
            .filter { Calendar.current.component(.month, from: $0.date) == month }
            .filter { $0.status == .completed }
            .reduce(0) { $0 + $1.amount }
    }
    
    private func formatCurrency(_ amount: Decimal) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "USD"
        return formatter.string(from: amount as NSDecimalNumber) ?? "$0.00"
    }
    
    private func downloadInvoice(_ record: BillingRecord) {
        Task {
            if (await paymentManager.downloadInvoice(record)) != nil {
                // Handle downloaded invoice
            }
        }
    }
    
    private func exportAllInvoices() {
        // Export all invoices for the selected year
    }
    
    private func downloadTaxSummary() {
        // Generate and download tax summary
    }
}

// MARK: - Billing Record Row
struct BillingRecordRow: View {
    let record: BillingRecord
    let onDownload: () -> Void
    let onShare: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(record.description)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text(record.formattedDate)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(record.formattedAmount)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    HStack(spacing: 4) {
                        Image(systemName: record.status.icon)
                            .font(.caption)
                        Text(record.status.displayName)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(Color(record.status.color))
                }
            }
            
            if record.status == .completed {
                HStack(spacing: 12) {
                    Button("Download") {
                        onDownload()
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.purple, lineWidth: 1)
                    )
                    
                    Button("Share") {
                        onShare()
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple)
                    )
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Invoice Share Sheet
struct InvoiceShareSheet: View {
    let invoice: BillingRecord
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Invoice Preview
                invoicePreview
                
                // Share Options
                shareOptions
                
                Spacer()
            }
            .padding()
            .navigationTitle("Share Invoice")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var invoicePreview: some View {
        VStack(spacing: 16) {
            Text("Invoice Preview")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                HStack {
                    Text("Description")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(invoice.description)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Amount")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(invoice.formattedAmount)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                }
                
                HStack {
                    Text("Date")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(invoice.formattedDate)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("Status")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(invoice.status.displayName)
                        .fontWeight(.medium)
                        .foregroundColor(Color(invoice.status.color))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var shareOptions: some View {
        VStack(spacing: 16) {
            Text("Share Options")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                ShareOptionRow(
                    icon: "envelope.fill",
                    title: "Email",
                    description: "Send invoice via email"
                ) {
                    // Share via email
                }
                
                ShareOptionRow(
                    icon: "square.and.arrow.up.fill",
                    title: "Export PDF",
                    description: "Save as PDF document"
                ) {
                    // Export as PDF
                }
                
                ShareOptionRow(
                    icon: "doc.on.clipboard.fill",
                    title: "Copy Details",
                    description: "Copy invoice details to clipboard"
                ) {
                    // Copy to clipboard
                }
            }
        }
    }
}

// MARK: - Share Option Row
struct ShareOptionRow: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.purple)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.purple.opacity(0.1))
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    BillingHistoryView()
        .environmentObject(PaymentManager(subscriptionManager: SubscriptionManager()))
}
