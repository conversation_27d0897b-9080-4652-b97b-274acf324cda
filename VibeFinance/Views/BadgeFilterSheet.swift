//
//  BadgeFilterSheet.swift
//  VibeFinance - Badge Collection Filter Sheet
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import SwiftUI

struct BadgeFilterSheet: View {
    @Binding var selectedCategory: BadgeCategory?
    @Binding var selectedRarity: BadgeRarity?
    @Environment(\.dismiss) private var dismiss
    @Environment(\.theme) var theme
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Category Filter
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Category")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(theme.onSurface)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                            ForEach(BadgeCategory.allCases, id: \.self) { category in
                                CategoryFilterButton(
                                    category: category,
                                    isSelected: selectedCategory == category,
                                    onTap: {
                                        if selectedCategory == category {
                                            selectedCategory = nil
                                        } else {
                                            selectedCategory = category
                                        }
                                    }
                                )
                            }
                        }
                    }
                    
                    Divider()
                        .background(theme.onSurface.opacity(0.1))
                    
                    // Rarity Filter
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Rarity")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(theme.onSurface)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                            ForEach(BadgeRarity.allCases, id: \.self) { rarity in
                                RarityFilterButton(
                                    rarity: rarity,
                                    isSelected: selectedRarity == rarity,
                                    onTap: {
                                        if selectedRarity == rarity {
                                            selectedRarity = nil
                                        } else {
                                            selectedRarity = rarity
                                        }
                                    }
                                )
                            }
                        }
                    }
                    
                    Spacer(minLength: 40)
                    
                    // Action Buttons
                    VStack(spacing: 12) {
                        Button("Apply Filters") {
                            dismiss()
                        }
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(theme.primary)
                        )
                        
                        Button("Clear All Filters") {
                            selectedCategory = nil
                            selectedRarity = nil
                        }
                        .font(.subheadline)
                        .foregroundColor(theme.primary)
                    }
                }
                .padding(20)
            }
            .background(theme.background)
            .navigationTitle("Filter Badges")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(theme.primary)
                }
            }
        }
    }
}

// MARK: - Filter Button Components

struct CategoryFilterButton: View {
    let category: BadgeCategory
    let isSelected: Bool
    let onTap: () -> Void
    @Environment(\.theme) var theme
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(category.emoji)
                    .font(.title)
                
                Text(category.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : theme.onSurface)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? theme.primary : theme.surface)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? theme.primary : theme.onSurface.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct RarityFilterButton: View {
    let rarity: BadgeRarity
    let isSelected: Bool
    let onTap: () -> Void
    @Environment(\.theme) var theme
    
    private var rarityColor: Color {
        switch rarity {
        case .common: return .gray
        case .uncommon: return .green
        case .rare: return .blue
        case .epic: return .purple
        case .legendary: return .orange
        case .mythic: return .pink
        }
    }
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                // Rarity indicator with sparkles
                HStack(spacing: 2) {
                    ForEach(0..<max(1, rarity.sparkleCount), id: \.self) { _ in
                        Image(systemName: rarity.sparkleCount > 0 ? "sparkle" : "circle.fill")
                            .font(.caption)
                            .foregroundColor(isSelected ? .white : rarityColor)
                    }
                }
                
                Text(rarity.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : theme.onSurface)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? rarityColor : theme.surface)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? rarityColor : theme.onSurface.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    BadgeFilterSheet(
        selectedCategory: .constant(.learning),
        selectedRarity: .constant(.rare)
    )
    .preferredColorScheme(.dark)
}
