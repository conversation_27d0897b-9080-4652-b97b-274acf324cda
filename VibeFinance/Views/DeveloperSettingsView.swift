//
//  DeveloperSettingsView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct DeveloperSettingsView: View {
    @EnvironmentObject var authManager: AuthManager
    @Environment(\.dismiss) var dismiss

    
    var body: some View {
        NavigationView {
            List {
                Section("Authentication") {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Real Authentication")
                                .font(.headline)
                            Text("Production-ready Supabase authentication")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    }

                }
                
                Section("Current State") {
                    HStack {
                        Text("Development Mode")
                        Spacer()
                        #if DEBUG
                        Text("Enabled")
                            .foregroundColor(.green)
                        #else
                        Text("Disabled")
                            .foregroundColor(.red)
                        #endif
                    }
                    
                    HStack {
                        Text("Authentication Status")
                        Spacer()
                        Text(authManager.isAuthenticated ? "Authenticated" : "Not Authenticated")
                            .foregroundColor(authManager.isAuthenticated ? .green : .red)
                    }
                    
                    if let user = authManager.currentUser {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Current User")
                                .font(.headline)
                            Text("Email: \(user.email)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("Username: \(user.username)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("ID: \(user.id.uuidString)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section("Actions") {
                    if authManager.isAuthenticated {
                        Button(action: {
                            Task {
                                await authManager.signOut()
                            }
                        }) {
                            HStack {
                                Image(systemName: "rectangle.portrait.and.arrow.right")
                                    .foregroundColor(.red)
                                Text("Sign Out")
                            }
                        }
                        .foregroundColor(.red)
                    }
                    
                    Button(action: {
                        // Clear all UserDefaults for the app
                        if let bundleID = Bundle.main.bundleIdentifier {
                            UserDefaults.standard.removePersistentDomain(forName: bundleID)
                        }

                        // Reset auth manager state
                        authManager.isAuthenticated = false
                        authManager.currentUser = nil
                        authManager.errorMessage = nil
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                            Text("Reset All Settings")
                        }
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("Developer Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }

    }
}



#if DEBUG
struct DeveloperSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        DeveloperSettingsView()
            .environmentObject(AuthManager())
    }
}
#endif
