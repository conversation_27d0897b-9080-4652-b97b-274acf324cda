//
//  EnhancedSquadDetailView.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI

struct EnhancedSquadDetailView: View {
    let squad: Squad
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var squadManager: SquadManager
    @EnvironmentObject var userManager: UserManager
    @StateObject private var squadDetailManager = SquadDetailManager()
    @State private var selectedTab: SquadDetailTab = .overview
    @State private var showingInvestmentProposal = false
    @State private var showingMemberInvite = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Squad header
                squadHeader
                
                // Tab selector
                squadTabSelector
                
                // Content based on selected tab
                ScrollView {
                    VStack(spacing: 20) {
                        switch selectedTab {
                        case .overview:
                            overviewContent
                        case .investments:
                            investmentsContent
                        case .members:
                            membersContent
                        case .chat:
                            chatContent
                        case .analytics:
                            analyticsContent
                        }
                    }
                    .padding(16)
                }
            }
            .navigationTitle(squad.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Invite Members", systemImage: "person.badge.plus") {
                            showingMemberInvite = true
                        }
                        
                        Button("Squad Settings", systemImage: "gear") {
                            // Show settings
                        }
                        
                        Button("Leave Squad", systemImage: "rectangle.portrait.and.arrow.right") {
                            // Handle leave squad
                        }
                        .foregroundColor(.red)
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .onAppear {
                Task {
                    await squadDetailManager.loadSquadDetails(squad.id)
                }
            }
            .sheet(isPresented: $showingInvestmentProposal) {
                Text("Create Investment Proposal Coming Soon!")
                    .padding()
            }
            .sheet(isPresented: $showingMemberInvite) {
                Text("Invite Members Feature Coming Soon!")
                    .padding()
            }
        }
    }
    
    // MARK: - Squad Header
    private var squadHeader: some View {
        VStack(spacing: 16) {
            // Squad basic info
            HStack {
                Text(squad.emoji)
                    .font(.largeTitle)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(squad.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(squad.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Performance indicator
                VStack(alignment: .trailing, spacing: 4) {
                    Text("+\(String(format: "%.1f", squad.performancePercentage))%")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(squad.performancePercentage >= 0 ? .green : .red)
                    
                    Text("This Week")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Squad stats
            HStack(spacing: 20) {
                SquadStatCard(
                    title: "Members",
                    value: "\(squad.members.count)",
                    icon: "person.3.fill",
                    color: .purple
                )
                
                SquadStatCard(
                    title: "Total Value",
                    value: "$\(String(format: "%.0f", squad.totalValue))",
                    icon: "dollarsign.circle.fill",
                    color: .green
                )
                
                SquadStatCard(
                    title: "Investments",
                    value: "\(squad.investments.count)",
                    icon: "chart.line.uptrend.xyaxis",
                    color: .blue
                )
                
                SquadStatCard(
                    title: "Rank",
                    value: "#\(squadDetailManager.squadRank)",
                    icon: "trophy.fill",
                    color: .orange
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [.purple.opacity(0.5), .pink.opacity(0.5)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - Tab Selector
    private var squadTabSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 0) {
                ForEach(SquadDetailTab.allCases, id: \.self) { tab in
                    Button(action: {
                        selectedTab = tab
                    }) {
                        VStack(spacing: 4) {
                            Image(systemName: tab.icon)
                                .font(.title3)
                            Text(tab.title)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(selectedTab == tab ? .purple : .secondary)
                        .frame(minWidth: 80)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedTab == tab ? .purple.opacity(0.2) : .clear)
                        )
                    }
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - Overview Content
    private var overviewContent: some View {
        VStack(spacing: 20) {
            // Recent activity
            recentActivitySection
            
            // Top investments
            topInvestmentsSection
            
            // Quick actions
            quickActionsSection
        }
    }
    
    // MARK: - Recent Activity Section
    private var recentActivitySection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Recent Activity 📈")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("Last 24h")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            VStack {
                Text("Recent squad activities will appear here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
    }
    
    // MARK: - Top Investments Section
    private var topInvestmentsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Top Investments 💎")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    selectedTab = .investments
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            VStack {
                Text("Squad investments will appear here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
            }
        }
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        VStack(spacing: 16) {
            Text("Quick Actions ⚡")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                SquadQuickActionCard(
                    title: "Propose Investment",
                    icon: "plus.circle.fill",
                    color: .green
                ) {
                    showingInvestmentProposal = true
                }

                SquadQuickActionCard(
                    title: "Start Discussion",
                    icon: "message.circle.fill",
                    color: .blue
                ) {
                    selectedTab = .chat
                }

                SquadQuickActionCard(
                    title: "Invite Friends",
                    icon: "person.badge.plus.fill",
                    color: .purple
                ) {
                    showingMemberInvite = true
                }

                SquadQuickActionCard(
                    title: "View Analytics",
                    icon: "chart.bar.fill",
                    color: .orange
                ) {
                    selectedTab = .analytics
                }
            }
        }
    }
    
    // MARK: - Investments Content
    private var investmentsContent: some View {
        VStack(spacing: 20) {
            Text("Investment Features Coming Soon!")
                .font(.headline)
                .padding()
        }
    }
    
    // MARK: - Members Content
    private var membersContent: some View {
        VStack(spacing: 20) {
            Text("Members Management Coming Soon!")
                .font(.headline)
                .padding()
        }
    }
    
    // MARK: - Chat Content
    private var chatContent: some View {
        VStack(spacing: 20) {
            Text("Squad Chat Coming Soon!")
                .font(.headline)
                .padding()
        }
    }
    
    // MARK: - Analytics Content
    private var analyticsContent: some View {
        VStack(spacing: 20) {
            Text("Squad Analytics Coming Soon!")
                .font(.headline)
                .padding()
        }
    }
}

enum SquadDetailTab: String, CaseIterable {
    case overview = "overview"
    case investments = "investments"
    case members = "members"
    case chat = "chat"
    case analytics = "analytics"
    
    var title: String {
        switch self {
        case .overview: return "Overview"
        case .investments: return "Investments"
        case .members: return "Members"
        case .chat: return "Chat"
        case .analytics: return "Analytics"
        }
    }
    
    var icon: String {
        switch self {
        case .overview: return "house.fill"
        case .investments: return "chart.line.uptrend.xyaxis"
        case .members: return "person.3.fill"
        case .chat: return "message.fill"
        case .analytics: return "chart.bar.fill"
        }
    }
}

// MARK: - Supporting Views
struct SquadStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.1))
        )
    }
}

struct SquadQuickActionCard: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)

                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(color.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
}

#Preview {
    EnhancedSquadDetailView(
        squad: Squad(
            name: "Tech Titans",
            description: "Investing in the future of technology",
            emoji: "🚀",
            creatorID: UUID()
        )
    )
    .environmentObject(SquadManager())
    .environmentObject(UserManager())
}
