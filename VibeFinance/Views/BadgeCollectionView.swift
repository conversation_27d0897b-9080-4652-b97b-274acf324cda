//
//  BadgeCollectionView.swift
//  VibeFinance - Badge Collection Main View
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import SwiftUI

struct BadgeCollectionView: View {
    @StateObject private var badgeManager = BadgeManager()
    @State private var selectedCategory: BadgeCategory? = nil
    @State private var selectedRarity: BadgeRarity? = nil
    @State private var showingFilters = false
    @State private var searchText = ""
    @Environment(\.theme) var theme
    
    var filteredBadges: [Badge] {
        var badges = badgeManager.userBadges
        
        // Apply category filter
        if let category = selectedCategory {
            badges = badges.filter { $0.category == category }
        }
        
        // Apply rarity filter
        if let rarity = selectedRarity {
            badges = badges.filter { $0.rarity == rarity }
        }
        
        // Apply search filter
        if !searchText.isEmpty {
            badges = badges.filter { 
                $0.name.localizedCaseInsensitiveContains(searchText) ||
                $0.description.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        return badges.sorted { first, second in
            // Sort by unlocked status first, then by rarity
            if first.isUnlocked != second.isUnlocked {
                return first.isUnlocked && !second.isUnlocked
            }
            return first.rarity.rawValue > second.rarity.rawValue
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Collection Header
                    if let collection = badgeManager.badgeCollection {
                        BadgeCollectionHeader(collection: collection)
                            .padding(.horizontal)
                    }
                    
                    // Recently Unlocked
                    if !badgeManager.recentlyUnlocked.isEmpty {
                        RecentlyUnlockedBadges(badges: badgeManager.recentlyUnlocked)
                            .padding(.horizontal)
                    }
                    
                    // Featured Showcase
                    if let showcase = badgeManager.featuredShowcase {
                        FeaturedBadgeShowcase(showcase: showcase)
                            .padding(.horizontal)
                    }
                    
                    // Search and Filters
                    VStack(spacing: 12) {
                        HStack {
                            // Search Bar
                            HStack {
                                Image(systemName: "magnifyingglass")
                                    .foregroundColor(theme.onSurface.opacity(0.6))
                                
                                TextField("Search badges...", text: $searchText)
                                    .textFieldStyle(PlainTextFieldStyle())
                                
                                if !searchText.isEmpty {
                                    Button(action: { searchText = "" }) {
                                        Image(systemName: "xmark.circle.fill")
                                            .foregroundColor(theme.onSurface.opacity(0.6))
                                    }
                                }
                            }
                            .padding(12)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(theme.surface)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(theme.onSurface.opacity(0.1), lineWidth: 1)
                                    )
                            )
                            
                            // Filter Button
                            Button(action: { showingFilters.toggle() }) {
                                Image(systemName: "line.3.horizontal.decrease.circle")
                                    .font(.title2)
                                    .foregroundColor(theme.primary)
                            }
                        }
                        
                        // Active Filters
                        if selectedCategory != nil || selectedRarity != nil {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 8) {
                                    if let category = selectedCategory {
                                        FilterChip(
                                            title: "\(category.emoji) \(category.displayName)",
                                            onRemove: { selectedCategory = nil }
                                        )
                                    }
                                    
                                    if let rarity = selectedRarity {
                                        FilterChip(
                                            title: rarity.displayName,
                                            onRemove: { selectedRarity = nil }
                                        )
                                    }
                                    
                                    Button("Clear All") {
                                        selectedCategory = nil
                                        selectedRarity = nil
                                    }
                                    .font(.caption)
                                    .foregroundColor(theme.primary)
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    // Badge Grid
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(filteredBadges) { badge in
                            BadgeCard(badge: badge) {
                                // Handle badge selection
                                handleBadgeSelection(badge)
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    // Empty State
                    if filteredBadges.isEmpty && !badgeManager.isLoading {
                        VStack(spacing: 16) {
                            Image(systemName: "trophy.circle")
                                .font(.system(size: 60))
                                .foregroundColor(theme.onSurface.opacity(0.3))
                            
                            Text("No badges found")
                                .font(.headline)
                                .foregroundColor(theme.onSurface.opacity(0.7))
                            
                            Text("Try adjusting your filters or complete more quests to unlock badges!")
                                .font(.subheadline)
                                .foregroundColor(theme.onSurface.opacity(0.5))
                                .multilineTextAlignment(.center)
                        }
                        .padding(40)
                    }
                }
                .padding(.bottom, 100)
            }
            .background(theme.background)
            .navigationTitle("🏆 Badge Collection")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingFilters) {
                BadgeFilterSheet(
                    selectedCategory: $selectedCategory,
                    selectedRarity: $selectedRarity
                )
            }
            .task {
                await badgeManager.loadUserBadges(for: UUID()) // Mock user ID
            }
            .refreshable {
                await badgeManager.loadUserBadges(for: UUID())
            }
        }
    }
    
    private func handleBadgeSelection(_ badge: Badge) {
        // Handle badge tap - could show detail view, unlock animation, etc.
        if !badge.isUnlocked {
            Task {
                let unlocked = await badgeManager.unlockBadge(badge.id, for: UUID())
                if unlocked {
                    // Show success feedback
                }
            }
        }
    }
}

// MARK: - Supporting Components

struct FilterChip: View {
    let title: String
    let onRemove: () -> Void
    @Environment(\.theme) var theme
    
    var body: some View {
        HStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(theme.primary)
            
            Button(action: onRemove) {
                Image(systemName: "xmark")
                    .font(.caption2)
                    .foregroundColor(theme.primary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.primary.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(theme.primary.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct FeaturedBadgeShowcase: View {
    let showcase: BadgeShowcase
    @Environment(\.theme) var theme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(showcase.title)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)
                    
                    Text(showcase.description)
                        .font(.subheadline)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                }
                
                Spacer()
                
                if showcase.isLimited, let expiresAt = showcase.expiresAt {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("LIMITED TIME")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.red)
                        
                        Text(timeRemaining(until: expiresAt))
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                }
            }
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(showcase.badges) { badge in
                        BadgeCard(badge: badge) {
                            // Handle showcase badge tap
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(LinearGradient(
                    colors: [.purple.opacity(0.1), .pink.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(LinearGradient(
                            colors: [.purple.opacity(0.3), .pink.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ), lineWidth: 1)
                )
        )
    }
    
    private func timeRemaining(until date: Date) -> String {
        let timeInterval = date.timeIntervalSinceNow
        let days = Int(timeInterval / 86400)
        let hours = Int((timeInterval.truncatingRemainder(dividingBy: 86400)) / 3600)
        
        if days > 0 {
            return "\(days)d \(hours)h"
        } else {
            return "\(hours)h"
        }
    }
}

#Preview {
    BadgeCollectionView()
        .preferredColorScheme(.dark)
}
