//
//  FeedFiltersView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct FeedFiltersView: View {
    @Binding var selectedFilter: FeedFilter
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Text("🎯")
                        .font(.system(size: 50))
                    Text("Customize Your Feed")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("Choose what content you want to see")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)
                
                // Filter Options
                VStack(spacing: 16) {
                    ForEach(FeedFilter.allCases, id: \.self) { filter in
                        FilterOptionRow(
                            filter: filter,
                            isSelected: selectedFilter == filter
                        ) {
                            selectedFilter = filter
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Apply <PERSON><PERSON>(action: {
                    dismiss()
                }) {
                    Text("Apply Filter")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.purple)
                        )
                }
                .padding(.horizontal)
                .padding(.bottom, 30)
            }
            .navigationTitle("Feed Filters")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct FilterOptionRow: View {
    let filter: FeedFilter
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                Image(systemName: filter.icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : .purple)
                    .frame(width: 40, height: 40)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isSelected ? Color.purple : Color.purple.opacity(0.1))
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(filter.rawValue)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(filter.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Selection Indicator
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(isSelected ? .purple : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Filter Extensions
extension FeedFilter {
    var description: String {
        switch self {
        case .all:
            return "See all content types in your feed"
        case .stocks:
            return "Stock market news and analysis"
        case .crypto:
            return "Cryptocurrency updates and trends"
        case .news:
            return "General financial news and market updates"
        case .education:
            return "Educational content and tutorials"
        case .bookmarked:
            return "Content you've saved for later"
        }
    }
}

#Preview {
    FeedFiltersView(selectedFilter: .constant(.all))
}
