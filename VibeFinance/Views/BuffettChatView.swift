//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI
import NaturalLanguage
import Speech
import AVFoundation

// MARK: - Financial Agent Models

struct FinancialAgent: Identifiable {
    let id = UUID()
    let name: String
    let title: String
    let expertise: String
    let icon: String
    let colorType: AgentColorType
    let description: String
    let specialties: [String]

    enum AgentColorType {
        case accent, primary, secondary, success, warning

        func color(for theme: ThemeColors) -> Color {
            switch self {
            case .accent: return theme.accentColor
            case .primary: return theme.primaryColor
            case .secondary: return theme.secondaryColor
            case .success: return theme.success
            case .warning: return theme.warning
            }
        }
    }

    static let allAgents: [FinancialAgent] = [
        FinancialAgent(
            name: "Finance Buddy",
            title: "Your Gen Z Finance BFF 💅",
            expertise: "Making Finance Fun & Viral",
            icon: "heart.fill",
            colorType: .accent,
            description: "Your bestie who spills ALL the tea about money! 🍵 Explains finance like you're texting your ride-or-die friend. No cap! 💯✨",
            specialties: ["Gen Z Vibes", "TikTok Finance", "Meme Money", "Squad Goals", "No Cap Advice"]
        ),
        FinancialAgent(
            name: "Warren Buffett",
            title: "Value Investing Guru",
            expertise: "Long-term Value Investing",
            icon: "chart.pie.fill",
            colorType: .secondary,
            description: "The Oracle of Omaha specializes in finding undervalued companies with strong fundamentals.",
            specialties: ["Value Investing", "Company Analysis", "Long-term Strategy"]
        ),
        FinancialAgent(
            name: "Ray Dalio",
            title: "Risk Management Expert",
            expertise: "Portfolio Diversification",
            icon: "shield.checkered",
            colorType: .primary,
            description: "Founder of Bridgewater Associates, expert in risk management and economic principles.",
            specialties: ["Risk Management", "Diversification", "Economic Analysis"]
        ),
        FinancialAgent(
            name: "Peter Lynch",
            title: "Growth Stock Specialist",
            expertise: "Growth Investing",
            icon: "chart.line.uptrend.xyaxis",
            colorType: .success,
            description: "Former Fidelity manager known for finding growth stocks and consumer-focused investments.",
            specialties: ["Growth Stocks", "Consumer Analysis", "Market Research"]
        ),
        FinancialAgent(
            name: "Benjamin Graham",
            title: "Security Analysis Master",
            expertise: "Fundamental Analysis",
            icon: "magnifyingglass.circle.fill",
            colorType: .secondary,
            description: "Father of value investing and author of 'The Intelligent Investor'.",
            specialties: ["Security Analysis", "Financial Statements", "Margin of Safety"]
        ),
        FinancialAgent(
            name: "John Bogle",
            title: "Index Fund Pioneer",
            expertise: "Passive Investing",
            icon: "chart.bar.fill",
            colorType: .warning,
            description: "Founder of Vanguard and pioneer of low-cost index fund investing.",
            specialties: ["Index Funds", "Cost Management", "Long-term Planning"]
        ),
        FinancialAgent(
            name: "Cathie Wood",
            title: "Innovation Investor",
            expertise: "Disruptive Technology",
            icon: "bolt.circle.fill",
            colorType: .primary,
            description: "ARK Invest founder focused on disruptive innovation and technology investments.",
            specialties: ["Technology", "Innovation", "Disruptive Growth"]
        )
    ]
}

// MARK: - Conversation Context

enum ConversationContext: String, CaseIterable {
    case general = "General"
    case investing = "Investing"
    case portfolio = "Portfolio"
    case market = "Market Analysis"
    case risk = "Risk Management"

    var icon: String {
        switch self {
        case .general: return "brain.head.profile"
        case .investing: return "chart.line.uptrend.xyaxis"
        case .portfolio: return "chart.pie"
        case .market: return "chart.bar"
        case .risk: return "shield.checkered"
        }
    }

    func color(for theme: ThemeColors) -> Color {
        switch self {
        case .general: return theme.accentColor
        case .investing: return theme.success
        case .portfolio: return theme.primaryColor
        case .market: return theme.secondaryColor
        case .risk: return theme.warning
        }
    }
}

struct BuffettChatView: View {
    @State private var selectedAgent: FinancialAgent?
    @State private var showingAgentChat = false
    @Environment(\.theme) var theme

    // Apple Intelligence Features
    @State private var isRecording = false
    @State private var showingVoicePermission = false
    @State private var smartSuggestions: [String] = []
    @State private var isProcessingAI = false
    @State private var lastTranscription = ""

    var body: some View {
        NavigationView {
            VStack {
                Text("💬 Your Finance Squad")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding()

                Text("Chat with your AI besties about money! 💅✨")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .padding(.bottom)

                // Apple Intelligence Demo Section
                VStack(spacing: 20) {
                    Text("🧠 Apple Intelligence Enhanced")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    // Voice Recognition Button
                    Button(action: {
                        if isRecording {
                            stopVoiceRecognition()
                        } else {
                            startVoiceRecognition()
                        }
                    }) {
                        HStack {
                            Image(systemName: isRecording ? "mic.fill" : "mic")
                                .font(.title2)
                                .foregroundColor(isRecording ? .red : .blue)
                                .scaleEffect(isRecording ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.2), value: isRecording)

                            Text(isRecording ? "Recording..." : "Voice Input")
                                .fontWeight(.medium)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(isRecording ? Color.red : Color.blue, lineWidth: 2)
                                )
                        )
                        .foregroundColor(.white)
                    }

                    // Last Transcription
                    if !lastTranscription.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Last Voice Input:")
                                .font(.caption)
                                .foregroundColor(.gray)

                            Text(lastTranscription)
                                .font(.body)
                                .foregroundColor(.white)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(.ultraThinMaterial)
                                )
                        }
                    }

                    // Smart Suggestions
                    if !smartSuggestions.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("💡 Smart Suggestions:")
                                .font(.headline)
                                .foregroundColor(.white)

                            ForEach(smartSuggestions, id: \.self) { suggestion in
                                Button(action: {
                                    // Handle suggestion tap
                                    ProductionConfig.log("Suggestion tapped: \(suggestion)", category: "AI", level: .info)
                                }) {
                                    HStack {
                                        Text(suggestion)
                                            .font(.body)
                                            .foregroundColor(.white)
                                        Spacer()
                                        Image(systemName: "arrow.right.circle")
                                            .foregroundColor(.blue)
                                    }
                                    .padding()
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(.ultraThinMaterial)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 8)
                                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                                            )
                                    )
                                }
                            }
                        }
                    }

                    Text("🚀 Enhanced with Apple's Natural Language Processing")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                }
                .padding()

                Spacer()
            }
            .background(theme.background)
            .navigationTitle("AI Advisors")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    // MARK: - Apple Intelligence Methods

    private func analyzeUserIntent(_ message: String) -> String {
        let tagger = NLTagger(tagSchemes: [.tokenType, .lexicalClass])
        tagger.string = message

        var intent = "general"
        let range = message.startIndex..<message.endIndex

        tagger.enumerateTags(in: range, unit: .word, scheme: .lexicalClass) { tag, tokenRange in
            let token = String(message[tokenRange]).lowercased()

            if ["invest", "buy", "sell", "portfolio", "stock"].contains(token) {
                intent = "investment"
            } else if ["budget", "save", "spend", "money", "expense"].contains(token) {
                intent = "budgeting"
            } else if ["learn", "explain", "what", "how", "why"].contains(token) {
                intent = "education"
            } else if ["market", "news", "trend", "analysis"].contains(token) {
                intent = "market_analysis"
            }

            return true
        }

        return intent
    }

    private func generateSmartSuggestions(for intent: String) -> [String] {
        switch intent {
        case "investment":
            return [
                "📊 Show me portfolio analysis",
                "🎯 Set investment goals",
                "📈 Check market trends"
            ]
        case "budgeting":
            return [
                "💰 Create a budget plan",
                "📱 Track my expenses",
                "🎯 Set savings goals"
            ]
        case "education":
            return [
                "📚 Start a learning quest",
                "🎮 Try investment simulator",
                "📖 Read financial basics"
            ]
        case "market_analysis":
            return [
                "📊 View market dashboard",
                "📰 Read latest news",
                "🔍 Analyze specific stocks"
            ]
        default:
            return [
                "💡 Get personalized tips",
                "🎯 Set financial goals",
                "📈 Check my progress"
            ]
        }
    }

    private func startVoiceRecognition() {
        Task {
            do {
                // Request speech recognition authorization
                let authStatus = await withCheckedContinuation { continuation in
                    SFSpeechRecognizer.requestAuthorization { status in
                        continuation.resume(returning: status)
                    }
                }

                guard authStatus == .authorized else {
                    await MainActor.run {
                        showingVoicePermission = true
                    }
                    return
                }

                await MainActor.run {
                    isRecording = true
                    ProductionConfig.log("🎤 Voice recognition started", category: "AI", level: .info)
                }

                // Simulate voice input for demo
                try await Task.sleep(nanoseconds: 2_000_000_000)

                await MainActor.run {
                    lastTranscription = "Tell me about Warren Buffett's investment strategy"
                    isRecording = false

                    // Analyze intent and generate suggestions
                    let intent = analyzeUserIntent(lastTranscription)
                    smartSuggestions = generateSmartSuggestions(for: intent)

                    ProductionConfig.log("🧠 AI analyzed intent: \(intent)", category: "AI", level: .info)
                }

            } catch {
                await MainActor.run {
                    isRecording = false
                    ProductionConfig.log("❌ Voice recognition failed: \(error)", category: "AI", level: .error)
                }
            }
        }
    }

    private func stopVoiceRecognition() {
        isRecording = false
        ProductionConfig.log("🛑 Voice recognition stopped", category: "AI", level: .info)
    }
}
