//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Financial Agent Models

struct FinancialAgent: Identifiable {
    let id = UUID()
    let name: String
    let title: String
    let expertise: String
    let icon: String
    let colorType: AgentColorType
    let description: String
    let specialties: [String]

    enum AgentColorType {
        case accent, primary, secondary, success, warning

        func color(for theme: ThemeColors) -> Color {
            switch self {
            case .accent: return theme.accent
            case .primary: return theme.primary
            case .secondary: return theme.secondary
            case .success: return theme.success
            case .warning: return theme.warning
            }
        }
    }

    static let allAgents: [FinancialAgent] = [
        FinancialAgent(
            name: "Finance Buddy",
            title: "Your Gen Z Finance BFF 💅",
            expertise: "Making Finance Fun & Viral",
            icon: "heart.fill",
            colorType: .accent,
            description: "Your bestie who spills ALL the tea about money! 🍵 Explains finance like you're texting your ride-or-die friend. No cap! 💯✨",
            specialties: ["Gen Z Vibes", "TikTok Finance", "Meme Money", "Squad Goals", "No Cap Advice"]
        ),
        FinancialAgent(
            name: "<PERSON> Buffett",
            title: "Value Investing Guru",
            expertise: "Long-term Value Investing",
            icon: "chart.pie.fill",
            colorType: .secondary,
            description: "The Oracle of Omaha specializes in finding undervalued companies with strong fundamentals.",
            specialties: ["Value Investing", "Company Analysis", "Long-term Strategy"]
        ),
        FinancialAgent(
            name: "Ray Dalio",
            title: "Risk Management Expert",
            expertise: "Portfolio Diversification",
            icon: "shield.checkered",
            colorType: .primary,
            description: "Founder of Bridgewater Associates, expert in risk management and economic principles.",
            specialties: ["Risk Management", "Diversification", "Economic Analysis"]
        ),
        FinancialAgent(
            name: "Peter Lynch",
            title: "Growth Stock Specialist",
            expertise: "Growth Investing",
            icon: "chart.line.uptrend.xyaxis",
            colorType: .success,
            description: "Former Fidelity manager known for finding growth stocks and consumer-focused investments.",
            specialties: ["Growth Stocks", "Consumer Analysis", "Market Research"]
        ),
        FinancialAgent(
            name: "Benjamin Graham",
            title: "Security Analysis Master",
            expertise: "Fundamental Analysis",
            icon: "magnifyingglass.circle.fill",
            colorType: .secondary,
            description: "Father of value investing and author of 'The Intelligent Investor'.",
            specialties: ["Security Analysis", "Financial Statements", "Margin of Safety"]
        ),
        FinancialAgent(
            name: "John Bogle",
            title: "Index Fund Pioneer",
            expertise: "Passive Investing",
            icon: "chart.bar.fill",
            colorType: .warning,
            description: "Founder of Vanguard and pioneer of low-cost index fund investing.",
            specialties: ["Index Funds", "Cost Management", "Long-term Planning"]
        ),
        FinancialAgent(
            name: "Cathie Wood",
            title: "Innovation Investor",
            expertise: "Disruptive Technology",
            icon: "bolt.circle.fill",
            colorType: .primary,
            description: "ARK Invest founder focused on disruptive innovation and technology investments.",
            specialties: ["Technology", "Innovation", "Disruptive Growth"]
        )
    ]
}

// MARK: - Conversation Context

enum ConversationContext: String, CaseIterable {
    case general = "General"
    case investing = "Investing"
    case portfolio = "Portfolio"
    case market = "Market Analysis"
    case risk = "Risk Management"

    var icon: String {
        switch self {
        case .general: return "brain.head.profile"
        case .investing: return "chart.line.uptrend.xyaxis"
        case .portfolio: return "chart.pie"
        case .market: return "chart.bar"
        case .risk: return "shield.checkered"
        }
    }

    func color(for theme: ThemeColors) -> Color {
        switch self {
        case .general: return theme.accent
        case .investing: return theme.success
        case .portfolio: return theme.primary
        case .market: return theme.secondary
        case .risk: return theme.warning
        }
    }
}

struct BuffettChatView: View {
    @State private var selectedAgent: FinancialAgent?
    @State private var showingAgentChat = false
    @Environment(\.theme) var theme

    var body: some View {
        NavigationView {
            ZStack {
                // Unified theme background
                theme.background
                    .ignoresSafeArea()

                ScrollView {
                    LazyVStack(spacing: 16) {
                        // Header
                        VStack(spacing: 8) {
                            Text("💬 Your Finance Squad")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(theme.onBackground)

                            Text("Chat with your AI besties about money! 💅✨")
                                .font(.subheadline)
                                .foregroundColor(theme.onBackground.opacity(0.8))
                        }
                        .padding(.top, 20)

                        // Featured Finance Buddy
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("✨ Your Main Bestie")
                                    .font(.headline)
                                    .foregroundColor(theme.onBackground)

                                Spacer()

                                Text("MOST POPULAR")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(.purple)
                                    )
                            }
                            .padding(.horizontal, 16)

                            FeaturedFinanceBuddyCard {
                                selectedAgent = FinancialAgent.allAgents[0] // Finance Buddy
                                showingAgentChat = true
                            }
                            .padding(.horizontal, 16)
                        }

                        // Other Financial Agents
                        VStack(alignment: .leading, spacing: 12) {
                            Text("🏦 Other Finance Experts")
                                .font(.headline)
                                .foregroundColor(theme.onBackground)
                                .padding(.horizontal, 16)

                        // Financial Agents Grid
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                            ForEach(Array(FinancialAgent.allAgents.dropFirst())) { agent in // Skip Finance Buddy (first agent)
                                FinancialAgentCard(agent: agent) {
                                    selectedAgent = agent
                                    showingAgentChat = true
                                }
                            }
                        }
                        .padding(.horizontal, 16)

                        // Quick Vibes
                        VStack(alignment: .leading, spacing: 12) {
                            Text("🚀 Quick Vibes")
                                .font(.headline)
                                .foregroundColor(theme.onBackground)
                                .padding(.horizontal, 16)

                            VStack(spacing: 8) {
                                QuickActionRow(
                                    icon: "chart.line.uptrend.xyaxis",
                                    title: "Portfolio Check ✨",
                                    description: "See how your bag is doing bestie!",
                                    action: { /* Handle action */ }
                                )

                                QuickActionRow(
                                    icon: "magnifyingglass",
                                    title: "Stock Tea 🍵",
                                    description: "Get the real tea on any stock",
                                    action: { /* Handle action */ }
                                )

                                QuickActionRow(
                                    icon: "lightbulb",
                                    title: "Investment Inspo 💡",
                                    description: "Fresh ideas to grow your wealth",
                                    action: { /* Handle action */ }
                                )
                            }
                            .padding(.horizontal, 16)
                        }
                        .padding(.top, 20)
                    }
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle("AI Advisors")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingAgentChat) {
                if let agent = selectedAgent {
                    AgentChatView(agent: agent)
                }
            }
        }
    }
}

// MARK: - Supporting Components

struct FinancialAgentCard: View {
    let agent: FinancialAgent
    let onTap: () -> Void
    @Environment(\.theme) var theme

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Agent Icon
                ZStack {
                    Circle()
                        .fill(agent.colorType.color(for: theme).opacity(0.2))
                        .frame(width: 60, height: 60)

                    Image(systemName: agent.icon)
                        .font(.title2)
                        .foregroundColor(agent.colorType.color(for: theme))
                }

                // Agent Info
                VStack(spacing: 4) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .lineLimit(1)

                    Text(agent.title)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                        .lineLimit(2)
                        .multilineTextAlignment(.center)

                    Text(agent.expertise)
                        .font(.caption2)
                        .foregroundColor(agent.colorType.color(for: theme))
                        .lineLimit(1)
                }
            }
            .padding(16)
            .frame(maxWidth: .infinity)
            .frame(height: 160)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct QuickActionRow: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.yellow)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.5))
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AgentChatView: View {
    let agent: FinancialAgent
    @Environment(\.dismiss) private var dismiss
    @Environment(\.theme) var theme
    @State private var messages: [String] = []
    @State private var newMessage = ""

    var body: some View {
        NavigationView {
            VStack {
                // Agent Header
                HStack(spacing: 12) {
                    Image(systemName: agent.icon)
                        .font(.title2)
                        .foregroundColor(agent.colorType.color(for: theme))

                    VStack(alignment: .leading) {
                        Text(agent.name)
                            .font(.headline)
                            .fontWeight(.bold)
                        Text(agent.expertise)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
                .padding()
                .background(Color(.systemGray6))

                // Chat Area
                ScrollView {
                    LazyVStack(spacing: 12) {
                        Text("Chat with \(agent.name) - Coming Soon!")
                            .foregroundColor(.secondary)
                            .padding()
                    }
                }

                // Message Input
                HStack {
                    TextField("Ask \(agent.name.components(separatedBy: " ").first ?? "")...", text: $newMessage)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Button("Send") {
                        // Handle send
                    }
                    .disabled(newMessage.isEmpty)
                }
                .padding()
            }
            .navigationTitle(agent.name)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Featured Finance Buddy Card
struct FeaturedFinanceBuddyCard: View {
    let action: () -> Void
    @Environment(\.theme) var theme

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Animated heart icon
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.purple, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 60, height: 60)

                    Image(systemName: "heart.fill")
                        .foregroundColor(.white)
                        .font(.title2)
                }

                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("Finance Buddy")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(theme.onBackground)

                        Text("💅")
                            .font(.title3)
                    }

                    Text("Your Gen Z Finance BFF")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.purple)

                    Text("Spills ALL the tea about money! 🍵")
                        .font(.caption)
                        .foregroundColor(theme.onBackground.opacity(0.7))
                        .lineLimit(2)

                    HStack {
                        ForEach(["💯", "✨", "🔥"], id: \.self) { emoji in
                            Text(emoji)
                                .font(.caption)
                        }

                        Spacer()

                        Text("START CHAT")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(
                                        LinearGradient(
                                            colors: [.purple, .pink],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                            )
                    }
                }

                Spacer()
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                LinearGradient(
                                    colors: [.purple.opacity(0.5), .pink.opacity(0.5)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 2
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    BuffettChatView()
        .preferredColorScheme(.dark)
}
