//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Financial Agent Models

struct FinancialAgent: Identifiable {
    let id = UUID()
    let name: String
    let title: String
    let expertise: String
    let icon: String
    let colorType: AgentColorType
    let description: String
    let specialties: [String]

    enum AgentColorType {
        case accent, primary, secondary, success, warning

        func color(for theme: ThemeColors) -> Color {
            switch self {
            case .accent: return theme.accentColor
            case .primary: return theme.primaryColor
            case .secondary: return theme.secondaryColor
            case .success: return theme.success
            case .warning: return theme.warning
            }
        }
    }

    static let allAgents: [FinancialAgent] = [
        FinancialAgent(
            name: "Finance Buddy",
            title: "Your Gen Z Finance BFF 💅",
            expertise: "Making Finance Fun & Viral",
            icon: "heart.fill",
            colorType: .accent,
            description: "Your bestie who spills ALL the tea about money! 🍵 Explains finance like you're texting your ride-or-die friend. No cap! 💯✨",
            specialties: ["Gen Z Vibes", "TikTok Finance", "Meme Money", "Squad Goals", "No Cap Advice"]
        ),
        FinancialAgent(
            name: "<PERSON> Buffett",
            title: "Value Investing Guru",
            expertise: "Long-term Value Investing",
            icon: "chart.pie.fill",
            colorType: .secondary,
            description: "The Oracle of Omaha specializes in finding undervalued companies with strong fundamentals.",
            specialties: ["Value Investing", "Company Analysis", "Long-term Strategy"]
        ),
        FinancialAgent(
            name: "Ray Dalio",
            title: "Risk Management Expert",
            expertise: "Portfolio Diversification",
            icon: "shield.checkered",
            colorType: .primary,
            description: "Founder of Bridgewater Associates, expert in risk management and economic principles.",
            specialties: ["Risk Management", "Diversification", "Economic Analysis"]
        ),
        FinancialAgent(
            name: "Peter Lynch",
            title: "Growth Stock Specialist",
            expertise: "Growth Investing",
            icon: "chart.line.uptrend.xyaxis",
            colorType: .success,
            description: "Former Fidelity manager known for finding growth stocks and consumer-focused investments.",
            specialties: ["Growth Stocks", "Consumer Analysis", "Market Research"]
        ),
        FinancialAgent(
            name: "Benjamin Graham",
            title: "Security Analysis Master",
            expertise: "Fundamental Analysis",
            icon: "magnifyingglass.circle.fill",
            colorType: .secondary,
            description: "Father of value investing and author of 'The Intelligent Investor'.",
            specialties: ["Security Analysis", "Financial Statements", "Margin of Safety"]
        ),
        FinancialAgent(
            name: "John Bogle",
            title: "Index Fund Pioneer",
            expertise: "Passive Investing",
            icon: "chart.bar.fill",
            colorType: .warning,
            description: "Founder of Vanguard and pioneer of low-cost index fund investing.",
            specialties: ["Index Funds", "Cost Management", "Long-term Planning"]
        ),
        FinancialAgent(
            name: "Cathie Wood",
            title: "Innovation Investor",
            expertise: "Disruptive Technology",
            icon: "bolt.circle.fill",
            colorType: .primary,
            description: "ARK Invest founder focused on disruptive innovation and technology investments.",
            specialties: ["Technology", "Innovation", "Disruptive Growth"]
        )
    ]
}

// MARK: - Conversation Context

enum ConversationContext: String, CaseIterable {
    case general = "General"
    case investing = "Investing"
    case portfolio = "Portfolio"
    case market = "Market Analysis"
    case risk = "Risk Management"

    var icon: String {
        switch self {
        case .general: return "brain.head.profile"
        case .investing: return "chart.line.uptrend.xyaxis"
        case .portfolio: return "chart.pie"
        case .market: return "chart.bar"
        case .risk: return "shield.checkered"
        }
    }

    func color(for theme: ThemeColors) -> Color {
        switch self {
        case .general: return theme.accentColor
        case .investing: return theme.success
        case .portfolio: return theme.primaryColor
        case .market: return theme.secondaryColor
        case .risk: return theme.warning
        }
    }
}

struct BuffettChatView: View {
    @State private var selectedAgent: FinancialAgent?
    @State private var showingAgentChat = false
    @Environment(\.theme) var theme

    var body: some View {
        NavigationView {
            VStack {
                Text("💬 Your Finance Squad")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding()

                Text("Chat with your AI besties about money! 💅✨")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .padding(.bottom)

                Spacer()

                Text("Coming Soon!")
                    .font(.headline)
                    .foregroundColor(.white)

                Spacer()
            }
            .background(theme.background)
            .navigationTitle("AI Advisors")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}
