//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI
import NaturalLanguage
import Speech
import AVFoundation

// MARK: - Financial Agent Models

struct FinancialAgent: Identifiable {
    let id = UUID()
    let name: String
    let title: String
    let expertise: String
    let icon: String
    let colorType: AgentColorType
    let description: String
    let specialties: [String]

    enum AgentColorType {
        case accent, primary, secondary, success, warning

        func color(for theme: ThemeColors) -> Color {
            switch self {
            case .accent: return theme.accentColor
            case .primary: return theme.primaryColor
            case .secondary: return theme.secondaryColor
            case .success: return theme.success
            case .warning: return theme.warning
            }
        }
    }

    static let allAgents: [FinancialAgent] = [
        FinancialAgent(
            name: "Finance Buddy",
            title: "Your Gen Z Finance BFF 💅",
            expertise: "Making Finance Fun & Viral",
            icon: "heart.fill",
            colorType: .accent,
            description: "Your bestie who spills ALL the tea about money! 🍵 Explains finance like you're texting your ride-or-die friend. No cap! 💯✨",
            specialties: ["Gen Z Vibes", "TikTok Finance", "Meme Money", "Squad Goals", "No Cap Advice"]
        ),
        FinancialAgent(
            name: "Warren Buffett",
            title: "Value Investing Guru",
            expertise: "Long-term Value Investing",
            icon: "chart.pie.fill",
            colorType: .secondary,
            description: "The Oracle of Omaha specializes in finding undervalued companies with strong fundamentals.",
            specialties: ["Value Investing", "Company Analysis", "Long-term Strategy"]
        ),
        FinancialAgent(
            name: "Ray Dalio",
            title: "Risk Management Expert",
            expertise: "Portfolio Diversification",
            icon: "shield.checkered",
            colorType: .primary,
            description: "Founder of Bridgewater Associates, expert in risk management and economic principles.",
            specialties: ["Risk Management", "Diversification", "Economic Analysis"]
        ),
        FinancialAgent(
            name: "Peter Lynch",
            title: "Growth Stock Specialist",
            expertise: "Growth Investing",
            icon: "chart.line.uptrend.xyaxis",
            colorType: .success,
            description: "Former Fidelity manager known for finding growth stocks and consumer-focused investments.",
            specialties: ["Growth Stocks", "Consumer Analysis", "Market Research"]
        ),
        FinancialAgent(
            name: "Benjamin Graham",
            title: "Security Analysis Master",
            expertise: "Fundamental Analysis",
            icon: "magnifyingglass.circle.fill",
            colorType: .secondary,
            description: "Father of value investing and author of 'The Intelligent Investor'.",
            specialties: ["Security Analysis", "Financial Statements", "Margin of Safety"]
        ),
        FinancialAgent(
            name: "John Bogle",
            title: "Index Fund Pioneer",
            expertise: "Passive Investing",
            icon: "chart.bar.fill",
            colorType: .warning,
            description: "Founder of Vanguard and pioneer of low-cost index fund investing.",
            specialties: ["Index Funds", "Cost Management", "Long-term Planning"]
        ),
        FinancialAgent(
            name: "Cathie Wood",
            title: "Innovation Investor",
            expertise: "Disruptive Technology",
            icon: "bolt.circle.fill",
            colorType: .primary,
            description: "ARK Invest founder focused on disruptive innovation and technology investments.",
            specialties: ["Technology", "Innovation", "Disruptive Growth"]
        )
    ]
}

// MARK: - Conversation Context

enum ConversationContext: String, CaseIterable {
    case general = "General"
    case investing = "Investing"
    case portfolio = "Portfolio"
    case market = "Market Analysis"
    case risk = "Risk Management"

    var icon: String {
        switch self {
        case .general: return "brain.head.profile"
        case .investing: return "chart.line.uptrend.xyaxis"
        case .portfolio: return "chart.pie"
        case .market: return "chart.bar"
        case .risk: return "shield.checkered"
        }
    }

    func color(for theme: ThemeColors) -> Color {
        switch self {
        case .general: return theme.accentColor
        case .investing: return theme.success
        case .portfolio: return theme.primaryColor
        case .market: return theme.secondaryColor
        case .risk: return theme.warning
        }
    }
}

struct BuffettChatView: View {
    @State private var selectedAgent: FinancialAgent?
    @State private var showingAgentChat = false
    @Environment(\.theme) var theme

    // Apple Intelligence Features
    @State private var isRecording = false
    @State private var showingVoicePermission = false
    @State private var smartSuggestions: [String] = []
    @State private var isProcessingAI = false
    @State private var lastTranscription = ""

    var body: some View {
        NavigationView {
            VStack {
                Text("💬 Your Finance Squad")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding()

                Text("Chat with your AI besties about money! 💅✨")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .padding(.bottom)

                // ChatGPT-style Interface with Apple Intelligence
                VStack(spacing: 0) {
                    // Chat Header
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Image(systemName: "brain.head.profile")
                                    .foregroundColor(.blue)
                                    .font(.title2)

                                Text("Apple Intelligence")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            }

                            Text("Powered by local foundational models")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }

                        Spacer()

                        // Voice Input Button
                        Button(action: {
                            if isRecording {
                                stopVoiceRecognition()
                            } else {
                                startVoiceRecognition()
                            }
                        }) {
                            Image(systemName: isRecording ? "mic.fill" : "mic")
                                .font(.title2)
                                .foregroundColor(isRecording ? .red : .blue)
                                .scaleEffect(isRecording ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.2), value: isRecording)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                    )

                    // Chat Messages Area
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            // Welcome Message
                            ChatBubbleView(
                                message: "👋 Hey! I'm your AI financial advisor powered by Apple Intelligence. I can help you with investments, budgeting, market analysis, and financial education. What would you like to know?",
                                isUser: false,
                                timestamp: Date()
                            )

                            // Demo conversation
                            if !lastTranscription.isEmpty {
                                ChatBubbleView(
                                    message: lastTranscription,
                                    isUser: true,
                                    timestamp: Date()
                                )

                                ChatBubbleView(
                                    message: "Great question! Based on my analysis using Apple's Natural Language Processing, I can see you're interested in investment strategy. Here's what Warren Buffett would say: 'Time in the market beats timing the market.' 📈\n\nWould you like me to analyze your portfolio or suggest some beginner-friendly investments?",
                                    isUser: false,
                                    timestamp: Date()
                                )
                            }
                        }
                        .padding()
                    }
                    .frame(maxHeight: 400)

                    // Smart Suggestions
                    if !smartSuggestions.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 12) {
                                ForEach(smartSuggestions, id: \.self) { suggestion in
                                    Button(action: {
                                        lastTranscription = suggestion
                                        let intent = analyzeUserIntent(suggestion)
                                        smartSuggestions = generateSmartSuggestions(for: intent)
                                    }) {
                                        Text(suggestion)
                                            .font(.caption)
                                            .foregroundColor(.white)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 8)
                                            .background(
                                                RoundedRectangle(cornerRadius: 16)
                                                    .fill(.blue.opacity(0.2))
                                                    .overlay(
                                                        RoundedRectangle(cornerRadius: 16)
                                                            .stroke(.blue, lineWidth: 1)
                                                    )
                                            )
                                    }
                                }
                            }
                            .padding(.horizontal)
                        }
                        .padding(.vertical, 8)
                    }

                    // Input Area
                    HStack(spacing: 12) {
                        HStack {
                            Image(systemName: "message")
                                .foregroundColor(.gray)

                            Text("Ask me anything about finance...")
                                .foregroundColor(.gray)

                            Spacer()
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(.ultraThinMaterial)
                        )
                        .onTapGesture {
                            // Simulate starting a conversation
                            if smartSuggestions.isEmpty {
                                smartSuggestions = generateSmartSuggestions(for: "general")
                            }
                        }

                        Button(action: {
                            // Simulate sending message
                            lastTranscription = "Tell me about Warren Buffett's investment strategy"
                            let intent = analyzeUserIntent(lastTranscription)
                            smartSuggestions = generateSmartSuggestions(for: intent)
                        }) {
                            Image(systemName: "arrow.up.circle.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                        }
                    }
                    .padding()
                }
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.black.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(.white.opacity(0.1), lineWidth: 1)
                        )
                )
                .padding()

                Spacer()
            }
            .background(theme.background)
            .navigationTitle("AI Advisors")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    // MARK: - Apple Intelligence Methods

    private func analyzeUserIntent(_ message: String) -> String {
        let tagger = NLTagger(tagSchemes: [.tokenType, .lexicalClass])
        tagger.string = message

        var intent = "general"
        let range = message.startIndex..<message.endIndex

        tagger.enumerateTags(in: range, unit: .word, scheme: .lexicalClass) { tag, tokenRange in
            let token = String(message[tokenRange]).lowercased()

            if ["invest", "buy", "sell", "portfolio", "stock"].contains(token) {
                intent = "investment"
            } else if ["budget", "save", "spend", "money", "expense"].contains(token) {
                intent = "budgeting"
            } else if ["learn", "explain", "what", "how", "why"].contains(token) {
                intent = "education"
            } else if ["market", "news", "trend", "analysis"].contains(token) {
                intent = "market_analysis"
            }

            return true
        }

        return intent
    }

    private func generateSmartSuggestions(for intent: String) -> [String] {
        switch intent {
        case "investment":
            return [
                "💰 What are the best beginner stocks?",
                "📊 How should I diversify my portfolio?",
                "🏦 Explain index funds vs ETFs",
                "💎 Tell me about value investing"
            ]
        case "budgeting":
            return [
                "💳 Help me create a budget plan",
                "🎯 How much should I save monthly?",
                "📱 Best apps for expense tracking",
                "🏠 Should I rent or buy a home?"
            ]
        case "education":
            return [
                "📚 Explain compound interest simply",
                "🎓 What's the 50/30/20 rule?",
                "📖 Warren Buffett's top 5 principles",
                "💡 How does the stock market work?"
            ]
        case "market_analysis":
            return [
                "📈 Current market trends analysis",
                "🔍 Should I buy the dip?",
                "⚡ Impact of inflation on investments",
                "🌍 International vs domestic stocks"
            ]
        default:
            return [
                "💡 Give me investment advice",
                "💰 Help with budgeting",
                "📊 Analyze current market",
                "🎓 Teach me about finance",
                "🤖 What can Apple Intelligence do?"
            ]
        }
    }

    private func startVoiceRecognition() {
        Task {
            do {
                // Request speech recognition authorization
                let authStatus = await withCheckedContinuation { continuation in
                    SFSpeechRecognizer.requestAuthorization { status in
                        continuation.resume(returning: status)
                    }
                }

                guard authStatus == .authorized else {
                    await MainActor.run {
                        showingVoicePermission = true
                    }
                    return
                }

                await MainActor.run {
                    isRecording = true
                    ProductionConfig.log("🎤 Voice recognition started", category: "AI", level: .info)
                }

                // Simulate voice input for demo
                try await Task.sleep(nanoseconds: 2_000_000_000)

                await MainActor.run {
                    lastTranscription = "Tell me about Warren Buffett's investment strategy"
                    isRecording = false

                    // Analyze intent and generate suggestions
                    let intent = analyzeUserIntent(lastTranscription)
                    smartSuggestions = generateSmartSuggestions(for: intent)

                    ProductionConfig.log("🧠 AI analyzed intent: \(intent)", category: "AI", level: .info)
                }

            } catch {
                await MainActor.run {
                    isRecording = false
                    ProductionConfig.log("❌ Voice recognition failed: \(error)", category: "AI", level: .error)
                }
            }
        }
    }

    private func stopVoiceRecognition() {
        isRecording = false
        ProductionConfig.log("🛑 Voice recognition stopped", category: "AI", level: .info)
    }
}

// MARK: - ChatGPT-style Chat Bubble Component

struct ChatBubbleView: View {
    let message: String
    let isUser: Bool
    let timestamp: Date

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if !isUser {
                // AI Avatar
                Circle()
                    .fill(.blue.gradient)
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    )
            }

            VStack(alignment: isUser ? .trailing : .leading, spacing: 4) {
                // Message Content
                Text(message)
                    .font(.body)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 18)
                            .fill(isUser ? .blue.opacity(0.8) : .gray.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 18)
                                    .stroke(.white.opacity(0.1), lineWidth: 1)
                            )
                    )
                    .frame(maxWidth: .infinity, alignment: isUser ? .trailing : .leading)

                // Timestamp
                Text(timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .padding(.horizontal, 4)
            }

            if isUser {
                // User Avatar
                Circle()
                    .fill(.green.gradient)
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "person.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    )
            }
        }
        .padding(.horizontal, isUser ? 40 : 0)
    }
}
