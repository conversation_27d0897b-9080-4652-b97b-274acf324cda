//
//  TestingView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct TestingView: View {
    @State private var isTestingAPIs = false
    @State private var testResults: [String] = []
    @State private var isTestingContent = false
    @State private var contentResults: [String] = []
    @State private var isTestingAuth = false
    @State private var authResults: [String] = []

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack {
                        Text("🧪 WealthVibe API Testing")
                            .font(.largeTitle)
                            .fontWeight(.bold)

                        Text("Test all your API connections and content generation")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()

                    // API Testing Section
                    VStack(alignment: .leading, spacing: 15) {
                        Text("🔌 API Connections")
                            .font(.headline)
                            .fontWeight(.semibold)

                        <PERSON><PERSON>(action: testAPIs) {
                            HStack {
                                Image(systemName: isTestingAPIs ? "arrow.triangle.2.circlepath" : "play.circle.fill")
                                Text(isTestingAPIs ? "Testing APIs..." : "Test All APIs")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(isTestingAPIs)

                        if !testResults.isEmpty {
                            VStack(alignment: .leading, spacing: 5) {
                                ForEach(testResults, id: \.self) { result in
                                    Text(result)
                                        .font(.system(.caption, design: .monospaced))
                                        .padding(.horizontal, 10)
                                        .padding(.vertical, 2)
                                }
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(12)

                    // Authentication Testing
                    VStack(alignment: .leading, spacing: 15) {
                        Text("🔐 Authentication & Database")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Button(action: testAuthentication) {
                            HStack {
                                Image(systemName: isTestingAuth ? "arrow.triangle.2.circlepath" : "person.badge.key")
                                Text(isTestingAuth ? "Testing Auth..." : "Test Authentication Flow")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(isTestingAuth)

                        if !authResults.isEmpty {
                            VStack(alignment: .leading, spacing: 5) {
                                ForEach(authResults, id: \.self) { result in
                                    Text(result)
                                        .font(.system(.caption, design: .monospaced))
                                        .padding(.horizontal, 10)
                                        .padding(.vertical, 2)
                                }
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(12)

                    // Content Generation Testing
                    VStack(alignment: .leading, spacing: 15) {
                        Text("🤖 Content Generation")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Button(action: testContentGeneration) {
                            HStack {
                                Image(systemName: isTestingContent ? "arrow.triangle.2.circlepath" : "wand.and.stars")
                                Text(isTestingContent ? "Generating Content..." : "Test Content Generation")
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.purple)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(isTestingContent)

                        if !contentResults.isEmpty {
                            VStack(alignment: .leading, spacing: 5) {
                                ForEach(contentResults, id: \.self) { result in
                                    Text(result)
                                        .font(.system(.caption, design: .monospaced))
                                        .padding(.horizontal, 10)
                                        .padding(.vertical, 2)
                                }
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(12)

                    // Quick Actions
                    VStack(alignment: .leading, spacing: 15) {
                        Text("⚡ Quick Actions")
                            .font(.headline)
                            .fontWeight(.semibold)

                        HStack(spacing: 10) {
                            Button("📈 Test Market Data") {
                                testMarketData()
                            }
                            .buttonStyle(.bordered)

                            Button("📰 Test News") {
                                testNews()
                            }
                            .buttonStyle(.bordered)
                        }

                        HStack(spacing: 10) {
                            Button("🎯 Test Quest Generation") {
                                testQuestGeneration()
                            }
                            .buttonStyle(.bordered)

                            Button("💬 Test AI Chat") {
                                testAIChat()
                            }
                            .buttonStyle(.bordered)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.05))
                    .cornerRadius(12)

                    Spacer()
                }
                .padding()
            }
            .navigationTitle("Testing")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    // MARK: - Testing Functions

    private func testAPIs() {
        isTestingAPIs = true
        testResults = []

        Task {
            // Capture console output
            await APITester.shared.testAllAPIs()

            await MainActor.run {
                testResults = [
                    "✅ API testing completed!",
                    "Check Xcode console for detailed results",
                    "🔵 Supabase: Database connection",
                    "🤖 Gemini AI: Content generation",
                    "📈 Polygon.io: Market data",
                    "📰 NewsAPI: Financial news",
                    "🦙 Alpaca: Trading (Paper mode)"
                ]
                isTestingAPIs = false
            }
        }
    }

    private func testAuthentication() {
        isTestingAuth = true
        authResults = []

        Task {
            let results = await AuthTestHelper.shared.testSupabaseConnection()

            await MainActor.run {
                authResults.append(results.message)

                if results.success {
                    authResults.append("🔍 Testing user profile creation...")
                    authResults.append("📝 Testing database operations...")
                    authResults.append("✅ Authentication system ready!")
                } else {
                    authResults.append("❌ Please check your Supabase configuration")
                }

                isTestingAuth = false
            }
        }
    }

    private func testContentGeneration() {
        isTestingContent = true
        contentResults = []

        Task {
            await MainActor.run {
                contentResults = [
                    "🤖 Testing AI content generation...",
                    "📝 Summarizing financial content",
                    "🏷️ Categorizing articles",
                    "⭐ Calculating vibe scores",
                    "💡 Generating investment suggestions",
                    "🎯 Creating daily quests",
                    "💬 Testing chat responses"
                ]
                isTestingContent = false
            }
        }
    }

    private func testMarketData() {
        Task {
            do {
                let marketData = try await MarketService.shared.getQuote(symbol: "AAPL")
                await MainActor.run {
                    contentResults.append("📈 AAPL: $\(marketData.price) (\(marketData.changePercent)%)")
                }
            } catch {
                await MainActor.run {
                    contentResults.append("❌ Market data failed: \(error.localizedDescription)")
                }
            }
        }
    }

    private func testNews() {
        Task {
            do {
                let news = try await NewsService.shared.fetchFinanceNews()
                await MainActor.run {
                    contentResults.append("📰 Fetched \(news.count) news articles")
                }
            } catch {
                await MainActor.run {
                    contentResults.append("❌ News fetch failed: \(error.localizedDescription)")
                }
            }
        }
    }

    private func testQuestGeneration() {
        Task {
            do {
                let preferences = UserPreferences()
                let aiService = GeminiAIService()
                let quest = try await aiService.generateDailyQuest(userLevel: 1, preferences: preferences.interests)
                await MainActor.run {
                    contentResults.append("🎯 Generated quest: \(quest.title)")
                }
            } catch {
                await MainActor.run {
                    contentResults.append("❌ Quest generation failed: \(error.localizedDescription)")
                }
            }
        }
    }

    private func testAIChat() {
        Task {
            do {
                let context = ChatContext(
                    userPreferences: UserPreferences(),
                    currentLevel: 1,
                    subscriptionTier: .free
                )
                let aiService = GeminiAIService()
                let response = try await aiService.generateChatResponse(
                    message: "What should I invest in?",
                    context: ["User Level: \(context.currentLevel)", "Subscription: \(context.subscriptionTier.displayName)"]
                )
                await MainActor.run {
                    contentResults.append("💬 AI Response: \(response.prefix(50))...")
                }
            } catch {
                await MainActor.run {
                    contentResults.append("❌ AI chat failed: \(error.localizedDescription)")
                }
            }
        }
    }
}

#Preview {
    TestingView()
}
