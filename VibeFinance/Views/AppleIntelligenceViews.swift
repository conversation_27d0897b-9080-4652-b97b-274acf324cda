//
//  AppleIntelligenceViews.swift
//  VibeFinance - Apple Intelligence UI/UX Components
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import AppIntents

// MARK: - Apple Intelligence Chat Interface

struct AppleIntelligenceChatView: View {
    @State private var messages: [ChatMessage] = []
    @State private var inputText = ""
    @State private var isProcessing = false
    @State private var showingTypingIndicator = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Apple Intelligence Header
                appleIntelligenceHeader
                
                // Chat Messages
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(messages) { message in
                                ChatMessageView(message: message)
                                    .id(message.id)
                            }
                            
                            if showingTypingIndicator {
                                TypingIndicatorView()
                                    .id("typing")
                            }
                        }
                        .padding()
                    }
                    .onChange(of: messages.count) { _, _ in
                        withAnimation(.easeInOut(duration: 0.3)) {
                            if let lastMessageId = messages.last?.id {
                                proxy.scrollTo(lastMessageId, anchor: .bottom)
                            } else if showingTypingIndicator {
                                proxy.scrollTo("typing", anchor: .bottom)
                            }
                        }
                    }
                }
                
                // Input Area
                chatInputArea
            }
            .background(VibeFinanceDesignSystem.Colors.primaryGradient)
            .navigationTitle("AI Assistant")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                addWelcomeMessage()
            }
        }
    }
    
    private var appleIntelligenceHeader: some View {
        VStack(spacing: 8) {
            HStack(spacing: 12) {
                // Apple Intelligence Icon
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.blue, .purple, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: "brain.head.profile")
                        .font(.title3)
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Apple Intelligence")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text("Financial Assistant")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                // Privacy Indicator
                HStack(spacing: 4) {
                    Image(systemName: "lock.shield")
                        .font(.caption)
                        .foregroundColor(.green)
                    
                    Text("Private")
                        .font(.caption2)
                        .foregroundColor(.green)
                }
            }
            .padding(.horizontal)
            
            // Capabilities Pills
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(aiCapabilities, id: \.self) { capability in
                        Text(capability)
                            .font(.caption2)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color.white.opacity(0.2))
                            )
                            .foregroundColor(.white)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical, 12)
        .background(
            Rectangle()
                .fill(Color.black.opacity(0.3))
                .blur(radius: 10)
        )
    }
    
    private var chatInputArea: some View {
        VStack(spacing: 12) {
            // Quick Actions
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(quickActions, id: \.title) { action in
                        QuickActionButton(action: action) {
                            handleQuickAction(action)
                        }
                    }
                }
                .padding(.horizontal)
            }
            
            // Text Input
            HStack(spacing: 12) {
                HStack(spacing: 8) {
                    TextField("Ask about your portfolio, investments, or financial goals...", text: $inputText, axis: .vertical)
                        .textFieldStyle(PlainTextFieldStyle())
                        .lineLimit(1...4)
                    
                    if !inputText.isEmpty {
                        Button(action: clearInput) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(.systemBackground))
                )
                
                Button(action: sendMessage) {
                    Image(systemName: isProcessing ? "stop.circle.fill" : "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(inputText.isEmpty && !isProcessing ? .secondary : .blue)
                }
                .disabled(inputText.isEmpty && !isProcessing)
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 12)
        .background(
            Rectangle()
                .fill(Color(.systemBackground).opacity(0.95))
                .blur(radius: 10)
        )
    }
    
    private let aiCapabilities = [
        "Portfolio Analysis", "Investment Advice", "Risk Assessment", 
        "Market Insights", "Goal Planning", "Educational Content"
    ]
    
    private let quickActions = [
        QuickAction(title: "Analyze Portfolio", icon: "chart.pie", prompt: "Analyze my current portfolio performance and suggest improvements"),
        QuickAction(title: "Investment Ideas", icon: "lightbulb", prompt: "Give me some investment ideas based on my risk tolerance"),
        QuickAction(title: "Market Update", icon: "chart.line.uptrend.xyaxis", prompt: "What's happening in the market today?"),
        QuickAction(title: "Risk Check", icon: "shield.checkered", prompt: "Assess the risk level of my current portfolio")
    ]
    
    private func addWelcomeMessage() {
        let welcomeMessage = ChatMessage(
            content: "Hello! I'm your Apple Intelligence-powered financial assistant. I can help you analyze your portfolio, suggest investments, explain financial concepts, and answer any questions about your financial journey. What would you like to know?",
            isFromUser: false
        )
        messages.append(welcomeMessage)
    }
    
    private func sendMessage() {
        guard !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = ChatMessage(
            content: inputText,
            isFromUser: true
        )
        
        messages.append(userMessage)
        let messageText = inputText
        inputText = ""
        
        // Show typing indicator
        showingTypingIndicator = true
        isProcessing = true
        
        // Simulate AI response
        Task {
            await generateAIResponse(for: messageText)
        }
    }
    
    private func handleQuickAction(_ action: QuickAction) {
        inputText = action.subtitle
        sendMessage()
    }
    
    private func clearInput() {
        inputText = ""
    }
    
    private func generateAIResponse(for prompt: String) async {
        // Simulate processing delay
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
        
        await MainActor.run {
            showingTypingIndicator = false
            
            let aiResponse = generateMockResponse(for: prompt)
            let responseMessage = ChatMessage(
                content: aiResponse,
                isFromUser: false
            )
            
            messages.append(responseMessage)
            isProcessing = false
        }
    }
    
    private func generateMockResponse(for prompt: String) -> String {
        let lowercased = prompt.lowercased()
        
        if lowercased.contains("portfolio") {
            return "Based on your current portfolio, I can see you have a well-diversified mix of assets. Your portfolio shows a 12.5% return this year, which is performing well above the market average. I notice you have a slight overweight in technology stocks at 35% of your portfolio. Consider rebalancing by reducing tech exposure to 25-30% and increasing your international diversification."
        } else if lowercased.contains("investment") || lowercased.contains("ideas") {
            return "Given your moderate risk tolerance and long-term investment horizon, I'd suggest considering these opportunities: 1) Low-cost index funds like VTI for broad market exposure, 2) International diversification through VTIAX, 3) Some exposure to growth sectors like clean energy through ETFs. Remember Warren Buffett's advice: 'Time in the market beats timing the market.'"
        } else if lowercased.contains("market") {
            return "Today's market is showing mixed signals. The S&P 500 is up 0.75% while the NASDAQ is gaining 1.25%. Key drivers include positive earnings reports from tech companies and optimistic economic data. However, keep in mind that short-term market movements shouldn't affect your long-term investment strategy."
        } else if lowercased.contains("risk") {
            return "Your portfolio currently has a beta of 1.15, indicating slightly higher volatility than the market. Your Sharpe ratio of 1.42 shows good risk-adjusted returns. To reduce risk, consider: 1) Adding bonds (target 20-30% allocation), 2) Increasing international diversification, 3) Dollar-cost averaging for new investments."
        } else {
            return "I understand you're asking about \(prompt). As your AI financial assistant, I'm here to help with portfolio analysis, investment recommendations, market insights, and financial education. Could you be more specific about what aspect of your finances you'd like to explore?"
        }
    }
}

// MARK: - Supporting Views

// Using ChatMessageView from ChatComponents.swift

// Using TypingIndicatorView from ChatComponents.swift

struct QuickActionButton: View {
    let action: QuickAction
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 6) {
                Image(systemName: action.systemImage)
                    .font(.caption)

                Text(action.title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(Color.blue.opacity(0.1))
                    .overlay(
                        Capsule()
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
            .foregroundColor(.blue)
        }
    }
}

// MARK: - Data Models

// Using ChatMessage and MessageType from Chat.swift

struct QuickAction {
    let title: String
    let subtitle: String
    let intent: any AppIntent
    let systemImage: String

    // Convenience initializer for backward compatibility
    init(title: String, icon: String, prompt: String) {
        self.title = title
        self.subtitle = prompt
        self.intent = EmptyIntent()
        self.systemImage = icon
    }

    init(title: String, subtitle: String, intent: any AppIntent, systemImage: String) {
        self.title = title
        self.subtitle = subtitle
        self.intent = intent
        self.systemImage = systemImage
    }
}

// Empty intent for backward compatibility
struct EmptyIntent: AppIntent {
    static var title: LocalizedStringResource = "Empty Intent"

    func perform() async throws -> some IntentResult {
        return .result()
    }
}
