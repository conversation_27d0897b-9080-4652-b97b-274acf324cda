//
//  EnhancedSubscriptionView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import StoreKit

struct EnhancedSubscriptionView: View {
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var selectedBillingCycle: BillingCycle = .monthly
    @State private var selectedTier: SubscriptionTier = .pro
    @State private var showingPaymentSheet = false
    @State private var showingBillingHistory = false
    @State private var showingManageSubscription = false
    @State private var promoCode = ""
    @State private var showingPromoCodeField = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    subscriptionHeader
                    
                    // Current Subscription Status
                    if subscriptionManager.subscriptionStatus != .free {
                        currentSubscriptionCard
                    }
                    
                    // Billing Cycle Selector
                    billingCycleSelector
                    
                    // Subscription Plans
                    subscriptionPlans
                    
                    // Promo Code Section
                    promoCodeSection
                    
                    // Features Comparison
                    featuresComparison
                    
                    // Action Buttons
                    actionButtons
                }
                .padding()
            }
            .navigationTitle("Subscription")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Billing History") {
                            showingBillingHistory = true
                        }
                        
                        Button("Manage Subscription") {
                            showingManageSubscription = true
                        }
                        
                        Button("Restore Purchases") {
                            Task {
                                await subscriptionManager.restorePurchases()
                            }
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingPaymentSheet) {
                PaymentSheet(
                    selectedTier: selectedTier,
                    billingCycle: selectedBillingCycle
                )
            }
            .sheet(isPresented: $showingBillingHistory) {
                BillingHistoryView()
            }
            .sheet(isPresented: $showingManageSubscription) {
                SubscriptionManagementView()
            }
            .onAppear {
                Task {
                    await subscriptionManager.loadProducts()
                    await paymentManager.loadSubscriptionDetails()
                }
            }
        }
    }
    
    // MARK: - Header
    private var subscriptionHeader: some View {
        VStack(spacing: 16) {
            Image(systemName: "crown.fill")
                .font(.system(size: 60))
                .foregroundColor(.purple)
            
            Text("Unlock Your Financial Future")
                .font(.title)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text("Choose the plan that fits your investment journey")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - Current Subscription Card
    private var currentSubscriptionCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Current Plan")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text(subscriptionManager.subscriptionStatus.displayName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple.opacity(0.1))
                    )
            }
            
            if let details = paymentManager.subscriptionDetails,
               let subscription = details.currentSubscription {
                
                VStack(spacing: 8) {
                    HStack {
                        Text("Status")
                            .foregroundColor(.secondary)
                        Spacer()
                        HStack(spacing: 4) {
                            Circle()
                                .fill(Color(subscription.status.color))
                                .frame(width: 8, height: 8)
                            Text(subscription.status.displayName)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                    }
                    
                    if let nextBilling = details.nextBillingDate {
                        HStack {
                            Text("Next Billing")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(nextBilling, style: .date)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                    }
                    
                    if subscription.cancelAtPeriodEnd {
                        HStack {
                            Text("Expires")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(subscription.currentPeriodEnd, style: .date)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                        }
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Billing Cycle Selector
    private var billingCycleSelector: some View {
        VStack(spacing: 12) {
            Text("Billing Cycle")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 12) {
                ForEach(BillingCycle.allCases, id: \.self) { cycle in
                    Button(action: {
                        selectedBillingCycle = cycle
                    }) {
                        VStack(spacing: 4) {
                            Text(cycle.displayName)
                                .font(.subheadline)
                                .fontWeight(.semibold)
                            
                            if cycle.discountPercentage > 0 {
                                Text("\(cycle.discountPercentage)% OFF")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                            }
                        }
                        .foregroundColor(selectedBillingCycle == cycle ? .white : .primary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedBillingCycle == cycle ? Color.purple : Color(.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    // MARK: - Subscription Plans
    private var subscriptionPlans: some View {
        VStack(spacing: 16) {
            Text("Choose Your Plan")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                ForEach([SubscriptionTier.basic, SubscriptionTier.pro], id: \.self) { tier in
                    SubscriptionPlanCard(
                        tier: tier,
                        billingCycle: selectedBillingCycle,
                        isSelected: selectedTier == tier,
                        onSelect: {
                            selectedTier = tier
                        }
                    )
                }
            }
        }
    }
    
    // MARK: - Promo Code Section
    private var promoCodeSection: some View {
        VStack(spacing: 12) {
            Button(action: {
                showingPromoCodeField.toggle()
            }) {
                HStack {
                    Image(systemName: "tag.fill")
                        .foregroundColor(.green)
                    Text("Have a promo code?")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                    Image(systemName: showingPromoCodeField ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            if showingPromoCodeField {
                VStack(spacing: 12) {
                    HStack {
                        TextField("Enter promo code", text: $promoCode)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.allCharacters)
                        
                        Button("Apply") {
                            Task {
                                await paymentManager.validatePromoCode(promoCode)
                            }
                        }
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.green)
                        )
                        .disabled(promoCode.isEmpty)
                    }
                    
                    if let discount = paymentManager.appliedDiscount {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Promo code applied: \(discount.formattedValue)")
                                .font(.caption)
                                .foregroundColor(.green)
                            Spacer()
                            Button("Remove") {
                                paymentManager.removeDiscount()
                                promoCode = ""
                            }
                            .font(.caption)
                            .foregroundColor(.red)
                        }
                    }
                    
                    if let error = paymentManager.paymentError,
                       error == .invalidPromoCode {
                        HStack {
                            Image(systemName: "exclamationmark.circle.fill")
                                .foregroundColor(.red)
                            Text(error.localizedDescription)
                                .font(.caption)
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
            }
        }
    }
    
    // MARK: - Features Comparison
    private var featuresComparison: some View {
        VStack(spacing: 16) {
            Text("What's Included")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                FeatureComparisonRow(
                    feature: "AI-Powered Feed",
                    free: "3 posts/day",
                    basic: "Unlimited",
                    pro: "Unlimited + Priority"
                )
                
                FeatureComparisonRow(
                    feature: "Investment Quests",
                    free: "Basic only",
                    basic: "All quests",
                    pro: "All + Exclusive"
                )
                
                FeatureComparisonRow(
                    feature: "AI Chat Support",
                    free: "Limited",
                    basic: "Full access",
                    pro: "Priority support"
                )
                
                FeatureComparisonRow(
                    feature: "Investment Simulator",
                    free: "❌",
                    basic: "❌",
                    pro: "✅"
                )
                
                FeatureComparisonRow(
                    feature: "Real Trading",
                    free: "❌",
                    basic: "❌",
                    pro: "✅"
                )
                
                FeatureComparisonRow(
                    feature: "Advanced Analytics",
                    free: "❌",
                    basic: "❌",
                    pro: "✅"
                )
                
                FeatureComparisonRow(
                    feature: "Community Squads",
                    free: "❌",
                    basic: "❌",
                    pro: "✅"
                )
            }
        }
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        VStack(spacing: 16) {
            if subscriptionManager.subscriptionStatus == .free {
                Button(action: {
                    showingPaymentSheet = true
                }) {
                    HStack {
                        if paymentManager.isProcessingPayment {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        }
                        Text("Start \(selectedTier.displayName) Plan")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
                .disabled(paymentManager.isProcessingPayment)
            } else {
                HStack(spacing: 12) {
                    Button("Change Plan") {
                        showingPaymentSheet = true
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.purple, lineWidth: 1)
                    )
                    
                    Button("Manage") {
                        showingManageSubscription = true
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
            }
            
            Text("Cancel anytime. No hidden fees.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

#Preview {
    EnhancedSubscriptionView()
        .environmentObject(SubscriptionManager())
        .environmentObject(PaymentManager(subscriptionManager: SubscriptionManager()))
}
