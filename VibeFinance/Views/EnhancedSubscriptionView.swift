//
//  EnhancedSubscriptionView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import StoreKit

struct EnhancedSubscriptionView: View {
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var selectedBillingCycle: BillingCycle = .monthly
    @State private var selectedTier: SubscriptionTier = .pro
    @State private var showingPaymentSheet = false
    @State private var showingBillingHistory = false
    @State private var showingManageSubscription = false
    @State private var promoCode = ""
    @State private var showingPromoCodeField = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header with Gen Z appeal
                    genZSubscriptionHeader
                    
                    // Current Subscription Status
                    if subscriptionManager.subscriptionStatus != .free {
                        currentSubscriptionCard
                    }
                    
                    // Billing Cycle Selector
                    billingCycleSelector
                    
                    // Subscription Plans
                    subscriptionPlans
                    
                    // Promo Code Section
                    promoCodeSection
                    
                    // Features Comparison
                    featuresComparison
                    
                    // Action Buttons
                    actionButtons
                }
                .padding()
            }
            .navigationTitle("Subscription")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Billing History") {
                            showingBillingHistory = true
                        }
                        
                        Button("Manage Subscription") {
                            showingManageSubscription = true
                        }
                        
                        Button("Restore Purchases") {
                            Task {
                                await subscriptionManager.restorePurchases()
                            }
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingPaymentSheet) {
                PaymentSheet(
                    selectedTier: selectedTier,
                    billingCycle: selectedBillingCycle
                )
            }
            .sheet(isPresented: $showingBillingHistory) {
                BillingHistoryView()
            }
            .sheet(isPresented: $showingManageSubscription) {
                SubscriptionManagementView()
            }
            .onAppear {
                Task {
                    await subscriptionManager.loadProducts()
                    await paymentManager.loadSubscriptionDetails()
                }
            }
        }
    }
    
    // MARK: - Gen Z Header
    private var genZSubscriptionHeader: some View {
        VStack(spacing: 20) {
            // Animated crown with sparkles
            HStack(spacing: 8) {
                Text("✨")
                    .font(.title)
                    .scaleEffect(1.2)

                Image(systemName: "crown.fill")
                    .font(.system(size: 50))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.purple, .pink, .orange],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )

                Text("✨")
                    .font(.title)
                    .scaleEffect(1.2)
            }

            VStack(spacing: 12) {
                Text("Level Up Your Finance Game! 🚀")
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Text("Join the VIP squad and unlock exclusive features that'll make your friends jealous! 💅✨")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)

                // Social proof
                HStack(spacing: 4) {
                    ForEach(0..<5, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                    }
                    Text("10K+ users upgraded this month!")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [.purple.opacity(0.5), .pink.opacity(0.5)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
        )
    }
    
    // MARK: - Current Subscription Card
    private var currentSubscriptionCard: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Current Plan")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text(subscriptionManager.subscriptionStatus.displayName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple.opacity(0.1))
                    )
            }
            
            if let details = paymentManager.subscriptionDetails,
               let subscription = details.currentSubscription {
                
                VStack(spacing: 8) {
                    HStack {
                        Text("Status")
                            .foregroundColor(.secondary)
                        Spacer()
                        HStack(spacing: 4) {
                            Circle()
                                .fill(Color(subscription.status.color))
                                .frame(width: 8, height: 8)
                            Text(subscription.status.displayName)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                    }
                    
                    if let nextBilling = details.nextBillingDate {
                        HStack {
                            Text("Next Billing")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(nextBilling, style: .date)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                    }
                    
                    if subscription.cancelAtPeriodEnd {
                        HStack {
                            Text("Expires")
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(subscription.currentPeriodEnd, style: .date)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                        }
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    // MARK: - Billing Cycle Selector
    private var billingCycleSelector: some View {
        VStack(spacing: 12) {
            Text("Billing Cycle")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 12) {
                ForEach(BillingCycle.allCases, id: \.self) { cycle in
                    Button(action: {
                        selectedBillingCycle = cycle
                    }) {
                        VStack(spacing: 4) {
                            Text(cycle.displayName)
                                .font(.subheadline)
                                .fontWeight(.semibold)
                            
                            if cycle.discountPercentage > 0 {
                                Text("\(cycle.discountPercentage)% OFF")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                            }
                        }
                        .foregroundColor(selectedBillingCycle == cycle ? .white : .primary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedBillingCycle == cycle ? Color.purple : Color(.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    // MARK: - Gen Z Subscription Plans
    private var subscriptionPlans: some View {
        VStack(spacing: 20) {
            HStack {
                Text("Pick Your Vibe! ✨")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )

                Spacer()

                Text("Most Popular 🔥")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.orange)
                    )
            }

            VStack(spacing: 16) {
                // Free tier showcase
                GenZPlanCard(
                    tier: .free,
                    billingCycle: selectedBillingCycle,
                    isSelected: false,
                    onSelect: {},
                    isCurrentTier: subscriptionManager.subscriptionStatus == .free
                )

                ForEach([SubscriptionTier.basic, SubscriptionTier.pro], id: \.self) { tier in
                    GenZPlanCard(
                        tier: tier,
                        billingCycle: selectedBillingCycle,
                        isSelected: selectedTier == tier,
                        onSelect: {
                            selectedTier = tier
                        },
                        isCurrentTier: subscriptionManager.subscriptionStatus == tier
                    )
                }
            }
        }
    }
    
    // MARK: - Promo Code Section
    private var promoCodeSection: some View {
        VStack(spacing: 12) {
            Button(action: {
                showingPromoCodeField.toggle()
            }) {
                HStack {
                    Image(systemName: "tag.fill")
                        .foregroundColor(.green)
                    Text("Have a promo code?")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Spacer()
                    Image(systemName: showingPromoCodeField ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            if showingPromoCodeField {
                VStack(spacing: 12) {
                    HStack {
                        TextField("Enter promo code", text: $promoCode)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.allCharacters)
                        
                        Button("Apply") {
                            Task {
                                await paymentManager.validatePromoCode(promoCode)
                            }
                        }
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.green)
                        )
                        .disabled(promoCode.isEmpty)
                    }
                    
                    if let discount = paymentManager.appliedDiscount {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Promo code applied: \(discount.formattedValue)")
                                .font(.caption)
                                .foregroundColor(.green)
                            Spacer()
                            Button("Remove") {
                                paymentManager.removeDiscount()
                                promoCode = ""
                            }
                            .font(.caption)
                            .foregroundColor(.red)
                        }
                    }
                    
                    if let error = paymentManager.paymentError,
                       error == .invalidPromoCode {
                        HStack {
                            Image(systemName: "exclamationmark.circle.fill")
                                .foregroundColor(.red)
                            Text(error.localizedDescription)
                                .font(.caption)
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
            }
        }
    }
    
    // MARK: - Gen Z Features Comparison
    private var featuresComparison: some View {
        VStack(spacing: 20) {
            HStack {
                Text("What You Get! 💅")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.purple, .pink],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                Spacer()
            }

            VStack(spacing: 12) {
                GenZFeatureRow(
                    feature: "📱 TikTok-Style Feed",
                    free: "3 posts/day",
                    basic: "Unlimited vibes",
                    pro: "VIP access + early content"
                )

                GenZFeatureRow(
                    feature: "🎯 Finance Quests",
                    free: "Basic challenges",
                    basic: "All quests unlocked",
                    pro: "Exclusive challenges + rewards"
                )

                GenZFeatureRow(
                    feature: "🤖 AI Finance Buddy",
                    free: "Limited chats",
                    basic: "Unlimited bestie mode",
                    pro: "Priority + advanced insights"
                )

                GenZFeatureRow(
                    feature: "🎮 Investment Simulator",
                    free: "❌",
                    basic: "❌",
                    pro: "✅ Full access"
                )

                GenZFeatureRow(
                    feature: "📈 Real Trading",
                    free: "❌",
                    basic: "❌",
                    pro: "✅ Make real money moves"
                )

                GenZFeatureRow(
                    feature: "📊 Advanced Analytics",
                    free: "❌",
                    basic: "❌",
                    pro: "✅ Pro trader insights"
                )

                GenZFeatureRow(
                    feature: "👥 Squad Leadership",
                    free: "❌",
                    basic: "❌",
                    pro: "✅ Lead your own squad"
                )

                GenZFeatureRow(
                    feature: "🎨 Custom Themes",
                    free: "❌",
                    basic: "✅ 5 themes",
                    pro: "✅ All themes + exclusive"
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        VStack(spacing: 16) {
            if subscriptionManager.subscriptionStatus == .free {
                Button(action: {
                    showingPaymentSheet = true
                }) {
                    HStack {
                        if paymentManager.isProcessingPayment {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        }
                        Text("Start \(selectedTier.displayName) Plan")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
                .disabled(paymentManager.isProcessingPayment)
            } else {
                HStack(spacing: 12) {
                    Button("Change Plan") {
                        showingPaymentSheet = true
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.purple, lineWidth: 1)
                    )
                    
                    Button("Manage") {
                        showingManageSubscription = true
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
            }
            
            Text("Cancel anytime. No hidden fees.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }
}

// MARK: - Gen Z Plan Card
struct GenZPlanCard: View {
    let tier: SubscriptionTier
    let billingCycle: BillingCycle
    let isSelected: Bool
    let onSelect: () -> Void
    let isCurrentTier: Bool

    private var price: String {
        switch tier {
        case .free: return "Free"
        case .basic: return "$9.99"
        case .pro: return "$19.99"
        }
    }

    private var tierEmoji: String {
        switch tier {
        case .free: return "🌱"
        case .basic: return "⭐"
        case .pro: return "👑"
        }
    }

    private var tierDescription: String {
        switch tier {
        case .free: return "Perfect for getting started bestie! 💅"
        case .basic: return "For the finance girlies who want more! ✨"
        case .pro: return "Ultimate main character energy! 🔥"
        }
    }

    private var features: [String] {
        switch tier {
        case .free:
            return [
                "3 daily feed posts",
                "Basic AI chat",
                "Community access",
                "Basic quests"
            ]
        case .basic:
            return [
                "Unlimited feed access 📱",
                "Full AI chat support 🤖",
                "All quests unlocked 🎯",
                "Priority customer support 💬",
                "Custom themes 🎨"
            ]
        case .pro:
            return [
                "Everything in Basic ⭐",
                "Real trading access 📈",
                "Advanced analytics 📊",
                "Squad leadership 👥",
                "Investment simulator 🎮",
                "Exclusive content 💎",
                "Early feature access 🚀"
            ]
        }
    }

    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(tierEmoji)
                            .font(.title2)
                        Text(tier.displayName)
                            .font(.title2)
                            .fontWeight(.bold)

                        if tier == .pro {
                            Text("POPULAR")
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(.orange)
                                )
                        }
                    }

                    Text(tierDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing) {
                    Text(price)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(tier == .free ? .green : .primary)

                    if tier != .free {
                        Text("/month")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Features
            VStack(alignment: .leading, spacing: 8) {
                ForEach(features, id: \.self) { feature in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(tier == .pro ? .orange : .green)
                            .font(.system(size: 16))
                        Text(feature)
                            .font(.subheadline)
                        Spacer()
                    }
                }
            }

            // Action button
            if tier != .free {
                Button(action: onSelect) {
                    Text(isCurrentTier ? "Current Plan ✨" : "Choose \(tier.displayName) 🚀")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            LinearGradient(
                                colors: isCurrentTier ? [.gray, .gray] :
                                        tier == .pro ? [.purple, .pink] : [.blue, .cyan],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(12)
                }
                .disabled(isCurrentTier)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            isSelected || isCurrentTier ?
                            (tier == .pro ? .orange : .blue) : .clear,
                            lineWidth: 2
                        )
                )
        )
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3), value: isSelected)
    }
}

// MARK: - Gen Z Feature Row
struct GenZFeatureRow: View {
    let feature: String
    let free: String
    let basic: String
    let pro: String

    var body: some View {
        HStack(spacing: 12) {
            // Feature name
            Text(feature)
                .font(.subheadline)
                .fontWeight(.medium)
                .frame(maxWidth: .infinity, alignment: .leading)
                .frame(minWidth: 120)

            // Free tier
            Text(free)
                .font(.caption)
                .foregroundColor(free.contains("❌") ? .red : .secondary)
                .frame(width: 80)
                .multilineTextAlignment(.center)

            // Basic tier
            Text(basic)
                .font(.caption)
                .foregroundColor(basic.contains("❌") ? .red : .blue)
                .frame(width: 80)
                .multilineTextAlignment(.center)

            // Pro tier
            Text(pro)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(pro.contains("❌") ? .red : .orange)
                .frame(width: 100)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(.ultraThinMaterial)
        )
    }
}

#Preview {
    EnhancedSubscriptionView()
        .environmentObject(SubscriptionManager())
        .environmentObject(PaymentManager(subscriptionManager: SubscriptionManager()))
}
