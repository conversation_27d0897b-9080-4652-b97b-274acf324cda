//
//  ThemeCustomizationView.swift
//  VibeFinance - Theme & Customization Main View
//
//  Created by MAGESH DHANASEKARAN on 7/9/25.
//

import SwiftUI

struct ThemeCustomizationView: View {
    @StateObject private var customizationManager = CustomizationManager()
    @State private var selectedTab: CustomizationTab = .themes
    @State private var selectedCategory: ThemeCategory? = nil
    @State private var showingPreview = false
    @State private var previewTheme: UnlockableAppTheme?
    @Environment(\.theme) var theme
    
    enum CustomizationTab: String, CaseIterable {
        case themes = "Themes"
        case avatar = "Avatar"
        case effects = "Effects"
        
        var icon: String {
            switch self {
            case .themes: return "paintbrush.fill"
            case .avatar: return "person.circle.fill"
            case .effects: return "sparkles"
            }
        }
    }
    
    var filteredThemes: [UnlockableAppTheme] {
        var themes = customizationManager.availableThemes
        
        if let category = selectedCategory {
            themes = themes.filter { $0.category == category }
        }
        
        return themes.sorted { first, second in
            if first.isUnlocked != second.isUnlocked {
                return first.isUnlocked && !second.isUnlocked
            }
            return first.rarity.rawValue > second.rarity.rawValue
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Custom Tab Bar
                CustomTabBar(selectedTab: $selectedTab)
                    .padding(.horizontal)
                    .padding(.top, 8)
                
                ScrollView {
                    LazyVStack(spacing: 20) {
                        // User Profile Header
                        if let profile = customizationManager.userProfile {
                            CustomizationProfileHeader(profile: profile)
                                .padding(.horizontal)
                        }
                        
                        // Featured Showcase
                        if let showcase = customizationManager.featuredShowcase {
                            FeaturedThemeShowcase(showcase: showcase) { theme in
                                previewTheme = theme
                                showingPreview = true
                            }
                            .padding(.horizontal)
                        }
                        
                        // Tab Content
                        switch selectedTab {
                        case .themes:
                            ThemesTabContent(
                                themes: filteredThemes,
                                selectedCategory: $selectedCategory,
                                activeTheme: customizationManager.activeTheme,
                                onThemeSelect: { theme in
                                    Task {
                                        if theme.isUnlocked {
                                            await customizationManager.activateTheme(theme.id)
                                        } else {
                                            previewTheme = theme
                                            showingPreview = true
                                        }
                                    }
                                },
                                onThemePreview: { theme in
                                    previewTheme = theme
                                    showingPreview = true
                                }
                            )
                            
                        case .avatar:
                            AvatarTabContent(
                                avatarItems: customizationManager.avatarItems,
                                userProfile: customizationManager.userProfile,
                                onItemSelect: { category, itemID in
                                    Task {
                                        await customizationManager.updateAvatarCustomization(category: category, itemID: itemID)
                                    }
                                }
                            )
                            
                        case .effects:
                            EffectsTabContent(
                                themes: customizationManager.getUnlockedThemes().filter { $0.effects != nil }
                            )
                        }
                    }
                    .padding(.bottom, 100)
                }
            }
            .background(theme.background)
            .navigationTitle("🎨 Customization")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingPreview) {
                if let theme = previewTheme {
                    ThemePreviewSheet(
                        theme: theme,
                        onUnlock: {
                            Task {
                                let unlocked = await customizationManager.unlockTheme(theme.id, for: UUID())
                                if unlocked {
                                    showingPreview = false
                                }
                            }
                        },
                        onActivate: {
                            Task {
                                await customizationManager.activateTheme(theme.id)
                                showingPreview = false
                            }
                        }
                    )
                }
            }
            .task {
                await customizationManager.loadUserCustomizations(for: UUID())
            }
        }
    }
}

// MARK: - Custom Tab Bar

struct CustomTabBar: View {
    @Binding var selectedTab: ThemeCustomizationView.CustomizationTab
    @Environment(\.theme) var theme
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(ThemeCustomizationView.CustomizationTab.allCases, id: \.self) { tab in
                Button(action: { selectedTab = tab }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.icon)
                            .font(.title3)
                            .foregroundColor(selectedTab == tab ? theme.primary : theme.onSurface.opacity(0.6))
                        
                        Text(tab.rawValue)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(selectedTab == tab ? theme.primary : theme.onSurface.opacity(0.6))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedTab == tab ? theme.primary.opacity(0.1) : Color.clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(theme.onSurface.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

// MARK: - Profile Header

struct CustomizationProfileHeader: View {
    let profile: UserCustomizationProfile
    @Environment(\.theme) var theme
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                // Avatar Preview
                ZStack {
                    Circle()
                        .fill(LinearGradient(
                            colors: [theme.primary.opacity(0.3), theme.secondary.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "person.fill")
                        .font(.title)
                        .foregroundColor(theme.primary)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Customization Level \(profile.customizationLevel)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)
                    
                    Text("\(profile.totalCustomizationsUnlocked) items unlocked")
                        .font(.subheadline)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                    
                    // Progress Bar
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Collection Progress")
                                .font(.caption)
                                .foregroundColor(theme.onSurface.opacity(0.7))
                            Spacer()
                            Text("\(Int(profile.customizationProgress * 100))%")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(theme.primary)
                        }
                        
                        ProgressView(value: profile.customizationProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: theme.primary))
                            .scaleEffect(y: 0.8)
                    }
                }
                
                Spacer()
            }
            
            // Quick Stats
            HStack(spacing: 20) {
                StatItem(title: "Themes", value: "\(profile.unlockedThemes.count)")
                StatItem(title: "Favorites", value: "\(profile.favoriteThemes.count)")
                StatItem(title: "Avatar Items", value: "\(profile.unlockedAvatarItems.count)")
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.surface)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(theme.onSurface.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

struct StatItem: View {
    let title: String
    let value: String
    @Environment(\.theme) var theme
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(theme.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(theme.onSurface.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    ThemeCustomizationView()
        .preferredColorScheme(.dark)
}
