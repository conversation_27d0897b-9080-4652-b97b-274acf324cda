//
//  ThemeCustomizationView.swift
//  VibeFinance - Advanced Theme Customization System
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Theme Models
struct AppTheme: Identifiable, Codable {
    let id: String
    let name: String
    let description: String
    let colors: ThemeColors
    let gradient: [Color]
    let icon: String
    var isUnlocked: Bool
    let unlockCondition: String
    let rarity: ThemeRarity
    let price: Int?

    enum ThemeRarity: String, CaseIterable, Codable {
        case common = "Common"
        case rare = "Rare"
        case epic = "Epic"
        case legendary = "Legendary"

        var color: Color {
            switch self {
            case .common: return .gray
            case .rare: return .blue
            case .epic: return .purple
            case .legendary: return .yellow
            }
        }
    }
}

struct ThemeColors: Codable {
    let primary: String
    let secondary: String
    let accent: String
    let background: String
    let foreground: String

    var primaryColor: Color { Color(hex: primary) }
    var secondaryColor: Color { Color(hex: secondary) }
    var accentColor: Color { Color(hex: accent) }
    var backgroundColor: Color { Color(hex: background) }
    var foregroundColor: Color { Color(hex: foreground) }
}

// MARK: - Theme Customization View
struct ThemeCustomizationView: View {
    @StateObject private var themeManager = ThemeManager()
    @EnvironmentObject var userManager: UserManager
    @State private var selectedTheme: AppTheme?
    @State private var showUnlockAnimation = false
    @State private var animatingTheme: AppTheme?
    @State private var userCoins = 1500

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection

                    // Stats Cards
                    statsSection

                    // Current Theme Preview
                    currentThemePreview

                    // Available Themes Grid
                    themesGrid
                }
                .padding(.horizontal, 20)
                .padding(.top, 10)
            }
            .background(
                LinearGradient(
                    colors: [Color.black.opacity(0.9), Color.purple.opacity(0.3)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
            .navigationTitle("Themes")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
        }
        .overlay(
            // Unlock Animation Overlay
            Group {
                if showUnlockAnimation, let theme = animatingTheme {
                    UnlockAnimationView(theme: theme) {
                        withAnimation {
                            showUnlockAnimation = false
                            animatingTheme = nil
                        }
                    }
                }
            }
        )
        .onAppear {
            selectedTheme = themeManager.currentTheme
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 12) {
            Text("Theme Customization")
                .font(.system(size: 32, weight: .bold, design: .rounded))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.white, .purple.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )

            Text("Personalize your finance app with exclusive themes ✨")
                .font(.headline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .padding(.top, 20)
    }

    // MARK: - Stats Section
    private var statsSection: some View {
        HStack(spacing: 16) {
            StatCard(
                icon: "paintpalette.fill",
                title: "Themes Unlocked",
                value: "\(themeManager.unlockedCount)/\(themeManager.totalThemes)",
                color: .purple,
                progress: Double(themeManager.unlockedCount) / Double(themeManager.totalThemes)
            )

            StatCard(
                icon: "star.fill",
                title: "Coins",
                value: "\(userCoins)",
                color: .yellow
            )

            StatCard(
                icon: "crown.fill",
                title: "Level",
                value: "\(userManager.user?.level ?? 1)",
                color: .green
            )
        }
    }

    // MARK: - Current Theme Preview
    private var currentThemePreview: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Current Theme Preview")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            if let theme = selectedTheme {
                ThemePreviewCard(theme: theme, isLarge: true)
            }
        }
    }

    // MARK: - Themes Grid
    private var themesGrid: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Available Themes")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(themeManager.availableThemes) { theme in
                    ThemeCard(
                        theme: theme,
                        isSelected: selectedTheme?.id == theme.id,
                        userCoins: userCoins
                    ) { action in
                        handleThemeAction(theme: theme, action: action)
                    }
                }
            }
        }
    }

    // MARK: - Actions
    private func handleThemeAction(theme: AppTheme, action: ThemeAction) {
        switch action {
        case .select:
            if theme.isUnlocked {
                withAnimation(.spring()) {
                    selectedTheme = theme
                    themeManager.setCurrentTheme(theme)
                }
            }
        case .unlock:
            if let price = theme.price, userCoins >= price {
                withAnimation {
                    userCoins -= price
                    themeManager.unlockTheme(theme.id)
                    animatingTheme = theme
                    showUnlockAnimation = true
                }
            }
        }
    }
}

// MARK: - Supporting Views
struct StatCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    var progress: Double?

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))

            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)

            if let progress = progress {
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: color))
                    .scaleEffect(y: 0.5)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
    }
}

struct ThemeCard: View {
    let theme: AppTheme
    let isSelected: Bool
    let userCoins: Int
    let onAction: (ThemeAction) -> Void

    var body: some View {
        VStack(spacing: 0) {
            // Theme Preview
            ZStack {
                LinearGradient(
                    colors: theme.gradient,
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .frame(height: 120)

                // Mock UI Preview
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("$12,345.67")
                            .font(.headline)
                            .fontWeight(.bold)
                        Spacer()
                        Image(systemName: theme.icon)
                            .font(.title2)
                    }
                    Text("Total Balance")
                        .font(.caption)
                        .opacity(0.8)
                }
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)

                // Lock Overlay
                if !theme.isUnlocked {
                    Color.black.opacity(0.5)
                    Image(systemName: "lock.fill")
                        .font(.title)
                        .foregroundColor(.white)
                }

                // Rarity Badge
                VStack {
                    HStack {
                        Spacer()
                        Text(theme.rarity.rawValue)
                            .font(.caption2)
                            .fontWeight(.semibold)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(theme.rarity.color.opacity(0.8))
                            .foregroundColor(.white)
                            .clipShape(Capsule())
                    }
                    Spacer()
                }
                .padding(8)
            }
            .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))

            // Theme Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(theme.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Spacer()

                    if isSelected {
                        Text("Active")
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.green)
                            .foregroundColor(.white)
                            .clipShape(Capsule())
                    }
                }

                Text(theme.description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .lineLimit(2)

                Text(theme.unlockCondition)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.5))

                // Action Button
                Button(action: {
                    if theme.isUnlocked {
                        onAction(.select)
                    } else {
                        onAction(.unlock)
                    }
                }) {
                    HStack {
                        if !theme.isUnlocked {
                            Image(systemName: "lock.fill")
                                .font(.caption)
                        }

                        Text(theme.isUnlocked ? (isSelected ? "Selected" : "Select") : "Unlock (\(theme.price ?? 0) coins)")
                            .font(.caption)
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        theme.isUnlocked ?
                        (isSelected ? Color.gray.opacity(0.3) : Color.white.opacity(0.2)) :
                        (userCoins >= (theme.price ?? 0) ? Color.blue.opacity(0.8) : Color.gray.opacity(0.3))
                    )
                    .foregroundColor(.white)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                }
                .disabled(!theme.isUnlocked && userCoins < (theme.price ?? 0))
            }
            .padding()
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isSelected ? Color.white.opacity(0.5) : Color.white.opacity(0.2), lineWidth: isSelected ? 2 : 1)
                )
        )
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .animation(.spring(response: 0.3), value: isSelected)
    }
}

enum ThemeAction {
    case select
    case unlock
}
struct ThemePreviewCard: View {
    let theme: AppTheme
    let isLarge: Bool

    var body: some View {
        ZStack {
            LinearGradient(
                colors: theme.gradient,
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .frame(height: isLarge ? 200 : 120)

            // Glass Time Card Preview
            VStack(spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Friday | July 10")
                            .font(.caption)
                            .opacity(0.8)
                        Text("14:30")
                            .font(.system(size: isLarge ? 32 : 24, weight: .bold, design: .monospaced))
                        Text("GMT+10")
                            .font(.caption2)
                            .opacity(0.6)
                    }
                    Spacer()
                    Image(systemName: theme.icon)
                        .font(.title)
                }
                .foregroundColor(.white)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.2))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
                .background(.ultraThinMaterial)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .padding()
        }
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.white.opacity(0.3), lineWidth: 1)
        )
    }
}

struct UnlockAnimationView: View {
    let theme: AppTheme
    let onComplete: () -> Void

    @State private var animationPhase = 0

    var body: some View {
        ZStack {
            Color.black.opacity(0.7)
                .ignoresSafeArea()
                .onTapGesture {
                    onComplete()
                }

            VStack(spacing: 24) {
                // Animated unlock icon
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.yellow, .orange],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .scaleEffect(animationPhase == 0 ? 0.5 : 1.0)
                        .rotationEffect(.degrees(animationPhase == 0 ? 0 : 360))

                    Image(systemName: "lock.open.fill")
                        .font(.title)
                        .foregroundColor(.white)
                        .scaleEffect(animationPhase == 0 ? 0.5 : 1.0)
                }

                VStack(spacing: 12) {
                    Text("Theme Unlocked! 🎉")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("You've unlocked \(theme.name)")
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.8))

                    // Sparkle animation
                    HStack(spacing: 8) {
                        ForEach(0..<5, id: \.self) { index in
                            Image(systemName: "sparkles")
                                .font(.caption)
                                .foregroundColor(.yellow)
                                .opacity(animationPhase >= index ? 1.0 : 0.0)
                                .scaleEffect(animationPhase >= index ? 1.0 : 0.5)
                                .animation(
                                    .spring(response: 0.5, dampingFraction: 0.8)
                                    .delay(Double(index) * 0.1),
                                    value: animationPhase
                                )
                        }
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.black.opacity(0.8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
            .scaleEffect(animationPhase == 0 ? 0.8 : 1.0)
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                animationPhase = 5
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                onComplete()
            }
        }
    }
}

// MARK: - Color Extension
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

#Preview {
    ThemeCustomizationView()
        .environmentObject(UserManager())
        .preferredColorScheme(.dark)
}
