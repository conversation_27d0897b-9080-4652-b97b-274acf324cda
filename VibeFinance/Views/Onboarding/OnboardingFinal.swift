//
//  OnboardingFinal.swift
//  WealthVibe - Final Onboarding Step & Navigation
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Final Setup Step
struct FinalSetupStep: View {
    @Binding var profile: GenZUserProfile
    @Binding var showAuth: Bool
    @State private var showRewards = false
    @State private var showFeatures = false
    @State private var animateRewards = false
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Celebration Header
            VStack(spacing: 16) {
                ZStack {
                    // Celebration particles
                    if animateRewards {
                        ForEach(0..<8, id: \.self) { index in
                            Text(["🎉", "✨", "🎊", "💎", "🚀", "⭐", "🎯", "💰"].randomElement() ?? "✨")
                                .font(.title2)
                                .offset(
                                    x: cos(Double(index) * .pi / 4) * 60,
                                    y: sin(Double(index) * .pi / 4) * 60
                                )
                                .opacity(animateRewards ? 1 : 0)
                                .scaleEffect(animateRewards ? 1.2 : 0.5)
                                .animation(
                                    .easeOut(duration: 1.0).delay(Double(index) * 0.1),
                                    value: animateRewards
                                )
                        }
                    }
                    
                    Text("🎉")
                        .font(.system(size: 80))
                        .scaleEffect(animateRewards ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: animateRewards)
                }
                .frame(height: 120)
                
                Text("You're All Set!")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("Welcome to your personalized WealthVibe experience")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            
            if showRewards {
                // Starter Rewards
                VStack(spacing: 16) {
                    Text("🎁 Starter Pack")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    VStack(spacing: 12) {
                        OnboardingRewardCard(
                            icon: "💰",
                            title: "$10,000 Virtual Money",
                            description: "Practice trading risk-free"
                        )

                        OnboardingRewardCard(
                            icon: "⭐",
                            title: "100 XP Bonus",
                            description: "Head start on your journey"
                        )

                        OnboardingRewardCard(
                            icon: "🎯",
                            title: "First Quest Unlocked",
                            description: "Start earning rewards today"
                        )

                        if profile.socialPreferences.joinSquads {
                            OnboardingRewardCard(
                                icon: "👥",
                                title: "Squad Invite Ready",
                                description: "Join your first investment squad"
                            )
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
            
            if showFeatures {
                // Personalized Features Preview
                VStack(spacing: 12) {
                    Text("✨ Personalized Just for You")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    VStack(spacing: 8) {
                        PersonalizedFeature(
                            text: "📈 \(profile.investmentStyle.rawValue) investment recommendations"
                        )
                        
                        PersonalizedFeature(
                            text: "🎯 Quests focused on \(profile.financialGoals.first?.rawValue.dropFirst(2) ?? "your goals")"
                        )
                        
                        PersonalizedFeature(
                            text: "🧠 \(profile.personalityTraits.first?.rawValue.dropFirst(2) ?? "Personalized") learning style content"
                        )
                        
                        if profile.socialPreferences.competitiveMode {
                            PersonalizedFeature(
                                text: "🏆 Competitive challenges and leaderboards"
                            )
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
            
            Spacer()
            
            // Get Started Button
            Button(action: {
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    showAuth = true
                }
            }) {
                HStack {
                    Text("Start My Journey")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Image(systemName: "arrow.right")
                        .font(.headline)
                }
                .foregroundColor(.black)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    LinearGradient(
                        colors: [.white, .white.opacity(0.9)],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .shadow(color: .white.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .padding(.horizontal, 24)
            .scaleEffect(animateRewards ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: animateRewards)
            
            Spacer()
        }
        .onAppear {
            // Staggered animations
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 0.8)) {
                    animateRewards = true
                }
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    showRewards = true
                }
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    showFeatures = true
                }
            }
        }
    }
}

// MARK: - Onboarding Reward Card
struct OnboardingRewardCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Text(icon)
                .font(.title2)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            Image(systemName: "checkmark.circle.fill")
                .font(.title3)
                .foregroundColor(.green)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Personalized Feature
struct PersonalizedFeature: View {
    let text: String
    
    var body: some View {
        HStack {
            Text(text)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
}



#Preview {
    FinalSetupStep(
        profile: .constant(GenZUserProfile()),
        showAuth: .constant(false)
    )
    .background(
        LinearGradient(
            colors: [.pink, .purple, .blue],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
}
