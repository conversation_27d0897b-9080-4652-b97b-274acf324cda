//
//  GenZOnboardingView.swift
//  WealthVibe - Enhanced Gen Z/Gen Alpha Onboarding Experience
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

struct GenZOnboardingView: View {
    @EnvironmentObject var authManager: AuthManager
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var onboardingManager: OnboardingManager

    @State private var currentStep = 0
    @State private var showAuth = false
    @State private var userProfile = GenZUserProfile()
    @State private var isAnimating = false
    
    private let totalSteps = 7
    
    var body: some View {
        ZStack {
            // Dynamic gradient background
            AnimatedGradientBackground(step: currentStep)
                .ignoresSafeArea()
            
            if showAuth {
                AuthView()
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
            } else {
                VStack(spacing: 0) {
                    // Progress Header with Vibe Meter
                    VibeProgressHeader(
                        currentStep: currentStep,
                        totalSteps: totalSteps,
                        isAnimating: $isAnimating
                    )
                    
                    // Main Content
                    TabView(selection: $currentStep) {
                        // Step 0: Welcome & Vibe Check
                        WelcomeVibeStep()
                            .tag(0)
                        
                        // Step 1: Personality Quiz
                        PersonalityQuizStep(profile: $userProfile)
                            .tag(1)
                        
                        // Step 2: Investment Style Assessment
                        InvestmentStyleStep(profile: $userProfile)
                            .tag(2)
                        
                        // Step 3: Goals & Dreams
                        GoalsAndDreamsStep(profile: $userProfile)
                            .tag(3)
                        
                        // Step 4: Risk Tolerance (Gamified)
                        RiskToleranceGameStep(profile: $userProfile)
                            .tag(4)
                        
                        // Step 5: Social Preferences
                        SocialPreferencesStep(profile: $userProfile)
                            .tag(5)
                        
                        // Step 6: Final Setup & Rewards
                        FinalSetupStep(profile: $userProfile, showAuth: $showAuth)
                            .tag(6)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: currentStep)
                    
                    // Navigation Controls
                    OnboardingNavigationControls(
                        currentStep: $currentStep,
                        totalSteps: totalSteps,
                        canProceed: canProceedToNextStep,
                        onNext: nextStep,
                        onBack: previousStep,
                        onComplete: completeOnboarding
                    )
                }
            }
        }
        .onAppear {
            startAnimations()
        }
        .animation(.easeInOut(duration: 0.8), value: showAuth)
    }
    
    private var canProceedToNextStep: Bool {
        switch currentStep {
        case 0: return true // Welcome step
        case 1: return !userProfile.personalityTraits.isEmpty
        case 2: return userProfile.investmentStyle != .unknown
        case 3: return !userProfile.financialGoals.isEmpty
        case 4: return userProfile.riskTolerance != .unknown
        case 5: return true // Social preferences are optional
        case 6: return true // Final step
        default: return false
        }
    }
    
    private func nextStep() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            if currentStep < totalSteps - 1 {
                currentStep += 1
                triggerStepAnimation()
            }
        }
    }
    
    private func previousStep() {
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            if currentStep > 0 {
                currentStep -= 1
                triggerStepAnimation()
            }
        }
    }
    
    private func startAnimations() {
        withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
            isAnimating = true
        }
    }
    
    private func triggerStepAnimation() {
        // Trigger haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // Reset and restart animations for new step
        isAnimating = false
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                isAnimating = true
            }
        }
    }

    private func completeOnboarding() async {
        // Save the user profile data to Supabase
        await onboardingManager.saveUserProfile(userProfile)

        // Complete the onboarding process (grants rewards, sets up personalization)
        await onboardingManager.completeOnboarding()

        // Navigate to main app
        await MainActor.run {
            showAuth = false
        }
    }
}

// MARK: - User Profile Model
struct GenZUserProfile {
    var personalityTraits: Set<PersonalityTrait> = []
    var investmentStyle: InvestmentStyle = .unknown
    var financialGoals: Set<FinancialGoal> = []
    var riskTolerance: OnboardingRiskLevel = .unknown
    var socialPreferences: SocialPreferences = SocialPreferences()
    var preferredLearningStyle: LearningStyle = .visual
    var communicationStyle: CommunicationStyle = .casual
}

enum PersonalityTrait: String, CaseIterable {
    case adventurous = "🚀 Adventurous"
    case analytical = "🧠 Analytical" 
    case creative = "🎨 Creative"
    case social = "👥 Social"
    case cautious = "🛡️ Cautious"
    case ambitious = "⭐ Ambitious"
    case techSavvy = "💻 Tech-Savvy"
    case trendy = "✨ Trendy"
}

enum InvestmentStyle: String, CaseIterable {
    case unknown = ""
    case conservative = "🐢 Steady & Safe"
    case balanced = "⚖️ Balanced Approach"
    case aggressive = "🚀 High Risk, High Reward"
    case trendy = "📱 Follow the Trends"
    case ethical = "🌱 Sustainable & Ethical"
    case tech = "💻 Tech & Innovation"
}

enum FinancialGoal: String, CaseIterable {
    case emergencyFund = "🚨 Emergency Fund"
    case houseDown = "🏠 House Down Payment"
    case travel = "✈️ Travel Fund"
    case retirement = "🏖️ Early Retirement"
    case business = "💼 Start a Business"
    case education = "🎓 Education Fund"
    case car = "🚗 Dream Car"
    case freedom = "💰 Financial Freedom"
}

enum OnboardingRiskLevel: String, CaseIterable {
    case unknown = ""
    case conservative = "🐌 Play it Safe"
    case moderate = "🎯 Balanced Risk"
    case aggressive = "🚀 High Risk, High Reward"
    case yolo = "💎 Diamond Hands"
}

enum LearningStyle: String, CaseIterable {
    case visual = "👀 Visual Learner"
    case interactive = "🎮 Hands-On"
    case social = "👥 Learn with Others"
    case bite_sized = "🍿 Bite-Sized Content"
}

enum CommunicationStyle: String, CaseIterable {
    case casual = "😎 Keep it Casual"
    case professional = "💼 Professional"
    case funny = "😂 Make me Laugh"
    case motivational = "💪 Pump me Up"
}

struct SocialPreferences {
    var shareAchievements: Bool = true
    var joinSquads: Bool = true
    var publicProfile: Bool = false
    var mentorshipInterest: Bool = false
    var competitiveMode: Bool = true
}

// MARK: - Animated Background
struct AnimatedGradientBackground: View {
    let step: Int
    
    private var gradientColors: [Color] {
        switch step {
        case 0: return [.purple, .pink, .orange]
        case 1: return [.blue, .purple, .pink]
        case 2: return [.green, .blue, .teal]
        case 3: return [.orange, .red, .pink]
        case 4: return [.red, .orange, .yellow]
        case 5: return [.teal, .blue, .purple]
        case 6: return [.pink, .purple, .blue]
        default: return [.purple, .pink, .orange]
        }
    }
    
    var body: some View {
        LinearGradient(
            colors: gradientColors.map { $0.opacity(0.8) },
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .animation(.easeInOut(duration: 1.0), value: step)
    }
}

// MARK: - Progress Header
struct VibeProgressHeader: View {
    let currentStep: Int
    let totalSteps: Int
    @Binding var isAnimating: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            // Vibe Meter
            HStack {
                Text("VIBE METER")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                Text("\(currentStep + 1)/\(totalSteps)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            // Progress Bar with Glow Effect
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 8)
                    
                    // Progress
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [.white, .yellow, .white],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(
                            width: geometry.size.width * CGFloat(currentStep + 1) / CGFloat(totalSteps),
                            height: 8
                        )
                        .shadow(color: .white, radius: isAnimating ? 4 : 2)
                        .animation(.easeInOut(duration: 0.5), value: currentStep)
                }
            }
            .frame(height: 8)
        }
        .padding(.horizontal, 24)
        .padding(.top, 60)
        .padding(.bottom, 20)
    }
}

#Preview {
    GenZOnboardingView()
        .environmentObject(AuthManager())
        .environmentObject(UserManager())
        .environmentObject(ThemeManager())
}
