//
//  OnboardingNavigationControls.swift
//  WealthVibe - Onboarding Navigation Controls
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Navigation Controls
struct OnboardingNavigationControls: View {
    @Binding var currentStep: Int
    let totalSteps: Int
    let canProceed: Bool
    let onNext: () -> Void
    let onBack: () -> Void
    let onComplete: (() async -> Void)?
    @EnvironmentObject var onboardingManager: OnboardingManager
    
    var body: some View {
        HStack(spacing: 20) {
            // Back Button
            if currentStep > 0 {
                Button(action: onBack) {
                    HStack {
                        Image(systemName: "chevron.left")
                            .font(.headline)
                        Text("Back")
                            .font(.headline)
                    }
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 100, height: 48)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
                .transition(.opacity.combined(with: .move(edge: .leading)))
            } else {
                Spacer()
                    .frame(width: 100)
            }
            
            Spacer()
            
            // Next Button
            if currentStep < totalSteps - 1 {
                Button(action: onNext) {
                    HStack {
                        Text("Next")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Image(systemName: "chevron.right")
                            .font(.headline)
                    }
                    .foregroundColor(canProceed ? .black : .white.opacity(0.5))
                    .frame(width: 100, height: 48)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                canProceed ?
                                AnyShapeStyle(LinearGradient(colors: [.white, .white.opacity(0.9)], startPoint: .top, endPoint: .bottom)) :
                                AnyShapeStyle(Color.white.opacity(0.1))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(
                                        canProceed ? Color.clear : Color.white.opacity(0.3),
                                        lineWidth: 1
                                    )
                            )
                    )
                    .shadow(
                        color: canProceed ? .white.opacity(0.3) : .clear,
                        radius: canProceed ? 4 : 0
                    )
                }
                .disabled(!canProceed)
                .scaleEffect(canProceed ? 1.0 : 0.95)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: canProceed)
            } else {
                // Complete Onboarding Button for final step
                Button(action: {
                    Task {
                        if let onComplete = onComplete {
                            await onComplete()
                        }
                    }
                }) {
                    HStack(spacing: 8) {
                        if onboardingManager.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .progressViewStyle(CircularProgressViewStyle(tint: .black))
                        } else {
                            Text("Get Started")
                                .font(.headline)
                                .fontWeight(.semibold)
                            Image(systemName: "arrow.right")
                                .font(.headline)
                        }
                    }
                    .foregroundColor(.black)
                    .frame(width: 140, height: 48)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: [.white, .white.opacity(0.9)],
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(
                                        LinearGradient(
                                            colors: [.white.opacity(0.8), .white.opacity(0.4)],
                                            startPoint: .top,
                                            endPoint: .bottom
                                        ),
                                        lineWidth: 1
                                    )
                            )
                    )
                    .shadow(color: .white.opacity(0.3), radius: 4)
                }
                .disabled(onboardingManager.isLoading)
                .scaleEffect(onboardingManager.isLoading ? 0.95 : 1.0)
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: onboardingManager.isLoading)
            }
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 40)
    }
}
