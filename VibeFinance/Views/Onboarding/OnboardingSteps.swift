//
//  OnboardingSteps.swift
//  WealthVibe - Gen Z/Gen Alpha Onboarding Steps
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Welcome & Vibe Check Step
struct WelcomeVibeStep: View {
    @State private var isAnimating = false
    @State private var showEmojis = false
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            // Animated Logo
            VStack(spacing: 16) {
                ZStack {
                    // Glow effect
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [.white.opacity(0.3), .clear],
                                center: .center,
                                startRadius: 20,
                                endRadius: 80
                            )
                        )
                        .frame(width: 160, height: 160)
                        .scaleEffect(isAnimating ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: isAnimating)
                    
                    Text("💎")
                        .font(.system(size: 80))
                        .scaleEffect(isAnimating ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isAnimating)
                }
                
                Text("WealthVibe")
                    .font(.system(size: 42, weight: .bold, design: .rounded))
                    .foregroundColor(.white)
                    .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 2)
            }
            
            // Welcome Message
            VStack(spacing: 16) {
                Text("Welcome to the Future of Finance! 🚀")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                Text("Where investing meets gaming, and your money gets a glow-up ✨")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            
            // Floating Emojis
            if showEmojis {
                HStack(spacing: 20) {
                    ForEach(["🎮", "💰", "📈", "🎯", "👥"], id: \.self) { emoji in
                        Text(emoji)
                            .font(.title)
                            .offset(y: isAnimating ? -10 : 10)
                            .animation(
                                .easeInOut(duration: 1.5)
                                .repeatForever(autoreverses: true)
                                .delay(Double.random(in: 0...0.5)),
                                value: isAnimating
                            )
                    }
                }
            }
            
            Spacer()
            
            // Call to Action
            VStack(spacing: 12) {
                Text("Ready to vibe check your finances?")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text("Swipe to start your journey →")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .italic()
            }
            
            Spacer()
        }
        .padding(.horizontal, 24)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8)) {
                isAnimating = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                withAnimation(.easeInOut(duration: 0.5)) {
                    showEmojis = true
                }
            }
        }
    }
}

// MARK: - Personality Quiz Step
struct PersonalityQuizStep: View {
    @Binding var profile: GenZUserProfile
    @State private var selectedTraits: Set<PersonalityTrait> = []
    @State private var showDescription = false
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Header
            VStack(spacing: 16) {
                Text("🧠")
                    .font(.system(size: 60))
                
                Text("What's Your Vibe?")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                if showDescription {
                    Text("Pick the traits that describe you best (choose 2-4)")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            
            // Personality Traits Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                ForEach(PersonalityTrait.allCases, id: \.self) { trait in
                    PersonalityTraitCard(
                        trait: trait,
                        isSelected: selectedTraits.contains(trait)
                    ) {
                        toggleTrait(trait)
                    }
                }
            }
            .padding(.horizontal, 20)
            
            Spacer()
            
            // Selection Counter
            if !selectedTraits.isEmpty {
                HStack {
                    Text("\(selectedTraits.count) selected")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                    
                    Spacer()
                    
                    if selectedTraits.count >= 2 {
                        Text("✨ Perfect!")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                }
                .padding(.horizontal, 24)
                .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
            
            Spacer()
        }
        .onAppear {
            selectedTraits = profile.personalityTraits
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    showDescription = true
                }
            }
        }
        .onChange(of: selectedTraits) { newValue in
            profile.personalityTraits = newValue
        }
    }
    
    private func toggleTrait(_ trait: PersonalityTrait) {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            if selectedTraits.contains(trait) {
                selectedTraits.remove(trait)
            } else if selectedTraits.count < 4 {
                selectedTraits.insert(trait)
            }
        }
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Personality Trait Card
struct PersonalityTraitCard: View {
    let trait: PersonalityTrait
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(String(trait.rawValue.prefix(2)))
                    .font(.title2)
                
                Text(String(trait.rawValue.dropFirst(2)))
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        isSelected ?
                        LinearGradient(colors: [.white.opacity(0.3), .white.opacity(0.1)], startPoint: .top, endPoint: .bottom) :
                        LinearGradient(colors: [.white.opacity(0.1), .white.opacity(0.05)], startPoint: .top, endPoint: .bottom)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? Color.white.opacity(0.6) : Color.white.opacity(0.2),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .foregroundColor(.white)
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .shadow(
                color: isSelected ? .white.opacity(0.3) : .clear,
                radius: isSelected ? 8 : 0
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isSelected)
    }
}

// MARK: - Investment Style Step
struct InvestmentStyleStep: View {
    @Binding var profile: GenZUserProfile
    @State private var showDescription = false
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Header
            VStack(spacing: 16) {
                Text("📈")
                    .font(.system(size: 60))
                
                Text("What's Your Investment Vibe?")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                if showDescription {
                    Text("Choose the style that matches your energy")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            
            // Investment Styles
            VStack(spacing: 16) {
                ForEach(InvestmentStyle.allCases.filter { $0 != .unknown }, id: \.self) { style in
                    InvestmentStyleCard(
                        style: style,
                        isSelected: profile.investmentStyle == style
                    ) {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            profile.investmentStyle = style
                        }
                        
                        // Haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                    }
                }
            }
            .padding(.horizontal, 20)
            
            Spacer()
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    showDescription = true
                }
            }
        }
    }
}

// MARK: - Investment Style Card
struct InvestmentStyleCard: View {
    let style: InvestmentStyle
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                Text(String(style.rawValue.prefix(2)))
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(String(style.rawValue.dropFirst(2)))
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text(styleDescription(for: style))
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        isSelected ?
                        LinearGradient(colors: [.white.opacity(0.25), .white.opacity(0.1)], startPoint: .top, endPoint: .bottom) :
                        LinearGradient(colors: [.white.opacity(0.1), .white.opacity(0.05)], startPoint: .top, endPoint: .bottom)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? Color.white.opacity(0.6) : Color.white.opacity(0.2),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .shadow(
                color: isSelected ? .white.opacity(0.3) : .clear,
                radius: isSelected ? 8 : 0
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isSelected)
    }
    
    private func styleDescription(for style: InvestmentStyle) -> String {
        switch style {
        case .conservative: return "Slow and steady wins the race"
        case .balanced: return "Best of both worlds"
        case .aggressive: return "Go big or go home"
        case .trendy: return "Ride the wave of popular stocks"
        case .ethical: return "Invest in what you believe in"
        case .tech: return "Future-focused tech investments"
        case .unknown: return ""
        }
    }
}

#Preview {
    VStack {
        WelcomeVibeStep()
    }
    .background(
        LinearGradient(
            colors: [.purple, .pink, .orange],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    )
}
