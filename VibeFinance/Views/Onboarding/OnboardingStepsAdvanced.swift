//
//  OnboardingStepsAdvanced.swift
//  WealthVibe - Advanced Gen Z/Gen Alpha Onboarding Steps
//
//  Created by MAGESH DHANASEKARAN on 7/10/25.
//

import SwiftUI

// MARK: - Goals & Dreams Step
struct GoalsAndDreamsStep: View {
    @Binding var profile: GenZUserProfile
    @State private var selectedGoals: Set<FinancialGoal> = []
    @State private var showDescription = false
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Header
            VStack(spacing: 16) {
                Text("🎯")
                    .font(.system(size: 60))
                
                Text("What Are Your Money Goals?")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                if showDescription {
                    Text("Pick your top financial dreams (choose 2-5)")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            
            // Goals Grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                ForEach(FinancialGoal.allCases, id: \.self) { goal in
                    FinancialGoalCard(
                        goal: goal,
                        isSelected: selectedGoals.contains(goal)
                    ) {
                        toggleGoal(goal)
                    }
                }
            }
            .padding(.horizontal, 20)
            
            Spacer()
            
            // Selection Counter
            if !selectedGoals.isEmpty {
                HStack {
                    Text("\(selectedGoals.count) goals selected")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                    
                    Spacer()
                    
                    if selectedGoals.count >= 2 {
                        Text("🎯 Great choices!")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                }
                .padding(.horizontal, 24)
                .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
            
            Spacer()
        }
        .onAppear {
            selectedGoals = profile.financialGoals
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    showDescription = true
                }
            }
        }
        .onChange(of: selectedGoals) { newValue in
            profile.financialGoals = newValue
        }
    }
    
    private func toggleGoal(_ goal: FinancialGoal) {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            if selectedGoals.contains(goal) {
                selectedGoals.remove(goal)
            } else if selectedGoals.count < 5 {
                selectedGoals.insert(goal)
            }
        }
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Financial Goal Card
struct FinancialGoalCard: View {
    let goal: FinancialGoal
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(String(goal.rawValue.prefix(2)))
                    .font(.title2)
                
                Text(String(goal.rawValue.dropFirst(2)))
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        isSelected ?
                        LinearGradient(colors: [.white.opacity(0.3), .white.opacity(0.1)], startPoint: .top, endPoint: .bottom) :
                        LinearGradient(colors: [.white.opacity(0.1), .white.opacity(0.05)], startPoint: .top, endPoint: .bottom)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? Color.white.opacity(0.6) : Color.white.opacity(0.2),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .foregroundColor(.white)
            .scaleEffect(isSelected ? 1.05 : 1.0)
            .shadow(
                color: isSelected ? .white.opacity(0.3) : .clear,
                radius: isSelected ? 8 : 0
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isSelected)
    }
}

// MARK: - Risk Tolerance Game Step
struct RiskToleranceGameStep: View {
    @Binding var profile: GenZUserProfile
    @State private var showGame = false
    @State private var gameProgress = 0.0
    @State private var selectedLevel: OnboardingRiskLevel = .unknown
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Header
            VStack(spacing: 16) {
                Text("🎲")
                    .font(.system(size: 60))
                
                Text("Risk Tolerance Check")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                Text("How comfortable are you with investment ups and downs?")
                    .font(.body)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            
            if showGame {
                // Interactive Risk Game
                VStack(spacing: 20) {
                    // Scenario Question
                    VStack(spacing: 12) {
                        Text("💰 You have $1000 to invest...")
                            .font(.headline)
                            .foregroundColor(.white)
                        
                        Text("Your investment drops 20% in the first month. What do you do?")
                            .font(.body)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.white.opacity(0.1))
                    )
                    
                    // Risk Level Options
                    VStack(spacing: 12) {
                        ForEach(OnboardingRiskLevel.allCases.filter { $0 != .unknown }, id: \.self) { level in
                            RiskLevelCard(
                                level: level,
                                isSelected: selectedLevel == level,
                                scenario: riskScenario(for: level)
                            ) {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    selectedLevel = level
                                    profile.riskTolerance = level
                                }
                                
                                // Haptic feedback
                                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                impactFeedback.impactOccurred()
                            }
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
        .onAppear {
            selectedLevel = profile.riskTolerance
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    showGame = true
                }
            }
        }
    }
    
    private func riskScenario(for level: OnboardingRiskLevel) -> String {
        switch level {
        case .conservative: return "Sell immediately and put money in savings"
        case .moderate: return "Hold tight and wait for recovery"
        case .aggressive: return "Buy more while it's on sale!"
        case .yolo: return "HODL and buy the dip! 💎🙌"
        case .unknown: return ""
        }
    }
}

// MARK: - Risk Level Card
struct RiskLevelCard: View {
    let level: OnboardingRiskLevel
    let isSelected: Bool
    let scenario: String
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(String(level.rawValue.prefix(2)))
                        .font(.title3)
                    
                    Text(String(level.rawValue.dropFirst(2)))
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.title3)
                            .foregroundColor(.green)
                    }
                }
                
                Text(scenario)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        isSelected ?
                        LinearGradient(colors: [.white.opacity(0.25), .white.opacity(0.1)], startPoint: .top, endPoint: .bottom) :
                        LinearGradient(colors: [.white.opacity(0.1), .white.opacity(0.05)], startPoint: .top, endPoint: .bottom)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                isSelected ? Color.white.opacity(0.6) : Color.white.opacity(0.2),
                                lineWidth: isSelected ? 2 : 1
                            )
                    )
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .shadow(
                color: isSelected ? .white.opacity(0.3) : .clear,
                radius: isSelected ? 8 : 0
            )
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isSelected)
    }
}

// MARK: - Social Preferences Step
struct SocialPreferencesStep: View {
    @Binding var profile: GenZUserProfile
    @State private var showDescription = false
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // Header
            VStack(spacing: 16) {
                Text("👥")
                    .font(.system(size: 60))
                
                Text("Social Vibe Settings")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                if showDescription {
                    Text("Customize your social experience")
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            
            // Social Preferences
            VStack(spacing: 16) {
                SocialToggleCard(
                    title: "🏆 Share Achievements",
                    description: "Let friends see your wins",
                    isOn: Binding(
                        get: { profile.socialPreferences.shareAchievements },
                        set: { profile.socialPreferences.shareAchievements = $0 }
                    )
                )
                
                SocialToggleCard(
                    title: "👥 Join Investment Squads",
                    description: "Invest and learn with others",
                    isOn: Binding(
                        get: { profile.socialPreferences.joinSquads },
                        set: { profile.socialPreferences.joinSquads = $0 }
                    )
                )
                
                SocialToggleCard(
                    title: "🌟 Public Profile",
                    description: "Make your profile discoverable",
                    isOn: Binding(
                        get: { profile.socialPreferences.publicProfile },
                        set: { profile.socialPreferences.publicProfile = $0 }
                    )
                )
                
                SocialToggleCard(
                    title: "🎯 Competitive Mode",
                    description: "Join leaderboards and challenges",
                    isOn: Binding(
                        get: { profile.socialPreferences.competitiveMode },
                        set: { profile.socialPreferences.competitiveMode = $0 }
                    )
                )
            }
            .padding(.horizontal, 20)
            
            Spacer()
            
            // Note
            Text("You can change these settings anytime in your profile")
                .font(.caption)
                .foregroundColor(.white.opacity(0.6))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 24)
            
            Spacer()
        }
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 0.6)) {
                    showDescription = true
                }
            }
        }
    }
}

// MARK: - Social Toggle Card
struct SocialToggleCard: View {
    let title: String
    let description: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: .green))
                .scaleEffect(1.2)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isOn)
    }
}

#Preview {
    GoalsAndDreamsStep(profile: .constant(GenZUserProfile()))
        .background(
            LinearGradient(
                colors: [.orange, .red, .pink],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
}
