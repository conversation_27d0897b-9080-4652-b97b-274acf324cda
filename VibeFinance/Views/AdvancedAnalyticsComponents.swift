//
//  AdvancedAnalyticsComponents.swift
//  VibeFinance - Advanced Analytics Components
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import Charts

// MARK: - Advanced Metrics Section

struct AdvancedMetricsSection: View {
    let disclosureLevel: DisclosureLevel
    let timeframe: AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Image(systemName: "chart.bar.doc.horizontal")
                    .foregroundColor(.purple)
                    .font(.title3)
                
                Text("Advanced Metrics")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("Risk-Adjusted")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.white.opacity(0.1))
                    )
            }
            
            // Metrics Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(getAdvancedMetrics(), id: \.id) { metric in
                    AdvancedMetricCard(metric: metric)
                }
            }
            
            // Risk-Return Scatter Plot
            if disclosureLevel == .expert {
                RiskReturnScatterPlot(timeframe: timeframe)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private func getAdvancedMetrics() -> [AdvancedMetric] {
        return [
            AdvancedMetric(
                id: UUID(),
                name: "Information Ratio",
                value: 0.85,
                description: "Excess return per unit of tracking error",
                benchmark: 0.60,
                trend: .positive,
                category: .riskAdjusted
            ),
            AdvancedMetric(
                id: UUID(),
                name: "Sortino Ratio",
                value: 1.92,
                description: "Return per unit of downside deviation",
                benchmark: 1.45,
                trend: .positive,
                category: .riskAdjusted
            ),
            AdvancedMetric(
                id: UUID(),
                name: "Calmar Ratio",
                value: 1.47,
                description: "Annual return divided by max drawdown",
                benchmark: 1.20,
                trend: .positive,
                category: .riskAdjusted
            ),
            AdvancedMetric(
                id: UUID(),
                name: "Treynor Ratio",
                value: 0.108,
                description: "Excess return per unit of systematic risk",
                benchmark: 0.095,
                trend: .positive,
                category: .riskAdjusted
            )
        ]
    }
}

// MARK: - Advanced Metric Card

struct AdvancedMetricCard: View {
    let metric: AdvancedMetric
    @State private var showingDetail = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header
            HStack {
                Text(metric.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                Spacer()
                
                Image(systemName: metric.trend.iconName)
                    .foregroundColor(metric.trend.color)
                    .font(.caption)
            }
            
            // Value and comparison
            VStack(alignment: .leading, spacing: 4) {
                Text(String(format: "%.2f", metric.value))
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                HStack(spacing: 4) {
                    Text("vs")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))

                    Text(String(format: "%.2f", metric.benchmark))
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))

                    Spacer()

                    differenceText
                }
            }
            
            // Description
            Text(metric.description)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.7))
                .lineLimit(2)
        }
        .padding(12)
        .background(cardBackground)
        .onTapGesture {
            showingDetail = true
        }
        .sheet(isPresented: $showingDetail) {
            NavigationView {
                VStack(alignment: .leading, spacing: 16) {
                    Text(metric.name)
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("Value: \(metric.value, specifier: "%.2f")")
                        .font(.body)

                    Text("Trend: \(trendDescription)")
                        .font(.body)

                    Spacer()
                }
                .padding()
                .navigationTitle("Metric Details")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            showingDetail = false
                        }
                    }
                }
            }
        }
    }

    private var differenceText: some View {
        let difference = ((metric.value - metric.benchmark) / metric.benchmark) * 100
        return Text("\(difference >= 0 ? "+" : "")\(String(format: "%.1f", difference))%")
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(difference >= 0 ? .green : .red)
    }

    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.white.opacity(0.05))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
    }

    private var trendDescription: String {
        switch metric.trend {
        case .positive: return "↗️ Positive"
        case .negative: return "↘️ Negative"
        case .neutral: return "➡️ Neutral"
        }
    }
}

// MARK: - Risk-Return Scatter Plot

struct RiskReturnScatterPlot: View {
    let timeframe: AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Text("Risk-Return Analysis")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("Efficient Frontier")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            // Scatter plot
            Chart(getRiskReturnData()) { point in
                PointMark(
                    x: .value("Risk", point.risk),
                    y: .value("Return", point.returnValue)
                )
                .foregroundStyle(point.isPortfolio ? .yellow : .blue)
                .symbolSize(point.isPortfolio ? 100 : 50)
            }
            .frame(height: 200)
            .chartBackground { chartProxy in
                Rectangle()
                    .fill(Color.white.opacity(0.02))
            }
            .chartXAxis {
                AxisMarks(position: .bottom) { _ in
                    AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                        .foregroundStyle(.white.opacity(0.2))
                    AxisValueLabel()
                        .foregroundStyle(.white.opacity(0.7))
                        .font(.caption2)
                }
            }
            .chartYAxis {
                AxisMarks(position: .leading) { _ in
                    AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5))
                        .foregroundStyle(.white.opacity(0.2))
                    AxisValueLabel()
                        .foregroundStyle(.white.opacity(0.7))
                        .font(.caption2)
                }
            }
            
            // Legend
            HStack(spacing: 16) {
                HStack(spacing: 4) {
                    Circle()
                        .fill(.yellow)
                        .frame(width: 8, height: 8)
                    
                    Text("Your Portfolio")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                HStack(spacing: 4) {
                    Circle()
                        .fill(.blue)
                        .frame(width: 8, height: 8)
                    
                    Text("Benchmarks")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
    
    private func getRiskReturnData() -> [RiskReturnPoint] {
        return [
            RiskReturnPoint(risk: 18.2, returnValue: 12.5, name: "Your Portfolio", isPortfolio: true),
            RiskReturnPoint(risk: 22.1, returnValue: 9.1, name: "S&P 500", isPortfolio: false),
            RiskReturnPoint(risk: 25.3, returnValue: 11.2, name: "NASDAQ", isPortfolio: false),
            RiskReturnPoint(risk: 15.8, returnValue: 7.8, name: "Bonds", isPortfolio: false),
            RiskReturnPoint(risk: 28.5, returnValue: 13.1, name: "Small Cap", isPortfolio: false),
            RiskReturnPoint(risk: 20.1, returnValue: 8.9, name: "International", isPortfolio: false)
        ]
    }
}

// MARK: - Expert Analysis Section

struct ExpertAnalysisSection: View {
    let timeframe: AnalyticsTimeframe
    
    var body: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.pink)
                    .font(.title3)
                
                Text("Expert Analysis")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("AI-Powered")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.white.opacity(0.1))
                    )
            }
            
            // Analysis Cards
            VStack(spacing: 12) {
                FactorAnalysisCard()
                AttributionAnalysisCard()
                ScenarioAnalysisCard()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Factor Analysis Card

struct FactorAnalysisCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Factor Exposure")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("Fama-French")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))
            }
            
            // Factor bars
            VStack(spacing: 8) {
                FactorBar(name: "Market (Beta)", value: 1.15, benchmark: 1.00)
                FactorBar(name: "Size (SMB)", value: -0.12, benchmark: 0.00)
                FactorBar(name: "Value (HML)", value: 0.28, benchmark: 0.00)
                FactorBar(name: "Momentum", value: 0.15, benchmark: 0.00)
                FactorBar(name: "Quality", value: 0.22, benchmark: 0.00)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
}

// MARK: - Factor Bar

struct FactorBar: View {
    let name: String
    let value: Double
    let benchmark: Double
    
    var body: some View {
        HStack {
            Text(name)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .frame(width: 80, alignment: .leading)
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // Background
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 4)
                    
                    // Value bar
                    RoundedRectangle(cornerRadius: 2)
                        .fill(value >= benchmark ? .green : .red)
                        .frame(
                            width: geometry.size.width * abs(value) / 2.0,
                            height: 4
                        )
                        .offset(x: value < 0 ? geometry.size.width * (1 + value / 2.0) : geometry.size.width * 0.5)
                    
                    // Benchmark line
                    Rectangle()
                        .fill(.white)
                        .frame(width: 1, height: 8)
                        .offset(x: geometry.size.width * 0.5)
                }
            }
            .frame(height: 8)
            
            Text(String(format: "%.2f", value))
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(value >= benchmark ? .green : .red)
                .frame(width: 40, alignment: .trailing)
        }
    }
}

// MARK: - Attribution Analysis Card

struct AttributionAnalysisCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Performance Attribution")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("Sector Analysis")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))
            }
            
            // Attribution chart
            HStack(spacing: 8) {
                ForEach(getAttributionData(), id: \.sector) { data in
                    VStack(spacing: 4) {
                        Rectangle()
                            .fill(data.contribution >= 0 ? .green : .red)
                            .frame(width: 20, height: abs(data.contribution) * 50)
                        
                        Text(data.sector)
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                            .rotationEffect(.degrees(-45))
                    }
                }
            }
            .frame(height: 80)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
    
    private func getAttributionData() -> [AttributionData] {
        return [
            AttributionData(sector: "Tech", contribution: 2.8),
            AttributionData(sector: "Health", contribution: 1.2),
            AttributionData(sector: "Finance", contribution: -0.5),
            AttributionData(sector: "Energy", contribution: -1.1),
            AttributionData(sector: "Consumer", contribution: 0.8)
        ]
    }
}

// MARK: - Scenario Analysis Card

struct ScenarioAnalysisCard: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Scenario Analysis")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("Monte Carlo")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))
            }
            
            // Scenario outcomes
            VStack(spacing: 6) {
                ScenarioRow(name: "Bull Market (+20%)", probability: 25, outcome: "+18.5%", color: .green)
                ScenarioRow(name: "Normal Market (+8%)", probability: 50, outcome: "+9.2%", color: .blue)
                ScenarioRow(name: "Bear Market (-15%)", probability: 25, outcome: "-12.1%", color: .red)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
}

// MARK: - Scenario Row

struct ScenarioRow: View {
    let name: String
    let probability: Int
    let outcome: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(name)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            
            Spacer()
            
            Text("\(probability)%")
                .font(.caption)
                .foregroundColor(.white.opacity(0.6))
            
            Text(outcome)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
                .frame(width: 50, alignment: .trailing)
        }
    }
}

// MARK: - Supporting Data Models

struct AdvancedMetric: Identifiable {
    let id: UUID
    let name: String
    let value: Double
    let description: String
    let benchmark: Double
    let trend: MetricTrend
    let category: MetricCategory
}

enum MetricTrend {
    case positive, negative, neutral
    
    var iconName: String {
        switch self {
        case .positive: return "arrow.up.right"
        case .negative: return "arrow.down.right"
        case .neutral: return "minus"
        }
    }
    
    var color: Color {
        switch self {
        case .positive: return .green
        case .negative: return .red
        case .neutral: return .gray
        }
    }
}

enum MetricCategory {
    case riskAdjusted, performance, risk, efficiency
}

struct RiskReturnPoint: Identifiable {
    let id = UUID()
    let risk: Double
    let returnValue: Double
    let name: String
    let isPortfolio: Bool
}

struct AttributionData {
    let sector: String
    let contribution: Double
}
