//
//  MainTabView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Enhanced Main Tab View with Phase 2 Improvements

struct MainTabView: View {
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var realTradingManager: RealTradingManager
    @EnvironmentObject var analyticsManager: AnalyticsManager
    @EnvironmentObject var paymentManager: PaymentManager
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.theme) var theme
    @State private var selectedTab = 0

    // Phase 2 Enhancements
    @StateObject private var navigationCoordinator = NavigationCoordinator()

    var body: some View {
        ZStack {
            // Beautiful Glassmorphic Background
            themeManager.colors.background
                .ignoresSafeArea()

            VStack(spacing: 0) {
                // Content Area
                Group {
                    switch selectedTab {
                    case 0:
                        DashboardView()
                    case 1:
                        TradingView()
                    case 2:
                        BuffettAnalyticsView()
                    case 3:
                        BuffettChatView()
                    case 4:
                        ProfileView()
                    default:
                        DashboardView()
                    }
                }

                // Custom Glassmorphic Tab Bar
                HStack(spacing: 0) {
                    GlassmorphicTabButton(title: "Dashboard", icon: "house.fill", tag: 0, selectedTab: $selectedTab)
                    GlassmorphicTabButton(title: "Trading", icon: "chart.line.uptrend.xyaxis", tag: 1, selectedTab: $selectedTab)
                    GlassmorphicTabButton(title: "Analytics", icon: "chart.bar.xaxis", tag: 2, selectedTab: $selectedTab)
                    GlassmorphicTabButton(title: "AI", icon: "brain.head.profile", tag: 3, selectedTab: $selectedTab)
                    GlassmorphicTabButton(title: "Profile", icon: "person", tag: 4, selectedTab: $selectedTab)
                }
                .frame(height: 80)
                .background(
                    RoundedRectangle(cornerRadius: 0)
                        .fill(themeManager.colors.surface)
                        .background(.ultraThinMaterial)
                )
            }
        }
        // Temporarily commented out complex navigation logic
        // .onChange(of: navigationCoordinator.currentTab) { _, newTab in
        //     selectedTab = newTab.rawValue
        // }
        // .onChange(of: selectedTab) { _, newTab in
        //     if let tab = NavigationCoordinator.MainTab(rawValue: newTab) {
        //         navigationCoordinator.navigateToTab(tab)
        //     }
        // }
    }

    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()

        // Warren Buffett inspired tab bar background - consistent with design system
        appearance.backgroundColor = UIColor(red: 0.1, green: 0.2, blue: 0.4, alpha: 0.95)

        // Warren Buffett inspired gold accent for selected items
        let goldColor = UIColor(red: 1.0, green: 0.84, blue: 0.0, alpha: 1.0)
        appearance.selectionIndicatorTintColor = goldColor

        // Normal (unselected) item styling
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.white.withAlphaComponent(0.6)
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.white.withAlphaComponent(0.6)
        ]

        // Selected item styling with Warren Buffett gold
        appearance.stackedLayoutAppearance.selected.iconColor = goldColor
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: goldColor
        ]

        // Apply consistent appearance across all tab bar states
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance

        // Ensure consistent tint color
        UITabBar.appearance().tintColor = goldColor
        UITabBar.appearance().unselectedItemTintColor = UIColor.white.withAlphaComponent(0.6)
    }
}

// MARK: - Real Feed View Implementation

struct FeedView: View {
    @EnvironmentObject var feedManager: FeedManager
    @EnvironmentObject var userManager: UserManager
    @State private var showingFilters = false
    @State private var selectedFilter: FeedFilter = .all
    @State private var showingSubscriptionView = false
    @State private var showingPerformanceMonitor = false

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Feed Header with Quick Actions
                    FeedHeaderView(
                        showingFilters: $showingFilters,
                        selectedFilter: $selectedFilter
                    )

                    // Feed Items
                    if feedManager.isLoading && feedManager.feedItems.isEmpty {
                        FeedLoadingView()
                    } else {
                        ForEach(filteredFeedItems) { feedItem in
                            FeedItemCard(feedItem: feedItem) { action in
                                handleFeedAction(action, for: feedItem)
                            }
                        }

                        // Load More Button
                        if feedManager.hasMoreItems {
                            LoadMoreButton {
                                Task {
                                    await feedManager.loadMoreFeedItems()
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Your Feed")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await refreshFeed()
            }
            .onAppear {
                Task {
                    await loadInitialFeed()
                }
            }
            .sheet(isPresented: $showingFilters) {
                FeedFiltersView(selectedFilter: $selectedFilter)
            }
            .sheet(isPresented: $showingSubscriptionView) {
                EnhancedSubscriptionView()
            }
            .sheet(isPresented: $showingPerformanceMonitor) {
                PerformanceMonitorView()
            }
        }
    }

    private var filteredFeedItems: [FeedItem] {
        switch selectedFilter {
        case .all:
            return feedManager.feedItems
        case .stocks:
            return feedManager.feedItems.filter { $0.content.category == .stocks }
        case .crypto:
            return feedManager.feedItems.filter { $0.content.category == .crypto }
        case .news:
            return feedManager.feedItems.filter { $0.content.category == .news }
        case .education:
            return feedManager.feedItems.filter { $0.content.category == .education }
        case .bookmarked:
            return feedManager.feedItems.filter { $0.isBookmarked }
        }
    }

    private func loadInitialFeed() async {
        guard let user = userManager.user else { return }
        await feedManager.generateDailyFeed(for: user)
    }

    private func refreshFeed() async {
        guard let user = userManager.user else { return }
        await feedManager.refreshFeed(for: user)
    }

    private func handleFeedAction(_ action: FeedAction, for feedItem: FeedItem) {
        Task {
            switch action {
            case .like:
                await feedManager.addReaction(.fire, to: feedItem)
            case .bookmark:
                await feedManager.toggleBookmark(for: feedItem)
            case .share:
                // Handle sharing
                break
            case .invest:
                // Navigate to investment flow
                break
            }
        }
    }
}

struct QuestsView: View {
    @State private var dailyQuests: [Quest] = [
        Quest(
            title: "Market Analysis Master",
            description: "Analyze 3 trending stocks and make AI-powered predictions",
            category: .stocks,
            difficulty: .intermediate,
            xpReward: 100,
            estimatedTime: 15,
            tasks: [
                QuestTask(title: "Research Apple Stock", description: "Analyze AAPL fundamentals", type: .research, xpReward: 30, order: 1),
                QuestTask(title: "Predict Price Movement", description: "Make a prediction for next week", type: .multipleChoice, xpReward: 40, order: 2),
                QuestTask(title: "Compare with AI", description: "See how your prediction matches AI analysis", type: .simulation, xpReward: 30, order: 3)
            ],
            isDaily: true,
            expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date())
        ),
        Quest(
            title: "Portfolio Diversification",
            description: "Add different asset classes to reduce risk",
            category: .investing,
            difficulty: .advanced,
            xpReward: 200,
            estimatedTime: 25,
            tasks: [
                QuestTask(title: "Learn About ETFs", description: "Watch educational video on ETFs", type: .video, xpReward: 50, order: 1),
                QuestTask(title: "Choose Asset Classes", description: "Select 3 different asset classes", type: .multipleChoice, xpReward: 75, order: 2),
                QuestTask(title: "Make Investment", description: "Invest in your chosen assets", type: .simulation, xpReward: 75, order: 3)
            ],
            isDaily: true,
            expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date())
        ),
        Quest(
            title: "Risk Assessment Mastery",
            description: "Complete comprehensive risk tolerance evaluation",
            category: .basics,
            difficulty: .beginner,
            xpReward: 50,
            estimatedTime: 10,
            tasks: [
                QuestTask(title: "Risk Quiz", description: "Answer 10 questions about risk tolerance", type: .multipleChoice, xpReward: 25, order: 1),
                QuestTask(title: "Review Results", description: "Understand your risk profile", type: .reading, xpReward: 25, order: 2)
            ],
            isDaily: true,
            expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date())
        )
    ]

    @State private var weeklyChallenge = WeeklyChallenge(
        title: "Beat the Market",
        description: "Outperform S&P 500 this week with your picks",
        reward: 5000,
        xpReward: 1000,
        participants: 1247,
        timeRemaining: "3d 14h",
        currentRank: 23,
        performance: 8.7
    )

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    WeeklyChallengeCard(challenge: weeklyChallenge)
                    dailyQuestsSection
                    achievementsSection
                }
                .padding()
            }
            .navigationTitle("Quests")
            .refreshable {
                await refreshQuests()
            }
        }
    }

    private var dailyQuestsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("🎯 Daily Quests")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
                Text("0/\(dailyQuests.count)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            ForEach(dailyQuests) { quest in
                QuestCard(quest: quest) {
                    startQuest(quest)
                }
            }
        }
    }

    private var achievementsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🏆 Recent Achievements")
                .font(.title2)
                .fontWeight(.bold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                AchievementBadge(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "First Profit",
                    description: "Made your first profitable trade",
                    isUnlocked: true
                )
                AchievementBadge(
                    icon: "person.3.fill",
                    title: "Squad Leader",
                    description: "Created your first investment squad",
                    isUnlocked: true
                )
                AchievementBadge(
                    icon: "star.fill",
                    title: "Week Warrior",
                    description: "Complete 7 daily quests in a row",
                    isUnlocked: false
                )
                AchievementBadge(
                    icon: "crown.fill",
                    title: "Market Master",
                    description: "Beat market for 30 days straight",
                    isUnlocked: false
                )
            }
        }
        .padding(.top)
    }

    private func startQuest(_ quest: Quest) {
        // Navigate to quest detail view
        let questDetailView = QuestDetailView(quest: quest)
            .environmentObject(QuestManager())
            .environmentObject(UserManager())

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            let hostingController = UIHostingController(rootView: questDetailView)
            hostingController.modalPresentationStyle = .fullScreen
            window.rootViewController?.present(hostingController, animated: true)
        }
    }

    private func refreshQuests() async {
        // Simulate API call to refresh quests
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        // In real app, this would fetch new quests from the server
    }
}

struct SquadsView: View {
    @State private var squads: [Squad] = [
        Squad(
            name: "Tech Titans 💻",
            description: "Investing in the future of technology",
            emoji: "💻",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 500
        ),
        Squad(
            name: "Green Energy Squad 🌱",
            description: "Sustainable investing for the planet",
            emoji: "🌱",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 300
        ),
        Squad(
            name: "Dividend Dynasty 💰",
            description: "Building wealth through dividend income",
            emoji: "💰",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 200
        )
    ]

    @State private var showingCreateSquad = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header Stats
                    VStack(spacing: 16) {
                        HStack {
                            VStack(alignment: .leading) {
                                Text("Your Squads")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                Text("Collaborative investing made simple")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                            Button(action: { showingCreateSquad = true }) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.title)
                                    .foregroundColor(.purple)
                            }
                        }

                        // Quick Stats
                        HStack(spacing: 20) {
                            StatCard(title: "Total Value", value: "$4.2M", icon: "dollarsign.circle.fill", color: .green)
                            StatCard(title: "Avg Return", value: "+8.4%", icon: "chart.line.uptrend.xyaxis", color: .blue)
                            StatCard(title: "Active Squads", value: "3", icon: "person.3.fill", color: .purple)
                        }
                    }

                    // Featured Squads
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🔥 Trending Squads")
                            .font(.title2)
                            .fontWeight(.bold)

                        ForEach(squads) { squad in
                            SquadCard(squad: squad) {
                                joinSquad(squad)
                            }
                        }
                    }

                    // AI Recommendations
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🤖 AI Recommended for You")
                            .font(.title2)
                            .fontWeight(.bold)

                        AIRecommendationSquadCard(
                            title: "Crypto Pioneers ₿",
                            description: "Based on your risk profile and interest in emerging tech",
                            expectedReturn: 15.2,
                            memberCount: 89
                        )
                    }

                    // Performance Leaderboard
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🏆 Top Performing Squads")
                            .font(.title2)
                            .fontWeight(.bold)

                        ForEach(Array(squads.enumerated()), id: \.element.id) { index, squad in
                            SquadLeaderboardRow(
                                rank: index + 1,
                                squad: squad
                            )
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Investment Squads")
            .sheet(isPresented: $showingCreateSquad) {
                CreateSquadView()
            }
        }
    }

    private func joinSquad(_ squad: Squad) {
        // Handle squad joining logic
        print("Joining squad: \(squad.name)")
    }
}

// MARK: - Combined Trading View
struct TradingView: View {
    @State private var selectedMode: TradingMode = .simulator
    @StateObject private var performanceManager = PerformanceManager.shared
    @StateObject private var batteryOptimizer = BatteryOptimizer()
    @EnvironmentObject var themeManager: ThemeManager

    enum TradingMode: String, CaseIterable {
        case simulator = "Simulator"
        case realTrading = "Real Trading"

        var icon: String {
            switch self {
            case .simulator: return "gamecontroller.fill"
            case .realTrading: return "chart.line.uptrend.xyaxis"
            }
        }
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Beautiful Glassmorphic Background
                themeManager.colors.background
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Glassmorphic Mode Selector
                    GlassmorphicModeSelector(selectedMode: $selectedMode)
                        .padding(.horizontal, 16)
                        .padding(.top, 8)

                    // Content based on selected mode
                    Group {
                        switch selectedMode {
                        case .simulator:
                            BuffettSimulatorView()
                        case .realTrading:
                            RealTradingView()
                        }
                    }
                    .memoryEfficient()
                }
            }
            .navigationTitle("Trading")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                performanceManager.startPerformanceMonitoring()
            }
            .onDisappear {
                performanceManager.stopPerformanceMonitoring()
            }
            .onChange(of: selectedMode) { _, _ in
                // Track mode change for analytics
                DevelopmentConfig.log("Trading mode changed to \(selectedMode)", category: "USER")
            }
        }
    }
}

// MARK: - Glassmorphic Mode Selector
struct GlassmorphicModeSelector: View {
    @Binding var selectedMode: TradingView.TradingMode
    @StateObject private var batteryOptimizer = BatteryOptimizer()

    var body: some View {
        ZStack {
            // Background
            backgroundView

            // Content
            HStack(spacing: 0) {
                ForEach(TradingView.TradingMode.allCases, id: \.self) { mode in
                    ModeButton(
                        mode: mode,
                        selectedMode: selectedMode,
                        batteryOptimizer: batteryOptimizer
                    ) {
                        selectedMode = mode
                    }
                }
            }
            .padding(4)
        }
        .frame(height: 52)
    }

    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(
                LinearGradient(
                    colors: [
                        Color.white.opacity(0.2),
                        Color.white.opacity(0.1)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
            )
            .blur(radius: 0.5)
    }
}

// MARK: - Glassmorphic Tab Button
struct GlassmorphicTabButton: View {
    let title: String
    let icon: String
    let tag: Int
    @Binding var selectedTab: Int
    @Environment(\.theme) var theme

    var body: some View {
        VStack(spacing: 4) {
            ZStack {
                if selectedTab == tag {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    theme.accent.opacity(0.8),
                                    theme.accent.opacity(0.6)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 32, height: 32)
                        .shadow(color: theme.accent.opacity(0.3), radius: 8, x: 0, y: 4)
                }

                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(selectedTab == tag ? Color.black : theme.onBackground.opacity(0.8))
            }

            Text(title)
                .font(.caption2)
                .fontWeight(selectedTab == tag ? .semibold : .medium)
                .foregroundColor(selectedTab == tag ? theme.accent : theme.onBackground.opacity(0.6))
        }
        .frame(maxWidth: .infinity)
        .frame(height: 60)
        .contentShape(Rectangle())
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.2)) {
                selectedTab = tag
            }
        }
    }
}

// MARK: - Mode Button Component
struct ModeButton: View {
    let mode: TradingView.TradingMode
    let selectedMode: TradingView.TradingMode
    let batteryOptimizer: BatteryOptimizer
    let action: () -> Void
    @Environment(\.theme) var theme

    var body: some View {
        Button(action: {
            let shouldReduceAnimations = batteryOptimizer.batteryOptimizationLevel == .aggressive
            let animation: Animation? = shouldReduceAnimations ? nil : .easeInOut(duration: 0.3)
            withAnimation(animation) {
                action()
            }
        }) {
            ZStack {
                if selectedMode == mode {
                    selectedBackground
                }

                buttonContent
            }
            .frame(maxWidth: .infinity)
            .frame(height: 44)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var selectedBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(
                LinearGradient(
                    colors: [
                        theme.accent.opacity(0.8),
                        theme.accent.opacity(0.6)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .shadow(color: theme.accent.opacity(0.3), radius: 8, x: 0, y: 4)
    }

    private var buttonContent: some View {
        HStack(spacing: 8) {
            Image(systemName: mode.icon)
                .font(.subheadline)
            Text(mode.rawValue)
                .font(.subheadline)
                .fontWeight(selectedMode == mode ? .bold : .medium)
        }
        .foregroundColor(selectedMode == mode ? Color.black : theme.onBackground.opacity(0.8))
    }
}

struct SimulatorView: View {
    @EnvironmentObject var simulatorManager: SimulatorManager
    @EnvironmentObject var userManager: UserManager
    @State private var selectedTab = 0
    @State private var showingStockSearch = false
    @State private var showingTradeSheet = false
    @State private var selectedStock: StockPrice?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Portfolio Header
                SimulatorPortfolioHeader(
                    virtualBalance: simulatorManager.virtualBalance,
                    totalValue: simulatorManager.totalPortfolioValue,
                    dailyChange: simulatorManager.dailyChange,
                    dailyChangePercent: simulatorManager.dailyChangePercent
                )

                // Tab Selector
                SimulatorTabSelector(selectedTab: $selectedTab)

                // Content
                TabView(selection: $selectedTab) {
                    // Portfolio Tab
                    SimulatorPortfolioView()
                        .tag(0)

                    // Market Tab
                    SimulatorMarketView(
                        onStockSelected: { stock in
                            selectedStock = stock
                            showingTradeSheet = true
                        }
                    )
                    .tag(1)

                    // History Tab
                    SimulatorHistoryView()
                        .tag(2)

                    // Leaderboard Tab
                    SimulatorLeaderboardView()
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Investment Simulator")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingStockSearch = true
                    }) {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showingStockSearch) {
                SimulatorStockSearchView { stock in
                    selectedStock = stock
                    showingTradeSheet = true
                }
            }
            .sheet(isPresented: $showingTradeSheet) {
                if let stock = selectedStock {
                    SimulatorTradeView(stock: stock)
                }
            }
            .onAppear {
                Task {
                    await simulatorManager.loadPortfolio()
                }
            }
        }
    }
}

struct ChatView: View {
    @State private var messages: [ChatMessage] = [
        ChatMessage(
            id: UUID(),
            content: "Hello! I'm your AI financial advisor. I can help you with investment strategies, market analysis, portfolio optimization, and financial planning. What would you like to discuss today?",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-300)
        )
    ]
    @State private var newMessage = ""
    @State private var isTyping = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Quick Action Buttons
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        QuickChatButton(icon: "chart.line.uptrend.xyaxis", title: "Market Analysis", action: {
                            sendQuickMessage("Give me a market analysis for today")
                        })
                        QuickChatButton(icon: "dollarsign.circle", title: "Investment Ideas", action: {
                            sendQuickMessage("What are some good investment opportunities right now?")
                        })
                        QuickChatButton(icon: "person.crop.circle.badge.questionmark", title: "Portfolio Review", action: {
                            sendQuickMessage("Can you review my portfolio and suggest improvements?")
                        })
                        QuickChatButton(icon: "brain.head.profile", title: "AI Insights", action: {
                            sendQuickMessage("What are your AI insights for my financial goals?")
                        })
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)
                .background(Color(.systemGray6))

                // Messages List
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(messages) { message in
                                ChatMessageView(message: message)
                            }

                            if isTyping {
                                TypingIndicatorView()
                            }
                        }
                        .padding()
                    }
                    .onChange(of: messages.count) { _, _ in
                        withAnimation {
                            proxy.scrollTo(messages.last?.id, anchor: .bottom)
                        }
                    }
                }

                // Message Input
                HStack(spacing: 12) {
                    TextField("Ask me anything about finance...", text: $newMessage, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(1...4)

                    Button(action: sendMessage) {
                        Image(systemName: "paperplane.fill")
                            .foregroundColor(.white)
                            .frame(width: 36, height: 36)
                            .background(
                                Circle()
                                    .fill(newMessage.isEmpty ? Color.gray : Color.purple)
                            )
                    }
                    .disabled(newMessage.isEmpty)
                }
                .padding()
                .background(Color(.systemBackground))
            }
            .navigationTitle("AI Advisor")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    private func sendMessage() {
        guard !newMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let userMessage = ChatMessage(
            id: UUID(),
            content: newMessage,
            isFromUser: true,
            timestamp: Date()
        )

        messages.append(userMessage)
        let messageToSend = newMessage
        newMessage = ""

        // Simulate AI response
        generateAIResponse(to: messageToSend)
    }

    private func sendQuickMessage(_ message: String) {
        let userMessage = ChatMessage(
            id: UUID(),
            content: message,
            isFromUser: true,
            timestamp: Date()
        )

        messages.append(userMessage)
        generateAIResponse(to: message)
    }

    private func generateAIResponse(to message: String) {
        isTyping = true

        Task {
            let aiService = AIServiceManager.shared
            let response = await aiService.getChatResponse(message: message)

            await MainActor.run {
                self.isTyping = false

                let aiMessage = ChatMessage(
                    id: UUID(),
                    content: response,
                    isFromUser: false,
                    timestamp: Date()
                )

                self.messages.append(aiMessage)
            }
        }
    }

    private func generateSmartResponse(for message: String) -> String {
        let lowercased = message.lowercased()

        if lowercased.contains("market") || lowercased.contains("analysis") {
            return "📈 Based on current market conditions, I'm seeing strong momentum in tech stocks and renewable energy sectors. The S&P 500 is showing bullish patterns, but I recommend maintaining a diversified portfolio. Would you like me to analyze specific stocks or sectors?"
        } else if lowercased.contains("investment") || lowercased.contains("invest") {
            return "💡 For your investment strategy, I recommend a balanced approach: 60% stocks (mix of growth and value), 30% bonds, and 10% alternative investments like REITs. Given your risk profile, consider dollar-cost averaging into index funds. What's your investment timeline?"
        } else if lowercased.contains("portfolio") {
            return "📊 Your portfolio is performing well with a 12.3% YTD return! I notice you're overweight in tech (45% vs recommended 25%). Consider rebalancing into healthcare and international markets. Your risk-adjusted returns could improve by 2-3% annually with better diversification."
        } else if lowercased.contains("crypto") || lowercased.contains("bitcoin") {
            return "₿ Cryptocurrency can be a valuable portfolio addition, but limit exposure to 5-10% of total assets. Bitcoin and Ethereum remain the most established options. Consider DCA strategy and only invest what you can afford to lose. The volatility is high but long-term prospects remain positive."
        } else {
            return "🤖 I understand you're looking for financial guidance. I can help with investment strategies, portfolio analysis, market insights, risk assessment, and financial planning. Could you be more specific about what aspect of your finances you'd like to discuss?"
        }
    }
}

struct ProfileView: View {
    @EnvironmentObject var authManager: AuthManager
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.theme) var theme
    @State private var showingSubscriptionView = false
    @State private var showingPerformanceMonitor = false
    @State private var showingThemeSettings = false

    var body: some View {
        NavigationView {
            ZStack {
                // Beautiful Glassmorphic Background
                themeManager.colors.background
                    .ignoresSafeArea()

                VStack(spacing: 20) {
                    // Profile Header
                    VStack(spacing: 12) {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [Color(red: 1.0, green: 0.84, blue: 0.0), Color(red: 1.0, green: 0.9, blue: 0.3)],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 80, height: 80)
                            .overlay(
                                Text(userManager.user?.username.prefix(1).uppercased() ?? "U")
                                    .font(.title)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color(red: 0.1, green: 0.2, blue: 0.4))
                            )

                        Text(userManager.user?.username ?? "User")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(theme.onBackground)

                        Text(userManager.user?.email ?? "")
                            .font(.subheadline)
                            .foregroundColor(theme.onBackground.opacity(0.7))
                    }
                .padding(.top, 20)

                // Stats - Updated with consistent Warren Buffett styling
                if let stats = userManager.getUserStats() {
                    HStack(spacing: 30) {
                        VStack {
                            Text("\(stats.level)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(Color(red: 1.0, green: 0.84, blue: 0.0))
                            Text("Level")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }

                        VStack {
                            Text("\(stats.xp)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(Color(red: 1.0, green: 0.84, blue: 0.0))
                            Text("XP")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }

                        VStack {
                            Text("\(stats.daysActive)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(Color(red: 1.0, green: 0.84, blue: 0.0))
                            Text("Days")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }
                    }
                    .padding(.vertical, 20)
                }

                // Subscription Status - Updated with glassmorphic styling
                Button(action: {
                    showingSubscriptionView = true
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Subscription")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.white)

                            Text(userManager.user?.subscriptionStatus.displayName ?? "Free")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }

                        Spacer()

                        Text(userManager.user?.subscriptionStatus.displayName ?? "Free")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(Color(red: 0.1, green: 0.2, blue: 0.4))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color(red: 1.0, green: 0.84, blue: 0.0))
                            )

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.25),
                                        Color.white.opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                            .blur(radius: 0.5)
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 20)

                // Theme Settings - Updated with glassmorphic styling
                Button(action: {
                    showingThemeSettings = true
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Appearance")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(theme.onBackground)

                            Text(themeManager.useSystemTheme ? "System" : themeManager.currentTheme.displayName)
                                .font(.caption)
                                .foregroundColor(theme.onBackground.opacity(0.7))
                        }

                        Spacer()

                        Image(systemName: themeManager.currentTheme == .dark ? "moon.fill" : "sun.max.fill")
                            .font(.title3)
                            .foregroundColor(theme.accent)

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(theme.onBackground.opacity(0.7))
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: themeManager.currentTheme == .dark ? [
                                        Color.white.opacity(0.25),
                                        Color.white.opacity(0.1)
                                    ] : [
                                        Color.black.opacity(0.1),
                                        Color.black.opacity(0.05)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(theme.onBackground.opacity(0.3), lineWidth: 1)
                            )
                            .blur(radius: 0.5)
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 20)

                // Performance Monitor (Development only) - Updated with glassmorphic styling
                if DevelopmentConfig.isDevelopmentMode && DevelopmentConfig.showPerformanceMetrics {
                    Button(action: {
                        showingPerformanceMonitor = true
                    }) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Performance Monitor")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.white)

                                Text("Debug & Optimization")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.7))
                            }

                            Spacer()

                            Image(systemName: "speedometer")
                                .font(.title3)
                                .foregroundColor(Color(red: 1.0, green: 0.84, blue: 0.0))

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.25),
                                            Color.white.opacity(0.1)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                                .blur(radius: 0.5)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.horizontal, 20)
                }

                Spacer()

                // Sign Out Button - Updated with Warren Buffett styling
                Button(action: {
                    Task {
                        await authManager.signOut()
                    }
                }) {
                    Text("Sign Out")
                        .font(.headline)
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.15),
                                            Color.white.opacity(0.05)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.red, lineWidth: 1)
                                )
                                .blur(radius: 0.5)
                        )
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 30)
                }
            }
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingSubscriptionView) {
                EnhancedSubscriptionView()
            }
            .sheet(isPresented: $showingThemeSettings) {
                ThemeSettingsView()
                    .environmentObject(themeManager)
            }
            .sheet(isPresented: $showingPerformanceMonitor) {
                PerformanceMonitorView()
            }
        }
    }
}



#Preview {
    MainTabView()
        .environmentObject(AuthManager())
        .environmentObject(UserManager())
        .environmentObject(SubscriptionManager())
}
