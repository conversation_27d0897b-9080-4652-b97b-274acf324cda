//
//  MainTabView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Native SwiftUI TabView Implementation

struct MainTabView: View {
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var realTradingManager: RealTradingManager
    @EnvironmentObject var analyticsManager: AnalyticsManager
    @EnvironmentObject var paymentManager: PaymentManager
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.theme) var theme
    @State private var selectedTab = 0
    
    init() {
        setupTabBarAppearance()
    }

    var body: some View {
        TabView(selection: $selectedTab) {
            // Vibe Hub Tab
            DashboardView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("Vibe Hub")
                }
                .tag(0)
            
            // Bag Securing Tab
            RealTradingView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "chart.line.uptrend.xyaxis" : "chart.line.uptrend.xyaxis")
                    Text("Secure Bag")
                }
                .tag(1)
            
            // Flex Stats Tab
            BuffettAnalyticsView()
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "chart.bar.xaxis" : "chart.bar")
                    Text("Flex Stats")
                }
                .tag(2)
            
            // Squad Tab
            EnhancedSocialView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "person.3.fill" : "person.3")
                    Text("Squad")
                }
                .tag(3)

            // Bestie AI Tab
            BuffettChatView()
                .tabItem {
                    Image(systemName: selectedTab == 4 ? "brain.head.profile" : "brain")
                    Text("AI Bestie")
                }
                .tag(4)

            // Profile Tab
            ProfileView()
                .tabItem {
                    Image(systemName: selectedTab == 5 ? "person.fill" : "person")
                    Text("Profile")
                }
                .tag(5)
        }
        .accentColor(Color(red: 1.0, green: 0.84, blue: 0.0)) // Warren Buffett gold
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()

        // Warren Buffett inspired tab bar background - consistent with design system
        appearance.backgroundColor = UIColor(red: 0.1, green: 0.2, blue: 0.4, alpha: 0.95)

        // Warren Buffett inspired gold accent for selected items
        let goldColor = UIColor(red: 1.0, green: 0.84, blue: 0.0, alpha: 1.0)
        appearance.selectionIndicatorTintColor = goldColor

        // Normal (unselected) item styling
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.white.withAlphaComponent(0.6)
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.white.withAlphaComponent(0.6)
        ]

        // Selected item styling with Warren Buffett gold
        appearance.stackedLayoutAppearance.selected.iconColor = goldColor
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: goldColor
        ]

        // Apply consistent appearance across all tab bar states
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance

        // Ensure consistent tint color
        UITabBar.appearance().tintColor = goldColor
        UITabBar.appearance().unselectedItemTintColor = UIColor.white.withAlphaComponent(0.6)
    }
}

// MARK: - Preview

#Preview {
    MainTabView()
        .environmentObject(UserManager())
        .environmentObject(SubscriptionManager())
        .environmentObject(RealTradingManager())
        .environmentObject(AnalyticsManager())
        .environmentObject(PaymentManager(subscriptionManager: SubscriptionManager()))
        .environmentObject(ThemeManager())
}
