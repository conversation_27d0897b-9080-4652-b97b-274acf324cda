//
//  AuthView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct AuthView: View {
    @EnvironmentObject var authManager: AuthManager
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var themeManager: ThemeManager
    @State private var isSignUp = false
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var username = ""
    @State private var showForgotPassword = false
    @State private var showPreferences = false
    @State private var showDeveloperSettings = false

    var body: some View {
        ZStack {
            // Background
            themeManager.colors.background
            .ignoresSafeArea()

            ScrollView {
                VStack(spacing: 32) {
                    // Header
                    VStack(spacing: 16) {
                        Text("💎")
                            .font(.system(size: 60))

                        Text("WealthVibe")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(isSignUp ? "Join the financial revolution" : "Welcome back, vibe checker!")
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 60)

                    // Form
                    VStack(spacing: 20) {
                        if isSignUp {
                            CustomTextField(
                                text: $username,
                                placeholder: "Username",
                                icon: "person.fill"
                            )
                        }

                        CustomTextField(
                            text: $email,
                            placeholder: "Email",
                            icon: "envelope.fill",
                            keyboardType: .emailAddress
                        )

                        CustomTextField(
                            text: $password,
                            placeholder: "Password",
                            icon: "lock.fill",
                            isSecure: true
                        )

                        if isSignUp {
                            CustomTextField(
                                text: $confirmPassword,
                                placeholder: "Confirm Password",
                                icon: "lock.fill",
                                isSecure: true
                            )
                        }
                    }
                    .padding(.horizontal, 32)

                    // Error message
                    if let errorMessage = authManager.errorMessage {
                        Text(errorMessage)
                            .font(.caption)
                            .foregroundColor(.red)
                            .padding(.horizontal, 32)
                    }

                    // Action button
                    VStack(spacing: 16) {
                        Button(action: handleAuth) {
                            HStack {
                                if authManager.isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .black))
                                        .scaleEffect(0.8)
                                } else {
                                    Text(isSignUp ? "Create Account" : "Sign In")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                }
                            }
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity)
                            .frame(height: 56)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(Color.white)
                            )
                        }
                        .disabled(authManager.isLoading || !isFormValid)
                        .opacity(isFormValid ? 1.0 : 0.6)
                        .buttonStyle(PlainButtonStyle())

                        // Toggle auth mode
                        Button(action: {
                            withAnimation {
                                isSignUp.toggle()
                                clearForm()
                            }
                        }) {
                            HStack {
                                Text(isSignUp ? "Already have an account?" : "Don't have an account?")
                                    .foregroundColor(.white.opacity(0.8))
                                Text(isSignUp ? "Sign In" : "Sign Up")
                                    .fontWeight(.semibold)
                                    .foregroundColor(.white)
                            }
                            .font(.subheadline)
                        }
                        .buttonStyle(PlainButtonStyle())

                        // Forgot password
                        if !isSignUp {
                            Button(action: {
                                showForgotPassword = true
                            }) {
                                Text("Forgot Password?")
                                    .font(.subheadline)
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            .buttonStyle(PlainButtonStyle())
                        }


                    }
                    .padding(.horizontal, 32)

                    // Developer Settings and Quick Login (Debug only)
                    #if DEBUG
                    if authManager.isDevelopmentMode {
                        VStack(spacing: 12) {
                            // Quick login button when mock auth is enabled
                            if authManager.useMockAuth {
                                Button(action: {
                                    authManager.quickDevLogin()
                                }) {
                                    HStack {
                                        Image(systemName: "bolt.fill")
                                        Text("Quick Dev Login")
                                    }
                                    .font(.caption)
                                    .foregroundColor(.yellow)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.yellow.opacity(0.2))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 8)
                                                    .stroke(Color.yellow.opacity(0.5), lineWidth: 1)
                                            )
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                            }

                            // Developer settings button
                            Button(action: {
                                showDeveloperSettings = true
                            }) {
                                HStack {
                                    Image(systemName: "gearshape.fill")
                                    Text("Developer Settings")
                                }
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.white.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding(.horizontal, 32)
                        .padding(.top, 16)
                    }
                    #endif

                    Spacer(minLength: 50)
                }
            }
        }
        .sheet(isPresented: $showForgotPassword) {
            ForgotPasswordView()
        }
        .sheet(isPresented: $showPreferences) {
            PreferencesSetupView()
        }
        .sheet(isPresented: $showDeveloperSettings) {
            DeveloperSettingsView()
        }
        .onTapGesture {
            hideKeyboard()
        }
    }

    private var isFormValid: Bool {
        if isSignUp {
            return authManager.validateEmail(email) &&
                   authManager.validatePassword(password).isValid &&
                   authManager.validateUsername(username).isValid &&
                   password == confirmPassword
        } else {
            return authManager.validateEmail(email) && !password.isEmpty
        }
    }

    private func handleAuth() {
        hideKeyboard()
        authManager.clearError()

        Task {
            if isSignUp {
                await authManager.signUp(email: email, password: password, username: username)
                if authManager.isAuthenticated {
                    showPreferences = true
                }
            } else {
                await authManager.signIn(email: email, password: password)
            }
        }
    }

    private func clearForm() {
        email = ""
        password = ""
        confirmPassword = ""
        username = ""
        authManager.clearError()
    }

    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    private func testDatabaseConnection() {
        Task {
            let result = await AuthTestHelper.shared.testDatabaseConnection()
            await MainActor.run {
                // Show result in error message area temporarily
                if result.success {
                    authManager.errorMessage = "✅ Database connection successful!"
                } else {
                    authManager.errorMessage = result.message
                }
            }

            // Clear the message after 3 seconds
            try? await Task.sleep(nanoseconds: 3_000_000_000)
            await MainActor.run {
                authManager.errorMessage = nil
            }
        }
    }

    private func testAuthAPI() {
        Task {
            // Test the Supabase auth endpoint directly
            let url = URL(string: "https://mcrbwwkltigjawnlunlh.supabase.co/auth/v1/signup")!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs", forHTTPHeaderField: "apikey")

            let body = [
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            ]

            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: body)
                let (data, response) = try await URLSession.shared.data(for: request)

                if let httpResponse = response as? HTTPURLResponse {
                    let responseString = String(data: data, encoding: .utf8) ?? "No response data"
                    await MainActor.run {
                        if httpResponse.statusCode == 200 {
                            authManager.errorMessage = "✅ Auth API working! Status: \(httpResponse.statusCode)"
                        } else {
                            authManager.errorMessage = "❌ Auth API Status: \(httpResponse.statusCode)"
                        }
                    }
                    print("🔍 Auth API Response: \(httpResponse.statusCode)")
                    print("🔍 Response: \(responseString)")
                }
            } catch {
                await MainActor.run {
                    authManager.errorMessage = "❌ Auth API Error: \(error.localizedDescription)"
                }
                print("🔴 Auth API Error: \(error)")
            }

            // Clear the message after 5 seconds
            try? await Task.sleep(nanoseconds: 5_000_000_000)
            await MainActor.run {
                authManager.errorMessage = nil
            }
        }
    }

    private func testMyCredentials() {
        guard !email.isEmpty && !password.isEmpty else {
            authManager.errorMessage = "Enter your email and password first"
            return
        }

        Task {
            // Test sign-in with your actual credentials
            do {
                let authResult = try await SupabaseService.shared.signIn(email: email, password: password)
                await MainActor.run {
                    authManager.errorMessage = "✅ Sign-in successful! User ID: \(authResult.user.id)"
                }
                print("🔍 Successful auth result: \(authResult)")
            } catch {
                await MainActor.run {
                    authManager.errorMessage = "❌ Sign-in failed: \(error.localizedDescription)"
                }
                print("🔴 Sign-in error: \(error)")
            }

            // Clear the message after 5 seconds
            try? await Task.sleep(nanoseconds: 5_000_000_000)
            await MainActor.run {
                authManager.errorMessage = nil
            }
        }
    }

    private func directSignIn() {
        guard !email.isEmpty && !password.isEmpty else {
            authManager.errorMessage = "Enter your email and password first"
            return
        }

        Task {
            // Direct API call bypassing complex JSON parsing
            let url = URL(string: "https://mcrbwwkltigjawnlunlh.supabase.co/auth/v1/token?grant_type=password")!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs", forHTTPHeaderField: "apikey")

            let body = [
                "email": email,
                "password": password
            ]

            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: body)
                let (data, response) = try await URLSession.shared.data(for: request)

                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        // Parse response manually to avoid JSON decoding issues
                        if let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                           let userDict = jsonData["user"] as? [String: Any],
                           let idString = userDict["id"] as? String,
                           let userEmail = userDict["email"] as? String,
                           let userId = UUID(uuidString: idString) {

                            // Create user and sign in directly
                            let user = User(
                                id: userId,
                                email: userEmail,
                                username: userEmail.components(separatedBy: "@").first ?? "user"
                            )

                            await MainActor.run {
                                authManager.currentUser = user
                                authManager.isAuthenticated = true
                                authManager.errorMessage = "✅ Successfully signed in!"
                            }

                            print("🎉 Direct sign-in successful!")
                        } else {
                            await MainActor.run {
                                authManager.errorMessage = "✅ Auth OK but couldn't parse user data"
                            }
                        }
                    } else {
                        let responseString = String(data: data, encoding: .utf8) ?? "No response"
                        await MainActor.run {
                            authManager.errorMessage = "❌ Status: \(httpResponse.statusCode)"
                        }
                        print("🔴 Sign-in failed: \(httpResponse.statusCode) - \(responseString)")
                    }
                }
            } catch {
                await MainActor.run {
                    authManager.errorMessage = "❌ Error: \(error.localizedDescription)"
                }
                print("🔴 Direct sign-in error: \(error)")
            }
        }
    }
}

struct CustomTextField: View {
    @Binding var text: String
    let placeholder: String
    let icon: String
    var keyboardType: UIKeyboardType = .default
    var isSecure: Bool = false

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .foregroundColor(.white.opacity(0.7))
                .frame(width: 20)

            if isSecure {
                SecureField(placeholder, text: $text)
                    .foregroundColor(.white)
                    .keyboardType(keyboardType)
            } else {
                TextField(placeholder, text: $text)
                    .foregroundColor(.white)
                    .keyboardType(keyboardType)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct ForgotPasswordView: View {
    @EnvironmentObject var authManager: AuthManager
    @Environment(\.dismiss) var dismiss
    @State private var email = ""
    @State private var isEmailSent = false

    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                VStack(spacing: 16) {
                    Text("🔐")
                        .font(.system(size: 60))

                    Text("Reset Password")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("Enter your email and we'll send you a reset link")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)

                if !isEmailSent {
                    VStack(spacing: 20) {
                        CustomTextField(
                            text: $email,
                            placeholder: "Email",
                            icon: "envelope.fill",
                            keyboardType: .emailAddress
                        )

                        Button(action: {
                            Task {
                                await authManager.resetPassword(email: email)
                                if authManager.errorMessage == nil {
                                    isEmailSent = true
                                }
                            }
                        }) {
                            Text("Send Reset Link")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 56)
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(Color.blue)
                                )
                        }
                        .disabled(!authManager.validateEmail(email) || authManager.isLoading)
                        .buttonStyle(PlainButtonStyle())
                    }
                } else {
                    VStack(spacing: 20) {
                        Text("✅")
                            .font(.system(size: 60))

                        Text("Check your email!")
                            .font(.title2)
                            .fontWeight(.semibold)

                        Text("We've sent a password reset link to \(email)")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }

                Spacer()
            }
            .padding(.horizontal, 32)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    AuthView()
        .environmentObject(AuthManager())
        .environmentObject(UserManager())
}
