//
//  GamificationSupportViews.swift
//  VibeFinance
//
//  Created by AI Assistant on 7/9/25.
//

import SwiftUI

// MARK: - Achievement Categories View
struct AchievementCategoriesView: View {
    let achievements: [GamificationAchievement]
    let onAchievementTap: (GamificationAchievement) -> Void
    @State private var selectedCategory: AchievementCategory = .learning
    
    private var filteredAchievements: [GamificationAchievement] {
        achievements.filter { $0.category == selectedCategory }
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // Category selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(AchievementCategory.allCases, id: \.self) { category in
                        CategoryButton(
                            category: category,
                            isSelected: selectedCategory == category,
                            count: achievements.filter { $0.category == category }.count
                        ) {
                            selectedCategory = category
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
            
            // Achievements grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(filteredAchievements, id: \.id) { achievement in
                    AchievementCard(achievement: achievement) {
                        onAchievementTap(achievement)
                    }
                }
            }
        }
    }
}

struct CategoryButton: View {
    let category: AchievementCategory
    let isSelected: Bool
    let count: Int
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                Text(category.displayName)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text("\(count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .foregroundColor(isSelected ? .white : .primary)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? category.color : Color.gray.opacity(0.2))
            )
        }
    }
}

struct AchievementCard: View {
    let achievement: GamificationAchievement
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Achievement icon
                ZStack {
                    Circle()
                        .fill(achievement.rarity.color.opacity(0.2))
                        .frame(width: 60, height: 60)
                    
                    Image(systemName: achievement.icon)
                        .font(.title2)
                        .foregroundColor(achievement.rarity.color)
                    
                    if achievement.isUnlocked {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                            .background(Circle().fill(.white))
                            .offset(x: 20, y: -20)
                    }
                }
                
                VStack(spacing: 4) {
                    Text(achievement.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text(achievement.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(3)
                    
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.orange)
                            .font(.caption)
                        Text("\(achievement.xpReward) XP")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(achievement.rarity.color.opacity(0.3), lineWidth: 1)
                    )
            )
            .opacity(achievement.isUnlocked ? 1.0 : 0.6)
        }
    }
}

// MARK: - Unlockable Themes View
struct UnlockableThemesView: View {
    let themes: [UnlockableTheme]
    let onThemeSelect: (UnlockableTheme) -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Unlockable Themes 🎨")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                ForEach(themes, id: \.id) { theme in
                    ThemeCard(theme: theme) {
                        onThemeSelect(theme)
                    }
                }
            }
        }
    }
}

struct ThemeCard: View {
    let theme: UnlockableTheme
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Theme preview
                HStack(spacing: 4) {
                    ForEach(theme.previewColors, id: \.self) { color in
                        RoundedRectangle(cornerRadius: 4)
                            .fill(color)
                            .frame(height: 30)
                    }
                }
                
                VStack(spacing: 4) {
                    HStack {
                        Text(theme.name)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        if theme.isPremium {
                            Image(systemName: "crown.fill")
                                .foregroundColor(.orange)
                                .font(.caption)
                        }
                    }
                    
                    Text(theme.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                    
                    Text(theme.unlockRequirement)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(theme.isUnlocked ? .green : .orange)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill((theme.isUnlocked ? .green : .orange).opacity(0.2))
                        )
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(theme.isUnlocked ? .green : .gray.opacity(0.3), lineWidth: 1)
                    )
            )
            .opacity(theme.isUnlocked ? 1.0 : 0.7)
        }
    }
}

// MARK: - Premium Rewards View
struct PremiumRewardsView: View {
    let rewards: [PremiumReward]
    let canAccess: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Premium Rewards 💎")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if !canAccess {
                    Text("Pro Only")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.orange.opacity(0.2))
                        )
                }
            }
            
            LazyVStack(spacing: 12) {
                ForEach(rewards, id: \.id) { reward in
                    PremiumRewardCard(reward: reward, canAccess: canAccess)
                }
            }
        }
    }
}

struct PremiumRewardCard: View {
    let reward: PremiumReward
    let canAccess: Bool
    
    var body: some View {
        HStack {
            Image(systemName: reward.icon)
                .foregroundColor(.orange)
                .font(.title2)
                .frame(width: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(reward.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(reward.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                HStack {
                    Text("Duration: \(reward.duration)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("\(reward.cost) XP")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }
            }
            
            Spacer()
            
            Button("Redeem") {
                // Handle reward redemption
            }
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(canAccess ? .orange : .gray)
            )
            .disabled(!canAccess)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
        .opacity(canAccess ? 1.0 : 0.6)
    }
}

// MARK: - Leaderboard View
struct LeaderboardView: View {
    let leaderboard: [GamificationLeaderboardEntry]
    let userRank: Int
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Global Leaderboard 🏆")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text("Weekly")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            LazyVStack(spacing: 8) {
                ForEach(leaderboard, id: \.id) { entry in
                    LeaderboardRow(entry: entry)
                }
            }
        }
    }
}

struct LeaderboardRow: View {
    let entry: GamificationLeaderboardEntry
    
    var body: some View {
        HStack {
            // Rank
            Text("#\(entry.rank)")
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 40, alignment: .leading)
            
            // Username
            Text(entry.username)
                .font(.subheadline)
                .fontWeight(entry.isCurrentUser ? .bold : .medium)
                .foregroundColor(entry.isCurrentUser ? .purple : .primary)
            
            Spacer()
            
            // Level and XP
            VStack(alignment: .trailing, spacing: 2) {
                Text("Level \(entry.level)")
                    .font(.caption)
                    .fontWeight(.semibold)
                
                Text("\(entry.xp) XP")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(entry.isCurrentUser ? .purple.opacity(0.1) : .ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(entry.isCurrentUser ? .purple : .clear, lineWidth: 1)
                )
        )
    }
    
    private var rankColor: Color {
        switch entry.rank {
        case 1: return .yellow
        case 2: return .gray
        case 3: return .orange
        default: return .secondary
        }
    }
}

// MARK: - Achievement Detail View
struct GamificationAchievementDetailView: View {
    let achievement: GamificationAchievement
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Achievement icon
                ZStack {
                    Circle()
                        .fill(achievement.rarity.color.opacity(0.2))
                        .frame(width: 120, height: 120)
                    
                    Image(systemName: achievement.icon)
                        .font(.system(size: 50))
                        .foregroundColor(achievement.rarity.color)
                    
                    if achievement.isUnlocked {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.title)
                            .background(Circle().fill(.white))
                            .offset(x: 40, y: -40)
                    }
                }
                
                VStack(spacing: 12) {
                    Text(achievement.title)
                        .font(.title)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)
                    
                    Text(achievement.description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    HStack {
                        Text(achievement.rarity.rawValue.capitalized)
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(achievement.rarity.color)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(achievement.rarity.color.opacity(0.2))
                            )
                        
                        Text("\(achievement.xpReward) XP Reward")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(.orange.opacity(0.2))
                            )
                    }
                    
                    if let unlockedAt = achievement.unlockedAt {
                        Text("Unlocked on \(unlockedAt.formatted(date: .abbreviated, time: .omitted))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
            }
            .padding(24)
            .navigationTitle("Achievement")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Theme Selector View
struct ThemeSelectorView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var gamificationManager = GamificationManager()
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    ForEach(gamificationManager.availableThemes, id: \.id) { theme in
                        ThemeCard(theme: theme) {
                            // Handle theme selection
                        }
                    }
                }
                .padding(16)
            }
            .navigationTitle("Select Theme")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
