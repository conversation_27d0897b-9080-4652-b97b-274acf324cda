//
//  BuffettSimulatorView.swift
//  VibeFinance - Warren Buffett Inspired Trading Simulator
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct BuffettSimulatorView: View {
    @State private var selectedTab = 0
    @State private var virtualBalance: Double = 100000.0
    @State private var portfolioValue: Double = 112847.50
    @State private var dailyChange: Double = 1247.50
    @State private var holdings: [BuffettHolding] = []
    @State private var watchlist: [BuffettStock] = []
    @State private var showingStockDetail = false
    @State private var selectedStock: BuffettStock?
    
    private let tabs = ["Portfolio", "Market", "Watchlist", "Performance"]
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // Portfolio Summary Card
                BuffettPortfolioSummaryCard(
                    virtualBalance: virtualBalance,
                    portfolioValue: portfolioValue,
                    dailyChange: dailyChange
                )
                .padding(.horizontal, 16)
                .padding(.top, 8)

                // Warren's Investment Wisdom
                BuffettInvestmentWisdomCard()
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                // Unified Tab Selector with Warren Buffett styling
                VibeTabSelector(
                    selectedTab: $selectedTab,
                    tabs: tabs
                )
                .padding(.horizontal, 16)
                .padding(.top, 8)

                // Content based on selected tab - Fill remaining space
                TabView(selection: $selectedTab) {
                    // Portfolio Tab
                    BuffettPortfolioTab(holdings: holdings)
                        .tag(0)

                    // Market Tab
                    BuffettMarketTab(onStockSelected: { stock in
                        selectedStock = stock
                        showingStockDetail = true
                    })
                    .tag(1)

                    // Watchlist Tab
                    BuffettWatchlistTab(watchlist: watchlist)
                        .tag(2)

                    // Performance Tab
                    BuffettPerformanceTab()
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .frame(height: geometry.size.height - 320) // Adjust based on header content
            }
        }
        .onAppear {
            loadRealData()
        }
        .sheet(isPresented: $showingStockDetail) {
            if let stock = selectedStock {
                BuffettStockDetailView(stock: stock)
            }
        }
    }
    
    private func loadRealData() {
        Task {
            // Load real portfolio data from Supabase
            await loadRealPortfolio()

            // Load real watchlist data
            await loadRealWatchlist()

            // Load real market data for popular stocks
            await loadRealMarketData()
        }
    }

    private func loadRealPortfolio() async {
        // TODO: Load from SimulatorManager with real data
        await MainActor.run {
            // For now, start with empty portfolio - user will build it
            holdings = []
        }
    }

    private func loadRealWatchlist() async {
        // Load popular Warren Buffett-style stocks with real data
        let buffettStocks = ["BRK.A", "AAPL", "BAC", "KO", "AXP", "CVX", "OXY", "MCO"]
        var realWatchlist: [BuffettStock] = []

        for symbol in buffettStocks {
            if let stockData = await MarketService.shared.getStockData(symbol: symbol) {
                let buffettStock = BuffettStock(
                    symbol: stockData.symbol,
                    companyName: getCompanyName(for: symbol),
                    price: stockData.price,
                    change: stockData.change,
                    changePercent: stockData.changePercent,
                    marketCap: "N/A",
                    peRatio: 0.0,
                    buffettReasoning: getBuffettRating(for: symbol)
                )
                realWatchlist.append(buffettStock)
            }
        }

        await MainActor.run {
            watchlist = realWatchlist
        }
    }

    private func loadRealMarketData() async {
        // This will be handled by the MarketService
        // Real-time updates will come through the market service
    }

    private func getCompanyName(for symbol: String) -> String {
        let companyNames = [
            "BRK.A": "Berkshire Hathaway",
            "AAPL": "Apple Inc.",
            "BAC": "Bank of America",
            "KO": "Coca-Cola",
            "AXP": "American Express",
            "CVX": "Chevron",
            "OXY": "Occidental Petroleum",
            "MCO": "Moody's Corporation"
        ]
        return companyNames[symbol] ?? symbol
    }

    private func getBuffettRating(for symbol: String) -> String {
        let ratings = [
            "BRK.A": "⭐⭐⭐⭐⭐",
            "AAPL": "⭐⭐⭐⭐⭐",
            "BAC": "⭐⭐⭐⭐",
            "KO": "⭐⭐⭐⭐⭐",
            "AXP": "⭐⭐⭐⭐",
            "CVX": "⭐⭐⭐⭐",
            "OXY": "⭐⭐⭐",
            "MCO": "⭐⭐⭐⭐"
        ]
        return ratings[symbol] ?? "⭐⭐⭐"
    }
}

// MARK: - Portfolio Summary Card
struct BuffettPortfolioSummaryCard: View {
    let virtualBalance: Double
    let portfolioValue: Double
    let dailyChange: Double
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.theme) var theme

    var totalValue: Double {
        virtualBalance + portfolioValue
    }

    var totalGain: Double {
        portfolioValue - 100000.0 + virtualBalance
    }

    var totalGainPercent: Double {
        (totalGain / 100000.0) * 100
    }

    var body: some View {
        VStack(spacing: 16) {
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("💰 Virtual Portfolio")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)

                    Text("Learning Warren's way")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.8))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("$\(String(format: "%.2f", totalValue))")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onSurface)

                    Text("\(totalGain >= 0 ? "+" : "")$\(String(format: "%.2f", totalGain)) (\(String(format: "%.1f", totalGainPercent))%)")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(totalGain >= 0 ? theme.success : theme.error)
                }
            }

            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Cash Available")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                    Text("$\(String(format: "%.2f", virtualBalance))")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.onSurface)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("Invested")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                    Text("$\(String(format: "%.2f", portfolioValue))")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.accent)
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text("Today's Change")
                        .font(.caption)
                        .foregroundColor(theme.onSurface.opacity(0.7))
                    Text("\(dailyChange >= 0 ? "+" : "")$\(String(format: "%.2f", dailyChange))")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(dailyChange >= 0 ? theme.success : theme.error)
                }

                Spacer()
            }
        }
        .padding(20)
        .glassmorphicCard(theme: theme)
    }
}

// MARK: - Investment Wisdom Card
struct BuffettInvestmentWisdomCard: View {
    private let wisdomTips = [
        "🎯 Focus on companies you understand",
        "⏰ Time in the market beats timing the market",
        "🏰 Look for companies with economic moats",
        "📊 Buy when others are fearful",
        "💎 Hold wonderful companies forever"
    ]

    @State private var currentTip = ""
    @Environment(\.theme) var theme

    var body: some View {
        HStack(spacing: 12) {
            Text("💡")
                .font(.title2)

            Text(currentTip)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(theme.onSurface)

            Spacer()
        }
        .padding(12)
        .glassmorphicCard(theme: theme)
        .onAppear {
            currentTip = wisdomTips.randomElement() ?? wisdomTips[0]
        }
    }
}

// MARK: - Portfolio Tab
struct BuffettPortfolioTab: View {
    let holdings: [BuffettHolding]
    @Environment(\.theme) var theme

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                if holdings.isEmpty {
                    // Empty Portfolio State
                    VStack(spacing: 24) {
                        Image(systemName: "chart.pie")
                            .font(.system(size: 60))
                            .foregroundColor(theme.accent.opacity(0.7))

                        VStack(spacing: 12) {
                            Text("Start Your Investment Journey")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(theme.onSurface)

                            Text("\"The best time to plant a tree was 20 years ago. The second best time is now.\"")
                                .font(.body)
                                .foregroundColor(theme.onSurface.opacity(0.8))
                                .italic()
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 20)

                            Text("— Warren Buffett")
                                .font(.caption)
                                .foregroundColor(theme.accent)
                                .fontWeight(.semibold)
                        }

                        // Compact Tips Section
                        HStack(spacing: 12) {
                            Image(systemName: "lightbulb")
                                .foregroundColor(theme.accent)
                                .font(.title3)

                            VStack(alignment: .leading, spacing: 4) {
                                Text("Quick Start Tips")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(theme.onSurface)

                                Text("Browse Market → Find Quality Companies → Start Small")
                                    .font(.caption)
                                    .foregroundColor(theme.onSurface.opacity(0.8))
                            }

                            Spacer()

                            Button(action: {}) {
                                Image(systemName: "chevron.right")
                                    .foregroundColor(theme.accent)
                                    .font(.caption)
                            }
                        }
                        .padding(12)
                        .glassmorphicCard(theme: theme)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 40)
                } else {
                    ForEach(holdings) { holding in
                        BuffettHoldingCard(holding: holding)
                    }
                }
            }
            .padding()
        }
    }
}

// MARK: - Holding Card
struct BuffettHoldingCard: View {
    let holding: BuffettHolding
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.15),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(holding.symbol)
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text(holding.companyName)
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("$\(String(format: "%.2f", holding.currentPrice))")
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("\(holding.gainLoss >= 0 ? "+" : "")$\(String(format: "%.2f", holding.gainLoss))")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(holding.gainLoss >= 0 ? .green : .red)
                    }
                }
                
                HStack {
                    Text("\(holding.shares) shares")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                    
                    Spacer()
                    
                    Text("Value: $\(String(format: "%.2f", holding.totalValue))")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.yellow)
                }
            }
            .padding(12)
        }
    }
}

// MARK: - Market Tab
struct BuffettMarketTab: View {
    let onStockSelected: (BuffettStock) -> Void
    @State private var featuredStocks: [BuffettStock] = []

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                // Warren's Stock Picks Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("🎯 Warren's Favorite Types")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)

                    ForEach(featuredStocks) { stock in
                        BuffettStockCard(stock: stock) {
                            onStockSelected(stock)
                        }
                        .padding(.horizontal, 16)
                    }
                }
            }
            .padding(.vertical)
        }
        .onAppear {
            featuredStocks = BuffettStock.mockFeaturedStocks
        }
    }
}

// MARK: - Stock Card
struct BuffettStockCard: View {
    let stock: BuffettStock
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.15),
                                Color.white.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
                    .blur(radius: 0.5)

                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(stock.symbol)
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            Text(stock.companyName)
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                        }

                        Spacer()

                        VStack(alignment: .trailing, spacing: 4) {
                            Text("$\(String(format: "%.2f", stock.price))")
                                .font(.subheadline)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            Text("\(stock.change >= 0 ? "+" : "")\(String(format: "%.2f", stock.changePercent))%")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(stock.change >= 0 ? .green : .red)
                        }
                    }

                    // Warren's reasoning
                    if let reasoning = stock.buffettReasoning {
                        HStack(spacing: 8) {
                            Image(systemName: "lightbulb.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)

                            Text("Warren likes: \(reasoning)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.yellow.opacity(0.9))
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.yellow.opacity(0.1))
                                .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                        )
                    }

                    HStack {
                        Text("Market Cap: \(stock.marketCap)")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.6))

                        Spacer()

                        Text("P/E: \(String(format: "%.1f", stock.peRatio))")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.6))
                    }
                }
                .padding(12)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Watchlist Tab
struct BuffettWatchlistTab: View {
    let watchlist: [BuffettStock]

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(watchlist) { stock in
                    BuffettWatchlistCard(stock: stock)
                }
            }
            .padding()
        }
    }
}

// MARK: - Watchlist Card
struct BuffettWatchlistCard: View {
    let stock: BuffettStock

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.15),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
                .blur(radius: 0.5)

            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(stock.symbol)
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text(stock.companyName)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("$\(String(format: "%.2f", stock.price))")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("\(stock.change >= 0 ? "+" : "")\(String(format: "%.2f", stock.changePercent))%")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(stock.change >= 0 ? .green : .red)
                }

                Button(action: {}) {
                    Text("Buy")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.green)
                        )
                }
            }
            .padding(12)
        }
    }
}

// MARK: - Performance Tab
struct BuffettPerformanceTab: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Performance metrics would go here
                Text("📊 Performance Analytics")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Coming soon: Detailed performance analytics following Warren's metrics")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            .padding()
        }
    }
}

// MARK: - Data Models
struct BuffettHolding: Identifiable {
    let id = UUID()
    let symbol: String
    let companyName: String
    let shares: Int
    let averagePrice: Double
    let currentPrice: Double

    var totalValue: Double {
        Double(shares) * currentPrice
    }

    var gainLoss: Double {
        (currentPrice - averagePrice) * Double(shares)
    }

    static let mockHoldings: [BuffettHolding] = [
        BuffettHolding(symbol: "AAPL", companyName: "Apple Inc.", shares: 50, averagePrice: 150.00, currentPrice: 175.50),
        BuffettHolding(symbol: "KO", companyName: "Coca-Cola", shares: 100, averagePrice: 55.00, currentPrice: 62.30),
        BuffettHolding(symbol: "BRK.B", companyName: "Berkshire Hathaway", shares: 25, averagePrice: 280.00, currentPrice: 295.75),
        BuffettHolding(symbol: "JPM", companyName: "JPMorgan Chase", shares: 30, averagePrice: 140.00, currentPrice: 155.20)
    ]
}

struct BuffettStock: Identifiable {
    let id = UUID()
    let symbol: String
    let companyName: String
    let price: Double
    let change: Double
    let changePercent: Double
    let marketCap: String
    let peRatio: Double
    let buffettReasoning: String?

    static let mockFeaturedStocks: [BuffettStock] = [
        BuffettStock(
            symbol: "AAPL",
            companyName: "Apple Inc.",
            price: 175.50,
            change: 2.30,
            changePercent: 1.33,
            marketCap: "$2.8T",
            peRatio: 28.5,
            buffettReasoning: "Strong ecosystem and brand loyalty"
        ),
        BuffettStock(
            symbol: "KO",
            companyName: "The Coca-Cola Company",
            price: 62.30,
            change: 0.85,
            changePercent: 1.38,
            marketCap: "$270B",
            peRatio: 25.2,
            buffettReasoning: "Timeless brand with global reach"
        ),
        BuffettStock(
            symbol: "JNJ",
            companyName: "Johnson & Johnson",
            price: 168.45,
            change: -1.20,
            changePercent: -0.71,
            marketCap: "$445B",
            peRatio: 15.8,
            buffettReasoning: "Defensive healthcare with steady dividends"
        )
    ]

    static let mockWatchlist: [BuffettStock] = [
        BuffettStock(symbol: "MSFT", companyName: "Microsoft", price: 378.85, change: 4.20, changePercent: 1.12, marketCap: "$2.8T", peRatio: 32.1, buffettReasoning: nil),
        BuffettStock(symbol: "V", companyName: "Visa Inc.", price: 245.60, change: -2.10, changePercent: -0.85, marketCap: "$520B", peRatio: 31.5, buffettReasoning: nil),
        BuffettStock(symbol: "PG", companyName: "Procter & Gamble", price: 155.30, change: 1.45, changePercent: 0.94, marketCap: "$370B", peRatio: 24.8, buffettReasoning: nil)
    ]
}

// MARK: - Stock Detail View
struct BuffettStockDetailView: View {
    let stock: BuffettStock
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                LinearGradient(
                    colors: [
                        Color(red: 0.1, green: 0.2, blue: 0.4),
                        Color(red: 0.2, green: 0.3, blue: 0.5)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()

                VStack(spacing: 20) {
                    // Stock header
                    VStack(spacing: 8) {
                        Text(stock.symbol)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(stock.companyName)
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))

                        Text("$\(String(format: "%.2f", stock.price))")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }

                    Spacer()

                    // Buy/Sell buttons
                    HStack(spacing: 20) {
                        Button(action: { dismiss() }) {
                            Text("Buy")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.black)
                                .frame(maxWidth: .infinity)
                                .frame(height: 50)
                                .background(Color.green)
                                .cornerRadius(12)
                        }

                        Button(action: { dismiss() }) {
                            Text("Add to Watchlist")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 50)
                                .background(Color.blue.opacity(0.3))
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.blue, lineWidth: 2)
                                )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                }
                .padding()
            }
            .navigationTitle("Stock Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
            }
        }
    }
}

#Preview {
    BuffettSimulatorView()
}
