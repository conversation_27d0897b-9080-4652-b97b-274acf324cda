//
//  QuestCompletionView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct QuestCompletionView: View {
    let quest: Quest
    
    var body: some View {
        VStack(spacing: 24) {
            Text("🎉")
                .font(.system(size: 80))
            
            Text("Quest Complete!")
                .font(.title)
                .fontWeight(.bold)
            
            Text("You've successfully completed \(quest.title)")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            // Rewards Summary
            VStack(spacing: 16) {
                RewardCard(
                    icon: "star.fill",
                    title: "XP Earned",
                    value: "+\(quest.xpReward)",
                    color: .yellow
                )
                
                if quest.difficulty == .advanced {
                    RewardCard(
                        icon: "trophy.fill",
                        title: "Achievement",
                        value: "Expert Learner",
                        color: .gold
                    )
                }
            }
        }
    }
}

struct QuestCompletionCelebrationView: View {
    let quest: Quest
    @Environment(\.dismiss) private var dismiss
    @State private var showingConfetti = false
    @State private var scale: CGFloat = 0.5
    @State private var opacity: Double = 0
    
    var body: some View {
        ZStack {
            // Background
            Color.black.opacity(0.8)
                .ignoresSafeArea()
            
            VStack(spacing: 32) {
                // Celebration Animation
                VStack(spacing: 16) {
                    Text("🎉")
                        .font(.system(size: 100))
                        .scaleEffect(scale)
                        .opacity(opacity)
                    
                    Text("Quest Complete!")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .scaleEffect(scale)
                        .opacity(opacity)
                    
                    Text(quest.title)
                        .font(.title2)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .opacity(opacity)
                }
                
                // Rewards
                VStack(spacing: 20) {
                    RewardCelebrationCard(
                        icon: "star.fill",
                        title: "XP Earned",
                        value: "+\(quest.xpReward)",
                        color: .yellow
                    )
                    .opacity(opacity)
                    .offset(y: opacity == 1 ? 0 : 50)
                    
                    if quest.difficulty == .advanced {
                        RewardCelebrationCard(
                            icon: "trophy.fill",
                            title: "Achievement Unlocked",
                            value: "Expert Learner",
                            color: .gold
                        )
                        .opacity(opacity)
                        .offset(y: opacity == 1 ? 0 : 50)
                    }
                    
                    RewardCelebrationCard(
                        icon: "brain.head.profile",
                        title: "Knowledge Gained",
                        value: quest.category.displayName,
                        color: .purple
                    )
                    .opacity(opacity)
                    .offset(y: opacity == 1 ? 0 : 50)
                }
                
                // Continue Button
                Button(action: {
                    dismiss()
                }) {
                    Text("Continue Learning")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.white)
                        )
                }
                .padding(.horizontal, 40)
                .opacity(opacity)
                .scaleEffect(scale)
            }
            .padding()
            
            // Confetti Effect
            if showingConfetti {
                ConfettiView()
            }
        }
        .onAppear {
            startCelebrationAnimation()
        }
    }
    
    private func startCelebrationAnimation() {
        // Initial scale and fade in
        withAnimation(.spring(response: 0.8, dampingFraction: 0.6)) {
            scale = 1.0
            opacity = 1.0
        }
        
        // Start confetti after a delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            showingConfetti = true
        }
        
        // Stop confetti after animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            showingConfetti = false
        }
    }
}

// MARK: - Reward Cards
struct RewardCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(color.opacity(0.2))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                Text(value)
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct RewardCelebrationCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(color)
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(color.opacity(0.2))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            
            Spacer()
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Confetti Effect
struct ConfettiView: View {
    @State private var confettiPieces: [ConfettiPiece] = []
    
    var body: some View {
        ZStack {
            ForEach(confettiPieces, id: \.id) { piece in
                Rectangle()
                    .fill(piece.color)
                    .frame(width: piece.size.width, height: piece.size.height)
                    .rotationEffect(.degrees(piece.rotation))
                    .position(piece.position)
                    .opacity(piece.opacity)
            }
        }
        .onAppear {
            generateConfetti()
            animateConfetti()
        }
    }
    
    private func generateConfetti() {
        let colors: [Color] = [.red, .blue, .green, .yellow, .purple, .orange, .pink]
        
        for _ in 0..<50 {
            let piece = ConfettiPiece(
                id: UUID(),
                position: CGPoint(
                    x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                    y: -20
                ),
                color: colors.randomElement() ?? .blue,
                size: CGSize(
                    width: CGFloat.random(in: 4...8),
                    height: CGFloat.random(in: 8...16)
                ),
                rotation: Double.random(in: 0...360),
                opacity: 1.0
            )
            confettiPieces.append(piece)
        }
    }
    
    private func animateConfetti() {
        for i in 0..<confettiPieces.count {
            let delay = Double.random(in: 0...1)
            let duration = Double.random(in: 2...4)
            
            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                withAnimation(.linear(duration: duration)) {
                    confettiPieces[i].position.y = UIScreen.main.bounds.height + 50
                    confettiPieces[i].rotation += Double.random(in: 180...720)
                    confettiPieces[i].opacity = 0
                }
            }
        }
    }
}

struct ConfettiPiece {
    let id: UUID
    var position: CGPoint
    let color: Color
    let size: CGSize
    var rotation: Double
    var opacity: Double
}



#Preview {
    QuestCompletionCelebrationView(
        quest: Quest(
            title: "Stock Market Mastery",
            description: "Complete understanding of stock markets",
            category: .stocks,
            difficulty: .advanced,
            xpReward: 500,
            estimatedTime: 30,
            tasks: []
        )
    )
}
