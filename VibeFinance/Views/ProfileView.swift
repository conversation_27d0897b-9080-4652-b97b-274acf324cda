//
//  ProfileView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct ProfileView: View {
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var authManager: AuthManager
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.theme) var theme
    
    @State private var showingSubscriptionView = false
    @State private var showingPerformanceMonitor = false
    @State private var showingThemeSettings = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Beautiful Glassmorphic Background
                themeManager.currentTheme?.colors.backgroundColor ?? .black
                    .ignoresSafeArea()

                VStack(spacing: 20) {
                    profileHeader
                    userStats
                    subscriptionSection
                    menuItems
                    Spacer()
                }
                .padding(.horizontal, 20)
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingSubscriptionView) {
            EnhancedSubscriptionView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingPerformanceMonitor) {
            PerformanceMonitorView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingThemeSettings) {
            ThemeSettingsView()
                .environmentObject(themeManager)
        }
    }
    
    @ViewBuilder
    private var profileHeader: some View {
        VStack(spacing: 16) {
            // Profile Image
            Circle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 1.0, green: 0.84, blue: 0.0),
                            Color(red: 0.8, green: 0.6, blue: 0.0)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 80, height: 80)
                .overlay(
                    Image(systemName: "person.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.white)
                )
            
            // User Info
            VStack(spacing: 4) {
                Text("Warren Buffett")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("<EMAIL>")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(.top, 20)
    }
    
    @ViewBuilder
    private var userStats: some View {
        HStack(spacing: 20) {
            ProfileStatCard(title: "Portfolio", value: "$2.4M", icon: "chart.pie.fill", color: .green)
            ProfileStatCard(title: "Return", value: "+12.4%", icon: "arrow.up.right", color: .blue)
            ProfileStatCard(title: "Level", value: "12", icon: "star.fill", color: .yellow)
        }
    }
    
    @ViewBuilder
    private var subscriptionSection: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Subscription")
                    .font(.headline)
                    .foregroundColor(.white)
                Spacer()
            }
            
            Button(action: { showingSubscriptionView = true }) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("VibeFinance Pro")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                        
                        Text("All features unlocked")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    Image(systemName: "crown.fill")
                        .foregroundColor(Color(red: 1.0, green: 0.84, blue: 0.0))
                        .font(.title2)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.ultraThinMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    @ViewBuilder
    private var menuItems: some View {
        VStack(spacing: 12) {
            MenuItemRow(
                title: "Performance Monitor",
                icon: "speedometer",
                action: { showingPerformanceMonitor = true }
            )
            
            MenuItemRow(
                title: "Theme Settings",
                icon: "paintbrush.fill",
                action: { showingThemeSettings = true }
            )
            
            MenuItemRow(
                title: "Notifications",
                icon: "bell.fill",
                action: { /* Handle notifications */ }
            )
            
            MenuItemRow(
                title: "Privacy & Security",
                icon: "lock.fill",
                action: { /* Handle privacy */ }
            )
            
            MenuItemRow(
                title: "Help & Support",
                icon: "questionmark.circle.fill",
                action: { /* Handle support */ }
            )
            
            MenuItemRow(
                title: "Sign Out",
                icon: "arrow.right.square.fill",
                isDestructive: true,
                action: {
                    Task {
                        await authManager.signOut()
                    }
                }
            )
        }
    }
}

struct ProfileStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)

            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct MenuItemRow: View {
    let title: String
    let icon: String
    let isDestructive: Bool
    let action: () -> Void
    
    init(title: String, icon: String, isDestructive: Bool = false, action: @escaping () -> Void) {
        self.title = title
        self.icon = icon
        self.isDestructive = isDestructive
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isDestructive ? .red : .white)
                    .frame(width: 24)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(isDestructive ? .red : .white)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ProfileView()
        .environmentObject(UserManager())
        .environmentObject(SubscriptionManager())
        .environmentObject(AuthManager())
        .environmentObject(ThemeManager())
}
