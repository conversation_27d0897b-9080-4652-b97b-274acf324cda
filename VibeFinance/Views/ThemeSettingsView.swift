import SwiftUI

struct ThemeSettingsView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.theme) var theme
    @Environment(\.dismiss) var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(spacing: 16) {
                    Text("Appearance")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(theme.onBackground)

                    Text("VibeFinance uses a beautiful dark theme optimized for financial data")
                        .font(.subheadline)
                        .foregroundColor(theme.onBackground.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)
                .padding(.horizontal, 20)

                ScrollView {
                    VStack(spacing: 24) {
                        // Dark Mode Info
                        darkModeInfoSection

                        // Preview Section
                        previewSection
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 30)
                }

                Spacer()
            }
            .background(themeManager.colors.background.ignoresSafeArea())
            .navigationBarHidden(true)
            .overlay(
                // Close Button
                VStack {
                    HStack {
                        Spacer()
                        But<PERSON>(action: { dismiss() }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title2)
                                .foregroundColor(theme.onBackground.opacity(0.6))
                                .background(Circle().fill(theme.surface))
                        }
                        .padding(.trailing, 20)
                        .padding(.top, 20)
                    }
                    Spacer()
                }
            )
        }
    }
    
    private var darkModeInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Dark Mode")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(theme.onBackground)

            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "moon.fill")
                        .font(.title2)
                        .foregroundColor(theme.accent)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Optimized for Finance")
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(theme.onBackground)

                        Text("Dark mode reduces eye strain during long trading sessions and provides better contrast for financial data visualization.")
                            .font(.caption)
                            .foregroundColor(theme.onBackground.opacity(0.7))
                    }

                    Spacer()
                }

                HStack {
                    Image(systemName: "battery.100")
                        .font(.title2)
                        .foregroundColor(theme.success)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Battery Efficient")
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(theme.onBackground)

                        Text("Dark themes consume less battery on OLED displays, perfect for all-day trading.")
                            .font(.caption)
                            .foregroundColor(theme.onBackground.opacity(0.7))
                    }

                    Spacer()
                }
            }
            .padding(16)
            .glassmorphicCard(theme: themeManager.colors)
        }
    }
    
    private var previewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Preview")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(theme.onBackground)
            
            // Preview Card
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Portfolio Value")
                            .font(.caption)
                            .foregroundColor(theme.onSurface.opacity(0.7))
                        
                        Text("$125,847.32")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(theme.onSurface)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("+$2,847.32")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(theme.success)
                        
                        Text("+2.31%")
                            .font(.caption)
                            .foregroundColor(theme.success)
                    }
                }
                
                HStack(spacing: 12) {
                    Button("Buy") {
                        // Preview action
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(theme.accent)
                    .cornerRadius(8)

                    Button("Sell") {
                        // Preview action
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(theme.onSurface)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(theme.onSurface.opacity(0.3), lineWidth: 1)
                    )
                }
            }
            .padding(16)
            .glassmorphicCard(theme: themeManager.colors)
        }
    }
}





#Preview {
    ThemeSettingsView()
        .environmentObject(ThemeManager())
}
