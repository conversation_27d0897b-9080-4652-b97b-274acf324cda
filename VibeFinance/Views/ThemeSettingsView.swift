import SwiftUI

struct ThemeSettingsView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.dismiss) var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(spacing: 16) {
                    Text("Appearance")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(themeManager.currentTheme?.colors.foregroundColor ?? .white)

                    Text("VibeFinance uses a beautiful dark theme optimized for financial data")
                        .font(.subheadline)
                        .foregroundColor((themeManager.currentTheme?.colors.foregroundColor ?? .white).opacity(0.7))
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 20)
                .padding(.horizontal, 20)

                ScrollView {
                    VStack(spacing: 24) {
                        // Dark Mode Info
                        darkModeInfoSection

                        // Preview Section
                        previewSection
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 30)
                }

                Spacer()
            }
            .background((themeManager.currentTheme?.colors.backgroundColor ?? .black).ignoresSafeArea())
            .navigationBarHidden(true)
            .overlay(
                // Close Button
                VStack {
                    HStack {
                        Spacer()
                        Button(action: { dismiss() }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.title2)
                                .foregroundColor((themeManager.currentTheme?.colors.foregroundColor ?? .white).opacity(0.6))
                                .background(Circle().fill(Color.white.opacity(0.1)))
                        }
                        .padding(.trailing, 20)
                        .padding(.top, 20)
                    }
                    Spacer()
                }
            )
        }
    }
    
    private var darkModeInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Dark Mode")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(themeManager.currentTheme?.colors.foregroundColor ?? .white)

            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "moon.fill")
                        .font(.title2)
                        .foregroundColor(themeManager.currentTheme?.colors.accentColor ?? .purple)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Optimized for Finance")
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(themeManager.currentTheme?.colors.foregroundColor ?? .white)

                        Text("Dark mode reduces eye strain during long trading sessions and provides better contrast for financial data visualization.")
                            .font(.caption)
                            .foregroundColor((themeManager.currentTheme?.colors.foregroundColor ?? .white).opacity(0.7))
                    }

                    Spacer()
                }

                HStack {
                    Image(systemName: "battery.100")
                        .font(.title2)
                        .foregroundColor(themeManager.currentTheme?.colors.success ?? .green)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("Battery Efficient")
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(themeManager.currentTheme?.colors.foregroundColor ?? .white)

                        Text("Dark themes consume less battery on OLED displays, perfect for all-day trading.")
                            .font(.caption)
                            .foregroundColor((themeManager.currentTheme?.colors.foregroundColor ?? .white).opacity(0.7))
                    }

                    Spacer()
                }
            }
            .padding(16)
            .glassmorphicCard(theme: themeManager.currentTheme?.colors ?? ThemeColors.dark)
        }
    }
    
    private var previewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Preview")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(themeManager.currentTheme?.colors.foregroundColor ?? .white)
            
            // Preview Card
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Portfolio Value")
                            .font(.caption)
                            .foregroundColor((themeManager.currentTheme?.colors.onSurface ?? .white).opacity(0.7))

                        Text("$125,847.32")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(themeManager.currentTheme?.colors.onSurface ?? .white)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("+$2,847.32")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(themeManager.currentTheme?.colors.success ?? .green)

                        Text("+2.31%")
                            .font(.caption)
                            .foregroundColor(themeManager.currentTheme?.colors.success ?? .green)
                    }
                }
                
                HStack(spacing: 12) {
                    Button("Buy") {
                        // Preview action
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(themeManager.currentTheme?.colors.accentColor ?? .purple)
                    .cornerRadius(8)

                    Button("Sell") {
                        // Preview action
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(themeManager.currentTheme?.colors.onSurface ?? .white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 44)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke((themeManager.currentTheme?.colors.onSurface ?? .white).opacity(0.3), lineWidth: 1)
                    )
                }
            }
            .padding(16)
            .glassmorphicCard(theme: themeManager.currentTheme?.colors ?? ThemeColors.dark)
        }
    }
}





#Preview {
    ThemeSettingsView()
        .environmentObject(ThemeManager())
}
