//
//  EnhancedOnboardingView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import UserNotifications



struct EnhancedOnboardingView: View {
    @Binding var isOnboardingComplete: Bool
    @EnvironmentObject var userManager: UserManager
    @State private var currentStep = 0
    @State private var userPreferences = UserPreferences()
    @State private var showingPermissions = false
    
    private let totalSteps = 5
    
    var body: some View {
        VStack(spacing: 0) {
            // Progress Header
            OnboardingProgressHeader(
                currentStep: currentStep + 1,
                totalSteps: totalSteps
            )
            
            // Step Content
            TabView(selection: $currentStep) {
                // Step 1: Welcome
                OnboardingWelcomeStep()
                    .tag(0)
                
                // Step 2: Interests
                OnboardingInterestsStep(selectedInterests: $userPreferences.interests)
                    .tag(1)
                
                // Step 3: Goals
                OnboardingGoalsStep(selectedGoals: $userPreferences.goals)
                    .tag(2)
                
                // Step 4: Experience Level
                OnboardingExperienceStep(experienceLevel: $userPreferences.experienceLevel)
                    .tag(3)
                
                // Step 5: Permissions
                OnboardingPermissionsStep(showingPermissions: $showingPermissions)
                    .tag(4)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            
            // Navigation Buttons
            OnboardingNavigationButtons(
                currentStep: currentStep,
                totalSteps: totalSteps,
                canProceed: canProceedToNextStep,
                onPrevious: {
                    withAnimation {
                        currentStep = max(0, currentStep - 1)
                    }
                },
                onNext: {
                    if currentStep < totalSteps - 1 {
                        withAnimation {
                            currentStep += 1
                        }
                    } else {
                        completeOnboarding()
                    }
                }
            )
        }
        .sheet(isPresented: $showingPermissions) {
            OnboardingPermissionsSheet {
                completeOnboarding()
            }
        }
    }
    
    private var canProceedToNextStep: Bool {
        switch currentStep {
        case 0: return true // Welcome step
        case 1: return !userPreferences.interests.isEmpty
        case 2: return !userPreferences.goals.isEmpty
        case 3: return true // Allow all experience levels
        case 4: return true // Permissions step
        default: return false
        }
    }
    
    private func completeOnboarding() {
        Task {
            await userManager.updateUserPreferences(userPreferences)
            await MainActor.run {
                isOnboardingComplete = true
            }
        }
    }
}

// MARK: - Progress Header
struct OnboardingProgressHeader: View {
    let currentStep: Int
    let totalSteps: Int
    
    var progress: Double {
        Double(currentStep) / Double(totalSteps)
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Step \(currentStep) of \(totalSteps)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                .scaleEffect(y: 2)
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

// MARK: - Welcome Step
struct OnboardingWelcomeStep: View {
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 20) {
                Text("🚀")
                    .font(.system(size: 80))
                
                Text("Welcome to VibeFinance!")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text("Your AI-powered financial companion for the next generation")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 16) {
                FeatureHighlight(
                    icon: "brain.head.profile",
                    title: "AI-Powered Insights",
                    description: "Get personalized financial advice"
                )
                
                FeatureHighlight(
                    icon: "gamecontroller.fill",
                    title: "Gamified Learning",
                    description: "Earn XP and level up your finance skills"
                )
                
                FeatureHighlight(
                    icon: "person.3.fill",
                    title: "Social Investing",
                    description: "Join squads and invest with friends"
                )
            }
            
            Spacer()
        }
        .padding()
    }
}

// MARK: - Feature Highlight
struct FeatureHighlight: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.purple.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Interests Step
struct OnboardingInterestsStep: View {
    @Binding var selectedInterests: [String]
    
    private let interests = [
        "Stocks", "Crypto", "ETFs", "Real Estate", "Bonds",
        "Tech Stocks", "ESG Investing", "Dividend Stocks",
        "Growth Stocks", "Value Investing", "Day Trading",
        "Long-term Investing", "Retirement Planning"
    ]
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(spacing: 16) {
                    Text("💡")
                        .font(.system(size: 60))
                    
                    Text("What interests you?")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Select your investment interests so we can personalize your experience")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(interests, id: \.self) { interest in
                        InterestChip(
                            title: interest,
                            isSelected: selectedInterests.contains(interest)
                        ) {
                            if selectedInterests.contains(interest) {
                                selectedInterests.removeAll { $0 == interest }
                            } else {
                                selectedInterests.append(interest)
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

// MARK: - Interest Chip
struct InterestChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isSelected ? Color.purple : Color(.systemGray6))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Goals Step
struct OnboardingGoalsStep: View {
    @Binding var selectedGoals: [String]
    
    private let goals = [
        "Build Emergency Fund",
        "Save for Retirement",
        "Buy a House",
        "Pay Off Debt",
        "Start Investing",
        "Generate Passive Income",
        "Learn About Finance",
        "Build Wealth",
        "Save for Travel",
        "Start a Business"
    ]
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                VStack(spacing: 16) {
                    Text("🎯")
                        .font(.system(size: 60))
                    
                    Text("What are your goals?")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Tell us what you want to achieve so we can help you get there")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 40)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(goals, id: \.self) { goal in
                        InterestChip(
                            title: goal,
                            isSelected: selectedGoals.contains(goal)
                        ) {
                            if selectedGoals.contains(goal) {
                                selectedGoals.removeAll { $0 == goal }
                            } else {
                                selectedGoals.append(goal)
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

// MARK: - Experience Step
struct OnboardingExperienceStep: View {
    @Binding var experienceLevel: ExperienceLevel
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 16) {
                Text("📚")
                    .font(.system(size: 60))
                
                Text("What's your experience level?")
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text("This helps us customize your learning journey")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 16) {
                ForEach(ExperienceLevel.allCases, id: \.self) { level in
                    ExperienceLevelCard(
                        level: level,
                        isSelected: experienceLevel == level
                    ) {
                        experienceLevel = level
                    }
                }
            }
            .padding(.horizontal)
            
            Spacer()
        }
    }
}

// MARK: - Experience Level Card
struct ExperienceLevelCard: View {
    let level: ExperienceLevel
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: level.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : .purple)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(isSelected ? Color.purple : Color.purple.opacity(0.1))
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(level.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(level.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(isSelected ? .purple : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? Color.purple.opacity(0.1) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? Color.purple : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Permissions Step
struct OnboardingPermissionsStep: View {
    @Binding var showingPermissions: Bool
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 16) {
                Text("🔔")
                    .font(.system(size: 60))
                
                Text("Stay in the loop!")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Enable notifications to get quest reminders, market alerts, and squad updates")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 16) {
                PermissionCard(
                    icon: "bell.fill",
                    title: "Push Notifications",
                    description: "Get timely updates about your investments and quests"
                )
                
                PermissionCard(
                    icon: "location.fill",
                    title: "Location (Optional)",
                    description: "Find local investment events and meetups"
                )
            }
            .padding(.horizontal)
            
            Spacer()
        }
    }
}

// MARK: - Permission Card
struct PermissionCard: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.purple.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Navigation Buttons
struct OnboardingNavigationButtons: View {
    let currentStep: Int
    let totalSteps: Int
    let canProceed: Bool
    let onPrevious: () -> Void
    let onNext: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            if currentStep > 0 {
                Button("Previous") {
                    onPrevious()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.purple, lineWidth: 2)
                )
            }
            
            Button(currentStep == totalSteps - 1 ? "Get Started" : "Continue") {
                onNext()
            }
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(canProceed ? Color.purple : Color.gray)
            )
            .disabled(!canProceed)
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// MARK: - Permissions Sheet
struct OnboardingPermissionsSheet: View {
    let onComplete: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("🔔")
                    .font(.system(size: 80))
                
                Text("Enable Notifications")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Stay updated with your financial journey")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                Button("Enable Notifications") {
                    requestNotificationPermission()
                }
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.purple)
                )
                
                Button("Maybe Later") {
                    onComplete()
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            .padding()
            .navigationTitle("Permissions")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func requestNotificationPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                onComplete()
            }
        }
    }
}

#Preview {
    EnhancedOnboardingView(isOnboardingComplete: .constant(false))
        .environmentObject(UserManager())
}
