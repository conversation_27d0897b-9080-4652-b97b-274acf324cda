//
//  AdvancedAnalyticsView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct AdvancedAnalyticsView: View {
    @EnvironmentObject var analyticsManager: AnalyticsManager
    @EnvironmentObject var realTradingManager: RealTradingManager
    @EnvironmentObject var simulatorManager: SimulatorManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    
    @State private var selectedTab = 0
    @State private var selectedTimePeriod: TimePeriod = .oneMonth
    @State private var showingUpgradeSheet = false
    
    private let tabs = ["Overview", "Performance", "Risk", "Sectors", "Insights"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Analytics Dashboard
                VStack(spacing: 0) {
                    // Tab Selector
                    AnalyticsTabSelector(selectedTab: $selectedTab, tabs: tabs)

                    // Content
                    TabView(selection: $selectedTab) {
                        AnalyticsOverviewTab(selectedTimePeriod: $selectedTimePeriod)
                            .tag(0)

                        PerformanceAnalyticsTab(selectedTimePeriod: $selectedTimePeriod)
                            .tag(1)

                        RiskAnalyticsTab()
                            .tag(2)

                        SectorAnalyticsTab()
                            .tag(3)

                        InsightsAnalyticsTab()
                            .tag(4)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                }
            }
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Refresh") {
                        refreshAnalytics()
                    }
                }
            }
            .onAppear {
                refreshAnalytics()
            }
        }
    }
    
    private func refreshAnalytics() {
        Task {
            await analyticsManager.generatePortfolioAnalytics(
                portfolio: realTradingManager.realPortfolio,
                holdings: realTradingManager.realHoldings,
                transactions: realTradingManager.realTransactions
            )
        }
    }
}

// MARK: - Upgrade Required View
struct AnalyticsUpgradeView: View {
    let onUpgrade: () -> Void
    
    var body: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 20) {
                Image(systemName: "chart.bar.xaxis")
                    .font(.system(size: 80))
                    .foregroundColor(.purple)
                
                Text("Advanced Analytics")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Get professional-grade portfolio insights and risk analysis")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 16) {
                AnalyticsFeatureRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Performance Charts",
                    description: "Interactive charts with historical performance data"
                )
                
                AnalyticsFeatureRow(
                    icon: "shield.checkered",
                    title: "Risk Analysis",
                    description: "Value at Risk, Beta, and comprehensive risk metrics"
                )
                
                AnalyticsFeatureRow(
                    icon: "chart.pie.fill",
                    title: "Sector Analysis",
                    description: "Detailed sector allocation and diversification insights"
                )
                
                AnalyticsFeatureRow(
                    icon: "brain.head.profile",
                    title: "AI Insights",
                    description: "Personalized recommendations and market sentiment"
                )
                
                AnalyticsFeatureRow(
                    icon: "chart.bar.doc.horizontal",
                    title: "Benchmark Comparison",
                    description: "Compare your performance against market indices"
                )
            }
            .padding(.horizontal)
            
            Spacer()
            
            VStack(spacing: 16) {
                Button(action: onUpgrade) {
                    HStack {
                        Text("Upgrade to Pro")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Text("$19.99/month")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.purple)
                    )
                }
                
                Text("Unlock professional portfolio analytics and insights")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal)
        }
        .padding()
    }
}

// MARK: - Tab Selector
struct AnalyticsTabSelector: View {
    @Binding var selectedTab: Int
    let tabs: [String]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 0) {
                ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                    Button(action: {
                        selectedTab = index
                    }) {
                        VStack(spacing: 8) {
                            Text(tab)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(selectedTab == index ? .purple : .secondary)
                            
                            Rectangle()
                                .fill(selectedTab == index ? Color.purple : Color.clear)
                                .frame(height: 2)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .background(Color(.systemGray6))
    }
}

// MARK: - Overview Tab
struct AnalyticsOverviewTab: View {
    @Binding var selectedTimePeriod: TimePeriod
    @EnvironmentObject var analyticsManager: AnalyticsManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Time Period Selector
                TimePeriodSelector(selectedPeriod: $selectedTimePeriod)
                
                if let analytics = analyticsManager.portfolioAnalytics {
                    // Portfolio Summary
                    PortfolioSummaryCard(analytics: analytics)
                    
                    // Performance Chart
                    if !analyticsManager.performanceHistory.isEmpty {
                        PerformanceChartCard(
                            data: analyticsManager.performanceHistory,
                            timePeriod: selectedTimePeriod
                        )
                    }
                    
                    // Key Metrics Grid
                    KeyMetricsGrid(analytics: analytics)
                    
                    // Quick Insights
                    if let risk = analyticsManager.riskMetrics,
                       let sentiment = analyticsManager.marketSentiment {
                        QuickInsightsCard(
                            analytics: analytics,
                            risk: risk,
                            sentiment: sentiment
                        )
                    }
                } else if analyticsManager.isLoading {
                    LoadingView(message: "Analyzing portfolio...")
                } else {
                    EmptyAnalyticsView()
                }
            }
            .padding()
        }
    }
}

// MARK: - Performance Tab
struct PerformanceAnalyticsTab: View {
    @Binding var selectedTimePeriod: TimePeriod
    @EnvironmentObject var analyticsManager: AnalyticsManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Time Period Selector
                TimePeriodSelector(selectedPeriod: $selectedTimePeriod)
                
                if let analytics = analyticsManager.portfolioAnalytics {
                    // Performance Chart
                    if !analyticsManager.performanceHistory.isEmpty {
                        PerformanceChartCard(
                            data: analyticsManager.performanceHistory,
                            timePeriod: selectedTimePeriod
                        )
                    }
                    
                    // Benchmark Comparison
                    if let benchmark = analyticsManager.benchmarkComparison {
                        BenchmarkComparisonCard(comparison: benchmark)
                    }
                    
                    // Performance Metrics
                    PerformanceMetricsCard(analytics: analytics)
                    
                    // Returns Distribution
                    ReturnsDistributionCard(analytics: analytics)
                }
            }
            .padding()
        }
    }
}

// MARK: - Risk Tab
struct RiskAnalyticsTab: View {
    @EnvironmentObject var analyticsManager: AnalyticsManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                if let risk = analyticsManager.riskMetrics,
                   let analytics = analyticsManager.portfolioAnalytics {
                    
                    // Risk Overview
                    RiskOverviewCard(risk: risk)
                    
                    // Risk Metrics Detail
                    RiskMetricsDetailCard(risk: risk)
                    
                    // Concentration Risk
                    ConcentrationRiskCard(analytics: analytics)
                    
                    // Risk Recommendations
                    RiskRecommendationsCard(risk: risk, analytics: analytics)
                }
            }
            .padding()
        }
    }
}

// MARK: - Sectors Tab
struct SectorAnalyticsTab: View {
    @EnvironmentObject var analyticsManager: AnalyticsManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                if let sectors = analyticsManager.sectorAnalysis {
                    // Sector Allocation Chart
                    SectorAllocationChartCard(sectors: sectors)
                    
                    // Sector Performance
                    SectorPerformanceCard(sectors: sectors)
                    
                    // Diversification Score
                    DiversificationScoreCard(sectors: sectors)
                    
                    // Sector Recommendations
                    SectorRecommendationsCard(sectors: sectors)
                }
            }
            .padding()
        }
    }
}

// MARK: - Insights Tab
struct InsightsAnalyticsTab: View {
    @EnvironmentObject var analyticsManager: AnalyticsManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                if let sentiment = analyticsManager.marketSentiment {
                    // Market Sentiment
                    MarketSentimentCard(sentiment: sentiment)
                }
                
                // AI-Generated Insights
                AIInsightsCard()
                
                // Action Items
                ActionItemsCard()
                
                // Recent News
                if let sentiment = analyticsManager.marketSentiment {
                    RecentNewsCard(sentiment: sentiment)
                }
            }
            .padding()
        }
    }
}

// MARK: - Feature Row
struct AnalyticsFeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.purple)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(Color.purple.opacity(0.1))
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Empty State
struct EmptyAnalyticsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "chart.bar.xaxis")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Portfolio Data")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Connect your brokerage account or start investing to see analytics")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

#Preview {
    AdvancedAnalyticsView()
        .environmentObject(AnalyticsManager())
        .environmentObject(RealTradingManager())
        .environmentObject(SimulatorManager())
        .environmentObject(SubscriptionManager())
}
