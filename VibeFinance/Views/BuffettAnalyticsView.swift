//
//  BuffettAnalyticsView.swift
//  VibeFinance - <PERSON> Inspired Analytics
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Analytics Data Models

struct AnalyticsMetric: Identifiable {
    let id = UUID()
    let title: String
    let value: String
    let change: String
    let isPositive: Bool
    let icon: String
    let description: String
    let benchmark: String?
    let context: String
}

struct PortfolioInsight {
    let title: String
    let description: String
    let recommendation: String
    let priority: InsightPriority
    let buffettWisdom: String

    enum InsightPriority {
        case high, medium, low

        func color(for theme: ThemeColors) -> Color {
            switch self {
            case .high: return theme.error
            case .medium: return theme.warning
            case .low: return theme.primary
            }
        }
    }
}

struct BuffettAnalyticsView: View {
    @State private var selectedTab = 0
    @State private var selectedTimePeriod: TimePeriod = .month
    @State private var showingDetailedView = false
    @State private var selectedMetric: AnalyticsMetric?
    @State private var progressiveDisclosureLevel: DisclosureLevel = .overview
    @StateObject private var accessibilityManager = AccessibilityManager()
    @Environment(\.theme) var theme
    @Environment(\.theme) var theme

    private let tabs = ["Overview", "Performance", "Risk", "Holdings"]

    enum TimePeriod: String, CaseIterable {
        case day = "1D"
        case week = "1W"
        case month = "1M"
        case quarter = "3M"
        case year = "1Y"
        case all = "ALL"

        var displayName: String {
            switch self {
            case .day: return "1 Day"
            case .week: return "1 Week"
            case .month: return "1 Month"
            case .quarter: return "3 Months"
            case .year: return "1 Year"
            case .all: return "All Time"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Unified theme background
                theme.background
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Compact Analytics Header with Key Metrics
                    CompactAnalyticsHeader(
                        selectedTimePeriod: $selectedTimePeriod,
                        onMetricTap: { metric in
                            selectedMetric = metric
                            showingDetailedView = true
                        }
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    // Enhanced Tab Selector with Warren Buffett styling
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                                Button(action: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        selectedTab = index
                                    }
                                }) {
                                    VStack(spacing: 4) {
                                        Text(tab)
                                            .font(.system(size: 16, weight: selectedTab == index ? .bold : .medium))
                                            .foregroundColor(selectedTab == index ? theme.accent : theme.onSurface.opacity(0.7))

                                        // Active indicator
                                        Rectangle()
                                            .fill(selectedTab == index ? theme.accent : Color.clear)
                                            .frame(height: 2)
                                            .animation(.easeInOut(duration: 0.3), value: selectedTab)
                                    }
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                    .padding(.top, 8)

                    // Tab Content with Enhanced Analytics
                    TabView(selection: $selectedTab) {
                        // Overview Tab - Enhanced Data Visualization
                        EnhancedAnalyticsDataVisualization()
                            .tag(0)
                        
                        // Performance Tab - Enhanced with Benchmarks
                        EnhancedAnalyticsPerformanceTab(
                            timePeriod: selectedTimePeriod,
                            onBenchmarkTap: { benchmark in
                                // Handle benchmark tap
                            }
                        )
                        .tag(1)
                        
                        // Risk Tab - Enhanced with Risk Analysis
                        EnhancedAnalyticsRiskTab(
                            onRiskDetailTap: { riskDetail in
                                // Handle risk detail tap
                            }
                        )
                        .tag(2)
                        
                        // Holdings Tab - Enhanced with Position Analysis
                        EnhancedAnalyticsHoldingsTab(
                            onHoldingTap: { holding in
                                // Handle holding tap
                            }
                        )
                        .tag(3)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                }
            }
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingDetailedView) {
                if let metric = selectedMetric {
                    DetailedMetricView(metric: metric)
                        .environmentObject(accessibilityManager)
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
}

// MARK: - Supporting Views

struct CompactAnalyticsHeader: View {
    @Binding var selectedTimePeriod: BuffettAnalyticsView.TimePeriod
    let onMetricTap: (AnalyticsMetric) -> Void
    
    private let keyMetrics = [
        AnalyticsMetric(
            title: "Portfolio Value",
            value: "$127,450",
            change: "+12.5%",
            isPositive: true,
            icon: "chart.pie.fill",
            description: "Total portfolio value including all holdings",
            benchmark: "S&P 500: +8.2%",
            context: "Your portfolio is outperforming the market by 4.3%"
        ),
        AnalyticsMetric(
            title: "Monthly Return",
            value: "8.2%",
            change: "+2.1%",
            isPositive: true,
            icon: "arrow.up.right",
            description: "Monthly return on investment",
            benchmark: "Market Average: 6.1%",
            context: "Strong performance driven by tech holdings"
        ),
        AnalyticsMetric(
            title: "Risk Score",
            value: "6.8/10",
            change: "-0.3",
            isPositive: true,
            icon: "shield.fill",
            description: "Portfolio risk assessment",
            benchmark: nil,
            context: "Moderate risk level with good diversification"
        )
    ]
    
    var body: some View {
        VStack(spacing: 12) {
            // Time Period Selector
            HStack {
                Text("Analytics")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Spacer()

                // Time Period Pills - Constrained and Scrollable
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 6) {
                        ForEach(BuffettAnalyticsView.TimePeriod.allCases, id: \.self) { period in
                            Button(action: {
                                selectedTimePeriod = period
                            }) {
                                Text(period.rawValue)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(selectedTimePeriod == period ? .black : .white.opacity(0.8))
                                    .padding(.horizontal, 10)
                                    .padding(.vertical, 6)
                                    .background(
                                        RoundedRectangle(cornerRadius: 10)
                                            .fill(selectedTimePeriod == period ? VibeFinanceDesignSystem.Colors.accentGold : Color.white.opacity(0.1))
                                    )
                            }
                        }
                    }
                    .padding(.horizontal, 4)
                }
                .frame(maxWidth: 180) // Constrain the width to prevent overflow
            }
            
            // Key Metrics Carousel
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(keyMetrics) { metric in
                        Button(action: {
                            onMetricTap(metric)
                        }) {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: metric.icon)
                                        .font(.title3)
                                        .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                                    
                                    Spacer()
                                    
                                    HStack(spacing: 4) {
                                        Image(systemName: metric.isPositive ? "arrow.up.right" : "arrow.down.right")
                                            .font(.caption)
                                        
                                        Text(metric.change)
                                            .font(.caption)
                                            .fontWeight(.semibold)
                                    }
                                    .foregroundColor(metric.isPositive ? .green : .red)
                                }
                                
                                Text(metric.title)
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.8))
                                
                                Text(metric.value)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            }
                            .padding(12)
                            .frame(width: 140, height: 80)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.white.opacity(0.1))
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                    )
                            )
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
        }
    }
}

// MARK: - Enhanced Tab Views

struct EnhancedAnalyticsOverviewTab: View {
    let timePeriod: BuffettAnalyticsView.TimePeriod
    let onInsightTap: (PortfolioInsight) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Portfolio Performance Summary
                VStack(spacing: 12) {
                    HStack {
                        Text("Portfolio Performance")
                            .font(.headline)
                            .foregroundColor(.white)
                        Spacer()
                        Text(timePeriod.rawValue)
                            .font(.caption)
                            .foregroundColor(.yellow)
                    }

                    HStack(spacing: 20) {
                        VStack(alignment: .leading) {
                            Text("Total Value")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                            Text("$127,450")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        }

                        Spacer()

                        VStack(alignment: .trailing) {
                            Text("Monthly Return")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                            HStack {
                                Image(systemName: "arrow.up.right")
                                    .foregroundColor(.green)
                                Text("+12.5%")
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.green)
                            }
                        }
                    }
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                )

                // Key Metrics Grid
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    AnalyticsMetricCard(title: "Risk Score", value: "6.8", change: "+2.1%", isPositive: false, icon: "shield.fill")
                    AnalyticsMetricCard(title: "Sharpe Ratio", value: "1.42", change: "+0.3", isPositive: true, icon: "chart.line.uptrend.xyaxis")
                    AnalyticsMetricCard(title: "Beta", value: "0.85", change: "-0.05", isPositive: true, icon: "waveform.path.ecg")
                    AnalyticsMetricCard(title: "Volatility", value: "18.2%", change: "-1.2%", isPositive: true, icon: "chart.bar.fill")
                }

                // Warren's Insights
                VStack(alignment: .leading, spacing: 12) {
                    Text("Warren's Insights")
                        .font(.headline)
                        .foregroundColor(.white)

                    VStack(spacing: 8) {
                        AnalyticsInsightCard(
                            title: "Portfolio Concentration",
                            description: "Your top 3 holdings represent 65% of your portfolio",
                            recommendation: "Consider diversifying into more sectors",
                            priority: .medium
                        )

                        AnalyticsInsightCard(
                            title: "Value Opportunities",
                            description: "Several quality companies are trading below intrinsic value",
                            recommendation: "Review watchlist for potential additions",
                            priority: .high
                        )
                    }
                }
            }
            .padding(16)
        }
    }
}

struct EnhancedAnalyticsPerformanceTab: View {
    let timePeriod: BuffettAnalyticsView.TimePeriod
    let onBenchmarkTap: (String) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Performance Chart Placeholder
                VStack(spacing: 12) {
                    HStack {
                        Text("Performance vs Benchmarks")
                            .font(.headline)
                            .foregroundColor(.white)
                        Spacer()
                    }

                    // Chart placeholder
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.1))
                        .frame(height: 200)
                        .overlay(
                            VStack {
                                Image(systemName: "chart.line.uptrend.xyaxis")
                                    .font(.system(size: 40))
                                    .foregroundColor(.yellow.opacity(0.7))
                                Text("Performance Chart")
                                    .font(.subheadline)
                                    .foregroundColor(.white.opacity(0.7))
                                Text("Coming Soon")
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.5))
                            }
                        )
                }

                // Benchmark Comparison
                VStack(alignment: .leading, spacing: 12) {
                    Text("Benchmark Comparison")
                        .font(.headline)
                        .foregroundColor(.white)

                    VStack(spacing: 8) {
                        AnalyticsBenchmarkRow(name: "S&P 500", yourReturn: "+12.5%", benchmarkReturn: "+8.2%", outperforming: true)
                        AnalyticsBenchmarkRow(name: "NASDAQ", yourReturn: "+12.5%", benchmarkReturn: "+15.1%", outperforming: false)
                        AnalyticsBenchmarkRow(name: "Berkshire Hathaway", yourReturn: "+12.5%", benchmarkReturn: "+9.8%", outperforming: true)
                    }
                }
            }
            .padding(16)
        }
    }
}

struct EnhancedAnalyticsRiskTab: View {
    let onRiskDetailTap: (String) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Enhanced Risk Tab - Coming Soon")
                    .foregroundColor(.white)
            }
            .padding(16)
        }
    }
}

struct EnhancedAnalyticsHoldingsTab: View {
    let onHoldingTap: (String) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Enhanced Holdings Tab - Coming Soon")
                    .foregroundColor(.white)
            }
            .padding(16)
        }
    }
}

struct DetailedMetricView: View {
    let metric: AnalyticsMetric
    @EnvironmentObject var accessibilityManager: AccessibilityManager

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Metric Header
                    VStack(spacing: 8) {
                        Image(systemName: metric.icon)
                            .font(.system(size: 40))
                            .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)

                        Text(metric.title)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(metric.value)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        HStack(spacing: 4) {
                            Image(systemName: metric.isPositive ? "arrow.up.right" : "arrow.down.right")
                                .font(.title3)

                            Text(metric.change)
                                .font(.title3)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(metric.isPositive ? .green : .red)
                    }

                    // Description
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Description")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text(metric.description)
                            .font(.body)
                            .foregroundColor(.white.opacity(0.9))
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.1))
                    )

                    // Context
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Context")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text(metric.context)
                            .font(.body)
                            .foregroundColor(.white.opacity(0.9))
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.1))
                    )
                }
                .padding(16)
            }
            .background(VibeFinanceDesignSystem.Colors.primaryGradient)
            .navigationTitle("Metric Details")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - Supporting Components

struct AnalyticsMetricCard: View {
    let title: String
    let value: String
    let change: String
    let isPositive: Bool
    let icon: String

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.yellow)
                Spacer()
                HStack(spacing: 2) {
                    Image(systemName: isPositive ? "arrow.up" : "arrow.down")
                    Text(change)
                }
                .font(.caption)
                .foregroundColor(isPositive ? .green : .red)
            }

            Text(title)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))

            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.white)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct AnalyticsInsightCard: View {
    let title: String
    let description: String
    let recommendation: String
    let priority: PortfolioInsight.InsightPriority

    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(priority.color)
                .frame(width: 8, height: 8)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))

                Text(recommendation)
                    .font(.caption)
                    .foregroundColor(.yellow)
            }

            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

struct AnalyticsBenchmarkRow: View {
    let name: String
    let yourReturn: String
    let benchmarkReturn: String
    let outperforming: Bool

    var body: some View {
        HStack {
            Text(name)
                .font(.subheadline)
                .foregroundColor(.white)

            Spacer()

            VStack(alignment: .trailing, spacing: 2) {
                Text("You: \(yourReturn)")
                    .font(.caption)
                    .foregroundColor(.white)

                Text("Benchmark: \(benchmarkReturn)")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }

            Image(systemName: outperforming ? "arrow.up.circle.fill" : "arrow.down.circle.fill")
                .foregroundColor(outperforming ? .green : .red)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.white.opacity(0.05))
        )
    }
}

#Preview {
    BuffettAnalyticsView()
        .preferredColorScheme(.dark)
}
