//
//  PerformanceOptimizationSystem.swift
//  VibeFinance - Performance Optimization & Memory Management
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI
import Combine

// Using PerformanceMonitor from RenderingOptimizer.swift

// MARK: - Performance Monitoring (Removed - using from RenderingOptimizer.swift)

/*class PerformanceMonitor: ObservableObject {
    @Published var renderingMetrics = RenderingMetrics()
    @Published var memoryUsage = MemoryUsage()
    @Published var networkMetrics = NetworkMetrics()
    
    private var cancellables = Set<AnyCancellable>()
    private let updateInterval: TimeInterval = 1.0
    
    init() {
        startMonitoring()
    }
    
    private func startMonitoring() {
        Timer.publish(every: updateInterval, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateMetrics()
            }
            .store(in: &cancellables)
    }
    
    private func updateMetrics() {
        updateMemoryUsage()
        updateRenderingMetrics()
    }
    
    private func updateMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            memoryUsage.currentUsage = Double(info.resident_size) / 1024 / 1024 // MB
            memoryUsage.peakUsage = max(memoryUsage.peakUsage, memoryUsage.currentUsage)
        }
    }
    
    private func updateRenderingMetrics() {
        // Update FPS and frame time metrics
        renderingMetrics.lastUpdateTime = Date()
    }
}*/

struct RenderingMetrics {
    var fps: Double = 60.0
    var frameTime: Double = 16.67 // milliseconds
    var droppedFrames: Int = 0
    var lastUpdateTime = Date()
}

struct MemoryUsage {
    var currentUsage: Double = 0.0 // MB
    var peakUsage: Double = 0.0 // MB
    var cacheSize: Double = 0.0 // MB
}

struct NetworkMetrics {
    var requestCount: Int = 0
    var averageResponseTime: Double = 0.0 // milliseconds
    var failureRate: Double = 0.0 // percentage
}

// MARK: - Optimized Image Loading

class OptimizedImageLoader: ObservableObject {
    @Published var image: UIImage?
    @Published var isLoading = false
    @Published var error: Error?
    
    private static let cache = NSCache<NSString, UIImage>()
    private static let imageQueue = DispatchQueue(label: "image-processing", qos: .userInitiated)

    static func configureCache() {
        cache.countLimit = 100
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB
    }
    
    func loadImage(from url: URL, targetSize: CGSize? = nil) {
        Self.configureCache() // Configure cache if needed

        let cacheKey = NSString(string: url.absoluteString)

        // Check cache first
        if let cachedImage = Self.cache.object(forKey: cacheKey) {
            DispatchQueue.main.async {
                self.image = cachedImage
                self.isLoading = false
            }
            return
        }
        
        isLoading = true
        error = nil
        
        Self.imageQueue.async {
            do {
                let data = try Data(contentsOf: url)
                guard let originalImage = UIImage(data: data) else {
                    DispatchQueue.main.async {
                        self.error = ImageLoadingError.invalidData
                        self.isLoading = false
                    }
                    return
                }
                
                // Resize if target size is specified
                let finalImage = targetSize != nil ? 
                    originalImage.resized(to: targetSize!) : originalImage
                
                // Cache the processed image
                Self.cache.setObject(finalImage, forKey: cacheKey)
                
                DispatchQueue.main.async {
                    self.image = finalImage
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async {
                    self.error = error
                    self.isLoading = false
                }
            }
        }
    }
    
    enum ImageLoadingError: Error {
        case invalidData
        case networkError
    }
}

extension UIImage {
    func resized(to targetSize: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        return renderer.image { _ in
            self.draw(in: CGRect(origin: .zero, size: targetSize))
        }
    }
}

// MARK: - Optimized List Components

struct OptimizedLazyVStack<Content: View>: View {
    let spacing: CGFloat
    let content: Content
    
    @State private var visibleRange: Range<Int> = 0..<10
    @State private var scrollOffset: CGFloat = 0
    
    init(spacing: CGFloat = 8, @ViewBuilder content: () -> Content) {
        self.spacing = spacing
        self.content = content()
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: spacing) {
                content
            }
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .preference(key: ScrollOffsetPreferenceKey.self, value: geometry.frame(in: .named("scroll")).minY)
                }
            )
        }
        .coordinateSpace(name: "scroll")
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
            scrollOffset = value
            updateVisibleRange()
        }
    }
    
    private func updateVisibleRange() {
        // Calculate visible range based on scroll offset
        // This is a simplified implementation
        let itemHeight: CGFloat = 80 // Estimated item height
        let visibleItems = Int(UIScreen.main.bounds.height / itemHeight) + 2
        let startIndex = max(0, Int(-scrollOffset / itemHeight) - 1)
        visibleRange = startIndex..<(startIndex + visibleItems)
    }
}

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Memory-Efficient Data Loading

class DataManager<T>: ObservableObject {
    @Published var items: [T] = []
    @Published var loadingState: LoadingState<[T]> = .idle
    
    private let pageSize = 20
    private var currentPage = 0
    private var hasMoreData = true
    
    func loadInitialData() async {
        await MainActor.run {
            loadingState = .loading
        }
        
        do {
            let newItems = try await fetchData(page: 0, pageSize: pageSize)
            await MainActor.run {
                self.items = newItems
                self.currentPage = 0
                self.hasMoreData = newItems.count == pageSize
                self.loadingState = .loaded(newItems)
            }
        } catch {
            await MainActor.run {
                self.loadingState = .error(error)
            }
        }
    }
    
    func loadMoreData() async {
        guard hasMoreData && !loadingState.isLoading else { return }
        
        do {
            let newItems = try await fetchData(page: currentPage + 1, pageSize: pageSize)
            await MainActor.run {
                self.items.append(contentsOf: newItems)
                self.currentPage += 1
                self.hasMoreData = newItems.count == pageSize
            }
        } catch {
            // Handle error silently for pagination
            print("Failed to load more data: \(error)")
        }
    }
    
    private func fetchData(page: Int, pageSize: Int) async throws -> [T] {
        // This would be implemented by subclasses
        fatalError("fetchData must be implemented by subclasses")
    }
}

// MARK: - Performance-Optimized Views

struct PerformantVibeCard<Content: View>: View {
    let content: Content
    let onTap: (() -> Void)?
    
    @State private var isVisible = false
    
    init(onTap: (() -> Void)? = nil, @ViewBuilder content: () -> Content) {
        self.onTap = onTap
        self.content = content()
    }
    
    var body: some View {
        Group {
            if isVisible {
                Button(action: { onTap?() }) {
                    content
                        .padding(.vibeSpacing.md)
                        .background(
                            RoundedRectangle(cornerRadius: .vibeRadius.lg)
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.15),
                                            Color.white.opacity(0.05)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .background(
                                    RoundedRectangle(cornerRadius: .vibeRadius.lg)
                                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            } else {
                // Placeholder while not visible
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: 100) // Estimated height
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.3)) {
                isVisible = true
            }
        }
        .onDisappear {
            isVisible = false
        }
    }
}

// Using BatteryOptimizer from BatteryThermalOptimizer.swift

// MARK: - Performance Extensions

extension View {
    func memoryEfficient() -> some View {
        self
            .clipped() // Prevent overdraw
            .drawingGroup() // Flatten view hierarchy for complex views
    }
}
