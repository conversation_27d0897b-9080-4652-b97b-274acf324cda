//
//  VibeFinanceComponents.swift
//  VibeFinance - Unified Component Library
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - <PERSON> Inspired Component Library

// MARK: - Convenience Extensions for Design System Access

extension Font {
    static let vibe = VibeFinanceDesignSystem.Typography.self
}

extension Color {
    static let vibe = VibeFinanceDesignSystem.Colors.self
}

// MARK: - Tab Selector Component

/// Unified glassmorphic tab selector for consistent navigation
struct VibeTabSelector: View {
    @Binding var selectedTab: Int
    let tabs: [String]
    @Environment(\.theme) var theme

    var body: some View {
        HStack(spacing: 0) {
            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = index
                    }
                }) {
                    Text(tab)
                        .font(.vibe.callout)
                        .fontWeight(selectedTab == index ? .semibold : .medium)
                        .foregroundColor(selectedTab == index ? theme.onPrimary : theme.onSurface.opacity(0.7))
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                        .background(
                            RoundedRectangle(cornerRadius: .vibeRadius.md)
                                .fill(selectedTab == index ? theme.accent : Color.clear)
                                .animation(.easeInOut(duration: 0.3), value: selectedTab)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(4)
        .glassmorphicCard(theme: theme, cornerRadius: .vibeRadius.lg)
    }
}

// MARK: - Card Components

/// Unified card component with Warren Buffett inspired styling
struct VibeCard<Content: View>: View {
    let content: Content
    let cornerRadius: CGFloat
    let padding: CGFloat
    
    init(
        cornerRadius: CGFloat = .vibeRadius.lg,
        padding: CGFloat = .vibeSpacing.md,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.cornerRadius = cornerRadius
        self.padding = padding
    }
    
    var body: some View {
        content
            .padding(padding)
            .glassmorphic(cornerRadius: cornerRadius, opacity: 0.15)
    }
}

/// Financial metric card with consistent styling
struct VibeFinancialCard: View {
    let title: String
    let value: String
    let change: String?
    let isPositive: Bool?
    let icon: String?
    
    init(
        title: String,
        value: String,
        change: String? = nil,
        isPositive: Bool? = nil,
        icon: String? = nil
    ) {
        self.title = title
        self.value = value
        self.change = change
        self.isPositive = isPositive
        self.icon = icon
    }
    
    var body: some View {
        VibeCard {
            VStack(alignment: .leading, spacing: .vibeSpacing.sm) {
                HStack {
                    if let icon = icon {
                        Image(systemName: icon)
                            .font(.vibe.title3)
                            .foregroundColor(.vibe.accentGold)
                    }
                    
                    Text(title)
                        .font(.vibe.callout)
                        .foregroundColor(.vibe.textSecondary)
                    
                    Spacer()
                }
                
                Text(value)
                    .font(.vibe.financialMedium)
                    .foregroundColor(.vibe.textPrimary)
                
                if let change = change, let isPositive = isPositive {
                    HStack(spacing: 4) {
                        Image(systemName: isPositive ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text(change)
                            .font(.vibe.caption)
                    }
                    .foregroundColor(isPositive ? .vibe.profit : .vibe.loss)
                }
            }
        }
    }
}

// MARK: - Button Components

/// Primary button with Warren Buffett inspired styling
struct VibePrimaryButton: View {
    let title: String
    let icon: String?
    let action: () -> Void
    @State private var isPressed = false
    
    init(
        title: String,
        icon: String? = nil,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: .vibeSpacing.sm) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.vibe.headline)
                }
                
                Text(title)
                    .font(.vibe.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(VibeFinanceDesignSystem.Colors.primaryBlue)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: .vibeRadius.md)
                    .fill(VibeFinanceDesignSystem.Colors.accentGold)
                    .shadow(
                        color: VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3),
                        radius: isPressed ? 2 : 4,
                        x: 0,
                        y: isPressed ? 1 : 2
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

/// Secondary button with glassmorphic styling
struct VibeSecondaryButton: View {
    let title: String
    let icon: String?
    let action: () -> Void
    @State private var isPressed = false
    
    init(
        title: String,
        icon: String? = nil,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: .vibeSpacing.sm) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.vibe.headline)
                }
                
                Text(title)
                    .font(.vibe.headline)
                    .fontWeight(.medium)
            }
            .foregroundColor(.vibe.textPrimary)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .glassmorphic(cornerRadius: .vibeRadius.md, opacity: 0.2)
            .overlay(
                RoundedRectangle(cornerRadius: .vibeRadius.md)
                    .stroke(VibeFinanceDesignSystem.Colors.accentGold.opacity(0.5), lineWidth: 1)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Header Components

/// Warren Buffett inspired wisdom header
struct VibeWisdomHeader: View {
    let title: String
    let subtitle: String
    let quote: String?
    
    init(
        title: String,
        subtitle: String,
        quote: String? = nil
    ) {
        self.title = title
        self.subtitle = subtitle
        self.quote = quote
    }
    
    var body: some View {
        VibeCard(cornerRadius: .vibeRadius.xl, padding: .vibeSpacing.lg) {
            VStack(alignment: .leading, spacing: .vibeSpacing.md) {
                HStack {
                    // Warren Buffett inspired icon
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.vibe.accentGold, .vibe.accentGoldLight],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                        .overlay(
                            Image(systemName: "brain.head.profile")
                                .font(.title2)
                                .foregroundColor(.vibe.primaryBlue)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(title)
                            .font(.vibe.title2)
                            .foregroundColor(.vibe.textPrimary)
                        
                        Text(subtitle)
                            .font(.vibe.callout)
                            .foregroundColor(.vibe.textSecondary)
                    }
                    
                    Spacer()
                    
                    // Online indicator
                    HStack(spacing: 6) {
                        Circle()
                            .fill(VibeFinanceDesignSystem.Colors.profit)
                            .frame(width: 8, height: 8)
                        
                        Text("Online")
                            .font(.vibe.caption)
                            .foregroundColor(.vibe.textSecondary)
                    }
                }
                
                if let quote = quote {
                    Text(quote)
                        .font(.vibe.body)
                        .foregroundColor(.vibe.textSecondary)
                        .italic()
                        .padding(.top, .vibeSpacing.sm)
                }
            }
        }
    }
}

// MARK: - Input Components

/// Glassmorphic text input field
struct VibeTextField: View {
    let placeholder: String
    @Binding var text: String
    let icon: String?
    
    init(
        placeholder: String,
        text: Binding<String>,
        icon: String? = nil
    ) {
        self.placeholder = placeholder
        self._text = text
        self.icon = icon
    }
    
    var body: some View {
        HStack(spacing: .vibeSpacing.md) {
            if let icon = icon {
                Image(systemName: icon)
                    .font(.vibe.body)
                    .foregroundColor(.vibe.textSecondary)
            }
            
            TextField(placeholder, text: $text)
                .font(.vibe.body)
                .foregroundColor(.vibe.textPrimary)
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(.vibeSpacing.md)
        .glassmorphic(cornerRadius: .vibeRadius.md, opacity: 0.15)
        .overlay(
            RoundedRectangle(cornerRadius: .vibeRadius.md)
                .stroke(VibeFinanceDesignSystem.Colors.textSecondary.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - Portfolio Components

/// Warren Buffett inspired portfolio summary card
struct VibePortfolioCard: View {
    let totalValue: String
    let dailyChange: String
    let dailyChangePercent: String
    let isPositive: Bool

    var body: some View {
        VibeCard(cornerRadius: .vibeRadius.xl, padding: .vibeSpacing.lg) {
            VStack(alignment: .leading, spacing: .vibeSpacing.md) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Portfolio Value")
                            .font(.vibe.callout)
                            .foregroundColor(.vibe.textSecondary)

                        Text(totalValue)
                            .font(.vibe.financialLarge)
                            .foregroundColor(.vibe.textPrimary)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Today's Change")
                            .font(.vibe.caption)
                            .foregroundColor(.vibe.textSecondary)

                        HStack(spacing: 4) {
                            Image(systemName: isPositive ? "arrow.up.right" : "arrow.down.right")
                                .font(.caption)

                            Text(dailyChange)
                                .font(.vibe.financialSmall)

                            Text("(\(dailyChangePercent))")
                                .font(.vibe.caption)
                        }
                        .foregroundColor(isPositive ? .vibe.profit : .vibe.loss)
                    }
                }

                // Warren Buffett inspired quote
                Text("\"Time is the friend of the wonderful company, the enemy of the mediocre.\"")
                    .font(.vibe.footnote)
                    .foregroundColor(.vibe.textTertiary)
                    .italic()
                    .padding(.top, .vibeSpacing.sm)
            }
        }
    }
}

// MARK: - Quick Action Components

/// Quick action button for Warren Buffett inspired actions
struct VibeQuickActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: .vibeSpacing.xs) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.vibe.accentGold)

                Text(title)
                    .font(.vibe.caption)
                    .foregroundColor(.vibe.textSecondary)
                    .multilineTextAlignment(.center)
            }
            .frame(width: 70, height: 70)
            .glassmorphic(cornerRadius: .vibeRadius.md, opacity: 0.15)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Stock Components

/// Stock list item with Warren Buffett inspired styling
struct VibeStockListItem: View {
    let symbol: String
    let companyName: String
    let price: String
    let change: String
    let changePercent: String
    let isPositive: Bool
    let buffettNote: String?

    var body: some View {
        VibeCard(padding: .vibeSpacing.md) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(symbol)
                        .font(.vibe.headline)
                        .foregroundColor(.vibe.textPrimary)

                    Text(companyName)
                        .font(.vibe.caption)
                        .foregroundColor(.vibe.textSecondary)
                        .lineLimit(1)

                    if let buffettNote = buffettNote {
                        HStack(spacing: 4) {
                            Image(systemName: "lightbulb.fill")
                                .font(.caption2)
                                .foregroundColor(.vibe.accentGold)

                            Text(buffettNote)
                                .font(.vibe.caption)
                                .foregroundColor(.vibe.accentGold)
                                .lineLimit(1)
                        }
                    }
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text(price)
                        .font(.vibe.financialMedium)
                        .foregroundColor(.vibe.textPrimary)

                    HStack(spacing: 4) {
                        Image(systemName: isPositive ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption2)

                        Text(change)
                            .font(.vibe.caption)

                        Text("(\(changePercent))")
                            .font(.vibe.caption)
                    }
                    .foregroundColor(isPositive ? .vibe.profit : .vibe.loss)
                }
            }
        }
    }
}

// MARK: - Analytics Components

/// Analytics metric card with Warren Buffett wisdom
struct VibeAnalyticsMetric: View {
    let title: String
    let value: String
    let subtitle: String?
    let icon: String
    let color: Color
    let buffettWisdom: String?

    var body: some View {
        VibeCard {
            VStack(alignment: .leading, spacing: .vibeSpacing.sm) {
                HStack {
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(color)

                    Spacer()

                    if buffettWisdom != nil {
                        Button(action: {
                            // Show wisdom tooltip
                        }) {
                            Image(systemName: "info.circle")
                                .font(.caption)
                                .foregroundColor(.vibe.textTertiary)
                        }
                    }
                }

                Text(title)
                    .font(.vibe.callout)
                    .foregroundColor(.vibe.textSecondary)

                Text(value)
                    .font(.vibe.financialMedium)
                    .foregroundColor(.vibe.textPrimary)

                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.vibe.caption)
                        .foregroundColor(.vibe.textTertiary)
                }
            }
        }
    }
}
