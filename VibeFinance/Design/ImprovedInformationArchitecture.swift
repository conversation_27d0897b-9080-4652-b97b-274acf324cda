//
//  ImprovedInformationArchitecture.swift
//  VibeFinance - Enhanced Navigation & User Flow System
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Enhanced Navigation System

/// Improved navigation coordinator for better user flows
class NavigationCoordinator: ObservableObject {
    @Published var currentTab: MainTab = .dashboard
    @Published var navigationPath = NavigationPath()
    @Published var showingOnboarding = false
    @Published var showingQuickActions = false
    
    enum MainTab: Int, CaseIterable {
        case dashboard = 0
        case trading = 1
        case analytics = 2
        case aiAdviser = 3
        case profile = 4
        
        var title: String {
            switch self {
            case .dashboard: return "Dashboard"
            case .trading: return "Trading"
            case .analytics: return "Analytics"
            case .aiAdviser: return "AI Adviser"
            case .profile: return "Profile"
            }
        }
        
        var icon: String {
            switch self {
            case .dashboard: return "house"
            case .trading: return "chart.line.uptrend.xyaxis"
            case .analytics: return "chart.bar.xaxis"
            case .aiAdviser: return "brain.head.profile"
            case .profile: return "person.circle"
            }
        }
        
        var selectedIcon: String {
            return icon + ".fill"
        }

        var analyticsName: String {
            switch self {
            case .dashboard: return "dashboard"
            case .trading: return "trading"
            case .analytics: return "analytics"
            case .aiAdviser: return "ai_adviser"
            case .profile: return "profile"
            }
        }
    }
    
    // MARK: - Navigation Actions
    
    func navigateToTab(_ tab: MainTab) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentTab = tab
        }
    }
    
    func showQuickActions() {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            showingQuickActions = true
        }
    }
    
    func hideQuickActions() {
        withAnimation(.easeInOut(duration: 0.2)) {
            showingQuickActions = false
        }
    }
    
    func resetToRoot() {
        navigationPath = NavigationPath()
    }
}

// MARK: - Enhanced Tab Bar

struct EnhancedTabBar: View {
    @ObservedObject var coordinator: NavigationCoordinator
    @State private var tabBarOffset: CGFloat = 0
    
    var body: some View {
        VStack(spacing: 0) {
            // Tab Bar Content
            HStack(spacing: 0) {
                ForEach(NavigationCoordinator.MainTab.allCases, id: \.rawValue) { tab in
                    TabBarItem(
                        tab: tab,
                        isSelected: coordinator.currentTab == tab,
                        action: {
                            coordinator.navigateToTab(tab)
                            HapticManager.shared.impact(.light)
                        }
                    )
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 12)
            .background(
                // Glassmorphic background
                RoundedRectangle(cornerRadius: 24)
                    .fill(
                        LinearGradient(
                            colors: [
                                VibeFinanceDesignSystem.Colors.primaryBlue.opacity(0.95),
                                VibeFinanceDesignSystem.Colors.primaryBlueDark.opacity(0.9)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .background(
                        RoundedRectangle(cornerRadius: 24)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.white.opacity(0.3),
                                        Color.white.opacity(0.1)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
                    .shadow(
                        color: Color.black.opacity(0.2),
                        radius: 10,
                        x: 0,
                        y: 5
                    )
            )
            .padding(.horizontal, 16)
            .padding(.bottom, 8)
        }
        .offset(y: tabBarOffset)
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: tabBarOffset)
    }
}

struct TabBarItem: View {
    let tab: NavigationCoordinator.MainTab
    let isSelected: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: isSelected ? tab.selectedIcon : tab.icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(isSelected ? VibeFinanceDesignSystem.Colors.accentGold : .white.opacity(0.6))
                    .scaleEffect(isSelected ? 1.1 : 1.0)
                
                Text(tab.title)
                    .font(.caption2)
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isSelected ? VibeFinanceDesignSystem.Colors.accentGold : .white.opacity(0.6))
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        isSelected ? 
                        VibeFinanceDesignSystem.Colors.accentGold.opacity(0.2) :
                        Color.clear
                    )
                    .scaleEffect(isPressed ? 0.95 : 1.0)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Quick Actions Overlay

struct QuickActionsOverlay: View {
    @ObservedObject var coordinator: NavigationCoordinator
    
    private let quickActions = [
        NavQuickAction(title: "Buy Stock", icon: "plus.circle.fill", color: .green),
        NavQuickAction(title: "AI Analysis", icon: "brain.head.profile", color: .blue),
        NavQuickAction(title: "Portfolio", icon: "chart.pie.fill", color: .orange),
        NavQuickAction(title: "News", icon: "newspaper.fill", color: .purple)
    ]
    
    var body: some View {
        if coordinator.showingQuickActions {
            ZStack {
                // Background overlay
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        coordinator.hideQuickActions()
                    }
                
                // Quick actions grid
                VStack(spacing: 20) {
                    Text("Quick Actions")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                        ForEach(quickActions, id: \.title) { action in
                            NavQuickActionButton(action: action) {
                                coordinator.hideQuickActions()
                                // Handle action
                            }
                        }
                    }
                }
                .padding(24)
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(VibeFinanceDesignSystem.Colors.primaryGradient)
                        .shadow(radius: 20)
                )
                .padding(.horizontal, 32)
                .scaleEffect(coordinator.showingQuickActions ? 1.0 : 0.8)
                .opacity(coordinator.showingQuickActions ? 1.0 : 0.0)
            }
            .transition(.asymmetric(
                insertion: .scale.combined(with: .opacity),
                removal: .scale.combined(with: .opacity)
            ))
        }
    }
}

struct NavQuickAction {
    let title: String
    let icon: String
    let color: Color
}

struct NavQuickActionButton: View {
    let action: NavQuickAction
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                Image(systemName: action.icon)
                    .font(.title)
                    .foregroundColor(action.color)
                
                Text(action.title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            .frame(height: 80)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Haptic Feedback Manager

class HapticManager {
    static let shared = HapticManager()
    
    private init() {}
    
    func impact(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impactFeedback = UIImpactFeedbackGenerator(style: style)
        impactFeedback.impactOccurred()
    }
    
    func notification(_ type: UINotificationFeedbackGenerator.FeedbackType) {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(type)
    }
    
    func selection() {
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }
}

// MARK: - Enhanced Navigation View

struct EnhancedNavigationView<Content: View>: View {
    let title: String
    let showBackButton: Bool
    let content: Content
    @Environment(\.dismiss) private var dismiss
    
    init(
        title: String,
        showBackButton: Bool = false,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.showBackButton = showBackButton
        self.content = content()
    }
    
    var body: some View {
        NavigationView {
            content
                .navigationTitle(title)
                .navigationBarTitleDisplayMode(.large)
                .toolbar {
                    if showBackButton {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button(action: { dismiss() }) {
                                Image(systemName: "chevron.left")
                                    .font(.title3)
                                    .fontWeight(.medium)
                                    .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                            }
                        }
                    }
                }
        }
    }
}
