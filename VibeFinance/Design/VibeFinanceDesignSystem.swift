//
//  VibeFinanceDesignSystem.swift
//  VibeFinance - Unified Design System
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI
import UIKit

// MARK: - <PERSON> Inspired Design System

/// Comprehensive design system for VibeFinance following <PERSON>'s philosophy
/// of simplicity, consistency, and long-term value
struct VibeFinanceDesignSystem {
    
    // MARK: - Color Palette
    
    /// Primary color palette inspired by <PERSON>'s conservative, trustworthy approach
    struct Colors {
        
        // MARK: - Primary Colors
        
        /// <PERSON> inspired primary blue - represents trust, stability, wisdom
        static let primaryBlue = Color(red: 0.1, green: 0.2, blue: 0.4)
        static let primaryBlueLight = Color(red: 0.2, green: 0.3, blue: 0.5)
        static let primaryBlueDark = Color(red: 0.05, green: 0.15, blue: 0.35)
        
        /// Berkshire Hathaway inspired accent gold - represents value, success, wisdom
        static let accentGold = Color(red: 1.0, green: 0.84, blue: 0.0)
        static let accentGoldLight = Color(red: 1.0, green: 0.9, blue: 0.3)
        static let accentGoldDark = Color(red: 0.8, green: 0.67, blue: 0.0)
        
        // MARK: - Background Gradients
        
        /// Main Warren Buffett inspired gradient for all screens
        static let primaryGradient = LinearGradient(
            colors: [
                primaryBlue,
                primaryBlueLight,
                Color(red: 0.1, green: 0.25, blue: 0.45)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        /// Secondary gradient for cards and components
        static let cardGradient = LinearGradient(
            colors: [
                Color.white.opacity(0.25),
                Color.white.opacity(0.1)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        /// Success gradient for positive financial metrics
        static let successGradient = LinearGradient(
            colors: [
                Color.green.opacity(0.3),
                Color.blue.opacity(0.2)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        
        // MARK: - Semantic Colors
        
        /// Financial success/profit color
        static let profit = Color.green
        static let profitLight = Color.green.opacity(0.7)
        
        /// Financial loss/risk color
        static let loss = Color.red
        static let lossLight = Color.red.opacity(0.7)
        
        /// Warning/caution color
        static let warning = Color.orange
        static let warningLight = Color.orange.opacity(0.7)
        
        /// Information/neutral color
        static let info = Color.blue
        static let infoLight = Color.blue.opacity(0.7)
        
        // MARK: - Text Colors
        
        /// Primary text color for dark theme
        static let textPrimary = Color.white
        
        /// Secondary text color for dark theme
        static let textSecondary = Color.white.opacity(0.7)
        
        /// Tertiary text color for dark theme
        static let textTertiary = Color.white.opacity(0.5)
        
        // MARK: - Surface Colors
        
        /// Glassmorphic surface with blur effect
        static let glassSurface = Color.white.opacity(0.15)
        static let glassSurfaceLight = Color.white.opacity(0.25)
        static let glassSurfaceDark = Color.white.opacity(0.05)
        
        /// Card background with subtle transparency
        static let cardBackground = Color.black.opacity(0.3)
        
        /// Tab bar background
        static let tabBarBackground = Color(red: 0.1, green: 0.1, blue: 0.15)
    }
    
    // MARK: - Typography System
    
    /// Typography system following Apple's Human Interface Guidelines
    /// with Warren Buffett inspired hierarchy
    struct Typography {
        
        // MARK: - Font Weights
        
        static let light = Font.Weight.light
        static let regular = Font.Weight.regular
        static let medium = Font.Weight.medium
        static let semibold = Font.Weight.semibold
        static let bold = Font.Weight.bold
        
        // MARK: - Font Sizes
        
        /// Large title for main headers
        static let largeTitle = Font.largeTitle.weight(.bold)
        
        /// Title for section headers
        static let title = Font.title.weight(.semibold)
        
        /// Title 2 for subsection headers
        static let title2 = Font.title2.weight(.semibold)
        
        /// Title 3 for card headers
        static let title3 = Font.title3.weight(.medium)
        
        /// Headline for important text
        static let headline = Font.headline.weight(.semibold)
        
        /// Body text for main content
        static let body = Font.body.weight(.regular)
        
        /// Callout for secondary content
        static let callout = Font.callout.weight(.medium)
        
        /// Subheadline for tertiary content
        static let subheadline = Font.subheadline.weight(.regular)
        
        /// Footnote for small text
        static let footnote = Font.footnote.weight(.regular)
        
        /// Caption for very small text
        static let caption = Font.caption.weight(.regular)
        
        // MARK: - Financial Typography
        
        /// Large financial numbers (portfolio value)
        static let financialLarge = Font.system(size: 32, weight: .bold, design: .rounded)
        
        /// Medium financial numbers (stock prices)
        static let financialMedium = Font.system(size: 24, weight: .semibold, design: .rounded)
        
        /// Small financial numbers (percentages)
        static let financialSmall = Font.system(size: 16, weight: .medium, design: .rounded)
    }
    
    // MARK: - Spacing System
    
    /// Consistent spacing system following 8pt grid
    struct Spacing {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 16
        static let lg: CGFloat = 24
        static let xl: CGFloat = 32
        static let xxl: CGFloat = 48
        static let xxxl: CGFloat = 64
    }
    
    // MARK: - Corner Radius System
    
    /// Consistent corner radius system
    struct CornerRadius {
        static let xs: CGFloat = 4
        static let sm: CGFloat = 8
        static let md: CGFloat = 12
        static let lg: CGFloat = 16
        static let xl: CGFloat = 20
        static let xxl: CGFloat = 24
        static let round: CGFloat = 50
    }
    
    // MARK: - Shadow System
    
    /// Consistent shadow system for depth
    struct Shadows {
        static let small = Color.black.opacity(0.1)
        static let medium = Color.black.opacity(0.2)
        static let large = Color.black.opacity(0.3)
        
        static let smallRadius: CGFloat = 2
        static let mediumRadius: CGFloat = 4
        static let largeRadius: CGFloat = 8
    }
}

// MARK: - Design System Extensions
// Extensions are defined in VibeFinanceComponents.swift to avoid duplication

extension CGFloat {
    /// Consistent spacing system
    static let vibeSpacing = VibeFinanceDesignSystem.Spacing.self
    
    /// Consistent corner radius system
    static let vibeRadius = VibeFinanceDesignSystem.CornerRadius.self
}

// MARK: - View Modifiers

/// Glassmorphic effect modifier
struct GlassmorphicModifier: ViewModifier {
    let cornerRadius: CGFloat
    let opacity: Double
    
    func body(content: Content) -> some View {
        content
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(Color.white.opacity(opacity))
                    .background(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                    .blur(radius: 0.5)
            )
    }
}

extension View {
    /// Apply glassmorphic effect
    func glassmorphic(cornerRadius: CGFloat = 16, opacity: Double = 0.15) -> some View {
        self.modifier(GlassmorphicModifier(cornerRadius: cornerRadius, opacity: opacity))
    }
    
    /// Apply Warren Buffett inspired background
    func warrenBackground() -> some View {
        self.background(
            VibeFinanceDesignSystem.Colors.primaryGradient
                .ignoresSafeArea()
        )
    }
}
