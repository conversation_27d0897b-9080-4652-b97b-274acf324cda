//
//  UserTestingAnalyticsSystem.swift
//  VibeFinance - A/B Testing & User Analytics for Apple Design Award
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI
import Combine

// MARK: - A/B Testing Framework

@MainActor
class ABTestingManager: ObservableObject {
    @Published var activeTests: [String: ABTest] = [:]
    @Published var userVariants: [String: String] = [:]
    
    private let userDefaults = UserDefaults.standard
    private let analyticsManager = UserAnalyticsManager.shared
    
    static let shared = ABTestingManager()
    
    private init() {
        loadUserVariants()
        setupDefaultTests()
    }
    
    private func loadUserVariants() {
        if let data = userDefaults.data(forKey: "user_ab_variants"),
           let variants = try? JSONDecoder().decode([String: String].self, from: data) {
            userVariants = variants
        }
    }
    
    private func saveUserVariants() {
        if let data = try? JSONEncoder().encode(userVariants) {
            userDefaults.set(data, forKey: "user_ab_variants")
        }
    }
    
    private func setupDefaultTests() {
        // <PERSON> Quote Frequency Test
        addTest(ABTest(
            id: "buffett_quote_frequency",
            name: "Warren Buffett Quote Display Frequency",
            variants: [
                ABTestVariant(id: "high", name: "High Frequency", weight: 0.33),
                ABTestVariant(id: "medium", name: "Medium Frequency", weight: 0.34),
                ABTestVariant(id: "low", name: "Low Frequency", weight: 0.33)
            ],
            isActive: true
        ))
        
        // Dashboard Layout Test
        addTest(ABTest(
            id: "dashboard_layout",
            name: "Dashboard Layout Optimization",
            variants: [
                ABTestVariant(id: "compact", name: "Compact Layout", weight: 0.5),
                ABTestVariant(id: "expanded", name: "Expanded Layout", weight: 0.5)
            ],
            isActive: true
        ))
        
        // AI Adviser Interaction Style
        addTest(ABTest(
            id: "ai_adviser_style",
            name: "AI Adviser Interaction Style",
            variants: [
                ABTestVariant(id: "formal", name: "Formal Warren Style", weight: 0.5),
                ABTestVariant(id: "casual", name: "Casual Warren Style", weight: 0.5)
            ],
            isActive: true
        ))
    }
    
    func addTest(_ test: ABTest) {
        activeTests[test.id] = test
    }
    
    func getVariant(for testId: String) -> String {
        // Return existing variant if user already assigned
        if let existingVariant = userVariants[testId] {
            return existingVariant
        }
        
        // Assign new variant based on weights
        guard let test = activeTests[testId], test.isActive else {
            return "control"
        }
        
        let randomValue = Double.random(in: 0...1)
        var cumulativeWeight = 0.0
        
        for variant in test.variants {
            cumulativeWeight += variant.weight
            if randomValue <= cumulativeWeight {
                userVariants[testId] = variant.id
                saveUserVariants()
                
                // Track assignment
                analyticsManager.trackEvent("ab_test_assigned", parameters: [
                    "test_id": testId,
                    "variant": variant.id
                ])
                
                return variant.id
            }
        }
        
        return test.variants.first?.id ?? "control"
    }
    
    func trackConversion(testId: String, conversionType: String) {
        guard let variant = userVariants[testId] else { return }
        
        analyticsManager.trackEvent("ab_test_conversion", parameters: [
            "test_id": testId,
            "variant": variant,
            "conversion_type": conversionType
        ])
    }
}

struct ABTest {
    let id: String
    let name: String
    let variants: [ABTestVariant]
    let isActive: Bool
}

struct ABTestVariant {
    let id: String
    let name: String
    let weight: Double
}

// MARK: - User Analytics Manager

@MainActor
class UserAnalyticsManager: ObservableObject {
    @Published var userBehaviorData: [String: Any] = [:]
    @Published var sessionMetrics: SessionMetrics = SessionMetrics()
    
    static let shared = UserAnalyticsManager()
    
    private var sessionStartTime = Date()
    private var screenViewStartTime = Date()
    private var currentScreen = ""
    
    private init() {
        startSession()
    }
    
    private func startSession() {
        sessionStartTime = Date()
        sessionMetrics = SessionMetrics()
        
        trackEvent("session_start", parameters: [
            "timestamp": sessionStartTime.timeIntervalSince1970,
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown"
        ])
    }
    
    func trackEvent(_ eventName: String, parameters: [String: Any] = [:]) {
        var eventData = parameters
        eventData["event_name"] = eventName
        eventData["timestamp"] = Date().timeIntervalSince1970
        eventData["session_id"] = sessionMetrics.sessionId
        
        // Store locally for now (in production, send to analytics service)
        print("📊 Analytics Event: \(eventName) - \(eventData)")
        
        // Update session metrics
        sessionMetrics.eventCount += 1
        sessionMetrics.lastEventTime = Date()
    }
    
    func trackScreenView(_ screenName: String) {
        // Track previous screen time
        if !currentScreen.isEmpty {
            let timeSpent = Date().timeIntervalSince(screenViewStartTime)
            trackEvent("screen_time", parameters: [
                "screen": currentScreen,
                "duration": timeSpent
            ])
        }
        
        // Start tracking new screen
        currentScreen = screenName
        screenViewStartTime = Date()
        
        trackEvent("screen_view", parameters: [
            "screen": screenName
        ])
    }
    
    func trackUserAction(_ action: String, context: [String: Any] = [:]) {
        var actionData = context
        actionData["action"] = action
        actionData["screen"] = currentScreen
        
        trackEvent("user_action", parameters: actionData)
    }
    
    func trackPerformanceMetric(_ metric: String, value: Double, unit: String = "") {
        trackEvent("performance_metric", parameters: [
            "metric": metric,
            "value": value,
            "unit": unit
        ])
    }
    
    func trackError(_ error: String, context: [String: Any] = [:]) {
        var errorData = context
        errorData["error"] = error
        errorData["screen"] = currentScreen
        
        trackEvent("error", parameters: errorData)
    }
}

struct SessionMetrics {
    let sessionId = UUID().uuidString
    let startTime = Date()
    var eventCount = 0
    var lastEventTime = Date()
    
    var sessionDuration: TimeInterval {
        lastEventTime.timeIntervalSince(startTime)
    }
}

// MARK: - User Feedback System

@MainActor
class UserFeedbackManager: ObservableObject {
    @Published var feedbackItems: [FeedbackItem] = []
    @Published var showingFeedbackPrompt = false
    @Published var currentFeedbackType: FeedbackType = .general
    
    static let shared = UserFeedbackManager()
    
    private let analyticsManager = UserAnalyticsManager.shared
    
    private init() {}
    
    func promptForFeedback(type: FeedbackType, trigger: String) {
        currentFeedbackType = type
        showingFeedbackPrompt = true
        
        analyticsManager.trackEvent("feedback_prompt_shown", parameters: [
            "type": type.rawValue,
            "trigger": trigger
        ])
    }
    
    func submitFeedback(_ feedback: FeedbackItem) {
        feedbackItems.append(feedback)
        
        analyticsManager.trackEvent("feedback_submitted", parameters: [
            "type": feedback.type.rawValue,
            "rating": feedback.rating ?? -1,
            "has_text": !feedback.text.isEmpty
        ])
        
        // In production, send to feedback service
        print("📝 Feedback Submitted: \(feedback)")
    }
    
    func shouldShowFeedbackPrompt(for screen: String) -> Bool {
        // Smart logic to determine when to show feedback prompts
        let lastPromptKey = "last_feedback_prompt_\(screen)"
        let lastPrompt = UserDefaults.standard.object(forKey: lastPromptKey) as? Date ?? Date.distantPast
        
        // Show prompt if it's been more than 7 days since last prompt for this screen
        return Date().timeIntervalSince(lastPrompt) > 7 * 24 * 60 * 60
    }
    
    func markFeedbackPromptShown(for screen: String) {
        let lastPromptKey = "last_feedback_prompt_\(screen)"
        UserDefaults.standard.set(Date(), forKey: lastPromptKey)
    }
}

struct FeedbackItem {
    let id = UUID()
    let type: FeedbackType
    let text: String
    let rating: Int?
    let timestamp = Date()
    let screen: String
    let context: [String: Any]
}

enum FeedbackType: String, CaseIterable {
    case general = "general"
    case usability = "usability"
    case feature = "feature"
    case bug = "bug"
    case performance = "performance"
    
    var title: String {
        switch self {
        case .general: return "General Feedback"
        case .usability: return "Usability"
        case .feature: return "Feature Request"
        case .bug: return "Bug Report"
        case .performance: return "Performance"
        }
    }
    
    var icon: String {
        switch self {
        case .general: return "message.fill"
        case .usability: return "hand.tap.fill"
        case .feature: return "lightbulb.fill"
        case .bug: return "ladybug.fill"
        case .performance: return "speedometer"
        }
    }
}

// MARK: - Smart Feedback Prompt View

struct SmartFeedbackPrompt: View {
    @ObservedObject var feedbackManager = UserFeedbackManager.shared
    @State private var feedbackText = ""
    @State private var rating: Int?
    
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Image(systemName: feedbackManager.currentFeedbackType.icon)
                    .font(.title2)
                    .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                
                Text(feedbackManager.currentFeedbackType.title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Spacer()
                
                Button("✕") {
                    onDismiss()
                }
                .foregroundColor(.white.opacity(0.7))
            }
            
            // Rating (for applicable types)
            if feedbackManager.currentFeedbackType != .bug {
                VStack(alignment: .leading, spacing: 8) {
                    Text("How would you rate this experience?")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.9))
                    
                    HStack(spacing: 8) {
                        ForEach(1...5, id: \.self) { star in
                            Button(action: {
                                rating = star
                            }) {
                                Image(systemName: star <= (rating ?? 0) ? "star.fill" : "star")
                                    .foregroundColor(star <= (rating ?? 0) ? VibeFinanceDesignSystem.Colors.accentGold : .white.opacity(0.3))
                                    .font(.title3)
                            }
                        }
                    }
                }
            }
            
            // Text Input
            VStack(alignment: .leading, spacing: 8) {
                Text("Tell us more (optional)")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                
                TextField("Your feedback...", text: $feedbackText, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
            }
            
            // Submit Button
            Button(action: submitFeedback) {
                Text("Submit Feedback")
                    .font(.headline)
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(VibeFinanceDesignSystem.Colors.accentGold)
                    .cornerRadius(12)
            }
            .disabled(feedbackText.isEmpty && rating == nil)
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.9))
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
    }
    
    private func submitFeedback() {
        let feedback = FeedbackItem(
            type: feedbackManager.currentFeedbackType,
            text: feedbackText,
            rating: rating,
            screen: UserAnalyticsManager.shared.sessionMetrics.sessionId,
            context: [:]
        )
        
        feedbackManager.submitFeedback(feedback)
        onDismiss()
    }
}

// MARK: - Analytics-Driven Improvements

struct AnalyticsDrivenComponent<Content: View>: View {
    let componentId: String
    let content: Content
    
    @StateObject private var analyticsManager = UserAnalyticsManager.shared
    @State private var viewStartTime = Date()
    
    init(id: String, @ViewBuilder content: () -> Content) {
        self.componentId = id
        self.content = content()
    }
    
    var body: some View {
        content
            .onAppear {
                viewStartTime = Date()
                analyticsManager.trackEvent("component_view", parameters: [
                    "component_id": componentId
                ])
            }
            .onDisappear {
                let viewDuration = Date().timeIntervalSince(viewStartTime)
                analyticsManager.trackEvent("component_view_duration", parameters: [
                    "component_id": componentId,
                    "duration": viewDuration
                ])
            }
    }
}

// MARK: - View Extensions

extension View {
    func trackAnalytics(componentId: String) -> some View {
        AnalyticsDrivenComponent(id: componentId) {
            self
        }
    }
    
    func withABTest(testId: String) -> some View {
        let variant = ABTestingManager.shared.getVariant(for: testId)
        return self.environment(\.abTestVariant, variant)
    }
}

// MARK: - Environment Values

private struct ABTestVariantKey: EnvironmentKey {
    static let defaultValue: String = "control"
}

extension EnvironmentValues {
    var abTestVariant: String {
        get { self[ABTestVariantKey.self] }
        set { self[ABTestVariantKey.self] = newValue }
    }
}
