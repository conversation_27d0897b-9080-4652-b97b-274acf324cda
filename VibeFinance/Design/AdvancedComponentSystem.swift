//
//  AdvancedComponentSystem.swift
//  VibeFinance - Enhanced UI Components with Loading States & Animations
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Loading States

enum LoadingState<T> {
    case idle
    case loading
    case loaded(T)
    case error(Error)
    
    var isLoading: Bool {
        if case .loading = self { return true }
        return false
    }
    
    var data: T? {
        if case .loaded(let data) = self { return data }
        return nil
    }
    
    var error: Error? {
        if case .error(let error) = self { return error }
        return nil
    }
}

// MARK: - Enhanced Loading Components

struct VibeLoadingView: View {
    let message: String
    @State private var rotationAngle: Double = 0
    @State private var scale: CGFloat = 1.0
    
    var body: some View {
        VStack(spacing: 20) {
            // <PERSON> inspired loading animation
            ZStack {
                Circle()
                    .stroke(
                        VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3),
                        lineWidth: 4
                    )
                    .frame(width: 60, height: 60)
                
                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(
                        VibeFinanceDesignSystem.Colors.accentGold,
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 60, height: 60)
                    .rotationEffect(.degrees(rotationAngle))
                    .animation(
                        .linear(duration: 1.5).repeatForever(autoreverses: false),
                        value: rotationAngle
                    )
                
                Text("💰")
                    .font(.title2)
                    .scaleEffect(scale)
                    .animation(
                        .easeInOut(duration: 1.0).repeatForever(autoreverses: true),
                        value: scale
                    )
            }
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .onAppear {
            rotationAngle = 360
            scale = 1.2
        }
    }
}

struct VibeErrorView: View {
    let error: Error
    let retryAction: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            VStack(spacing: 8) {
                Text("Something went wrong")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(error.localizedDescription)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            VibePrimaryButton(
                title: "Try Again",
                icon: "arrow.clockwise",
                action: retryAction
            )
        }
        .padding(24)
    }
}

struct VibeEmptyStateView: View {
    let title: String
    let message: String
    let icon: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Text(icon)
                    .font(.system(size: 60))
                
                VStack(spacing: 8) {
                    Text(title)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(message)
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
            }
            
            if let actionTitle = actionTitle, let action = action {
                VibePrimaryButton(
                    title: actionTitle,
                    icon: "plus.circle",
                    action: action
                )
            }
        }
        .padding(32)
    }
}

// MARK: - Enhanced Interactive Components

struct InteractiveVibeCard<Content: View>: View {
    let content: Content
    let onTap: (() -> Void)?
    
    @State private var isPressed = false
    @State private var hoverOffset: CGFloat = 0
    
    init(onTap: (() -> Void)? = nil, @ViewBuilder content: () -> Content) {
        self.onTap = onTap
        self.content = content()
    }
    
    var body: some View {
        Button(action: { onTap?() }) {
            content
                .padding(.vibeSpacing.md)
                .background(
                    RoundedRectangle(cornerRadius: .vibeRadius.lg)
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.15),
                                    Color.white.opacity(0.05)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .background(
                            RoundedRectangle(cornerRadius: .vibeRadius.lg)
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.white.opacity(0.3),
                                            Color.white.opacity(0.1)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                        .shadow(
                            color: Color.black.opacity(0.1),
                            radius: isPressed ? 2 : 8,
                            x: 0,
                            y: isPressed ? 1 : 4
                        )
                )
                .scaleEffect(isPressed ? 0.98 : 1.0)
                .offset(y: hoverOffset)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
        .animation(.easeInOut(duration: 0.2), value: hoverOffset)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .onHover { hovering in
            hoverOffset = hovering ? -2 : 0
        }
    }
}

struct AnimatedVibeButton: View {
    let title: String
    let icon: String?
    let style: ButtonStyle
    let action: () -> Void
    
    @State private var isPressed = false
    @State private var rippleOffset: CGFloat = 0
    @State private var showRipple = false
    
    enum ButtonStyle {
        case primary
        case secondary
        case destructive
        
        var backgroundColor: Color {
            switch self {
            case .primary: return VibeFinanceDesignSystem.Colors.accentGold
            case .secondary: return Color.white.opacity(0.15)
            case .destructive: return Color.red
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary: return VibeFinanceDesignSystem.Colors.primaryBlue
            case .secondary: return .white
            case .destructive: return .white
            }
        }
    }
    
    var body: some View {
        Button(action: {
            // Haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            // Ripple animation
            withAnimation(.easeOut(duration: 0.6)) {
                showRipple = true
                rippleOffset = 50
            }
            
            // Reset ripple
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                showRipple = false
                rippleOffset = 0
            }
            
            action()
        }) {
            ZStack {
                // Ripple effect
                if showRipple {
                    Circle()
                        .fill(Color.white.opacity(0.3))
                        .frame(width: rippleOffset, height: rippleOffset)
                        .scaleEffect(showRipple ? 1.0 : 0.0)
                }
                
                HStack(spacing: .vibeSpacing.sm) {
                    if let icon = icon {
                        Image(systemName: icon)
                            .font(.vibe.headline)
                    }
                    
                    Text(title)
                        .font(.vibe.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(style.foregroundColor)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: .vibeRadius.md)
                    .fill(style.backgroundColor)
                    .shadow(
                        color: style.backgroundColor.opacity(0.3),
                        radius: isPressed ? 2 : 4,
                        x: 0,
                        y: isPressed ? 1 : 2
                    )
            )
            .scaleEffect(isPressed ? 0.96 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Smart Data Loading Component

struct SmartDataView<T, Content: View>: View {
    @Binding var loadingState: LoadingState<T>
    let loadData: () async -> Void
    let content: (T) -> Content
    
    var body: some View {
        Group {
            switch loadingState {
            case .idle:
                VibeLoadingView(message: "Preparing your data...")
                    .onAppear {
                        Task {
                            await loadData()
                        }
                    }
                
            case .loading:
                VibeLoadingView(message: "Loading Warren's insights...")
                
            case .loaded(let data):
                content(data)
                    .transition(.asymmetric(
                        insertion: .move(edge: .bottom).combined(with: .opacity),
                        removal: .move(edge: .top).combined(with: .opacity)
                    ))
                
            case .error(let error):
                VibeErrorView(error: error) {
                    Task {
                        await loadData()
                    }
                }
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.easeInOut(duration: 0.4), value: loadingState.isLoading)
    }
}

// MARK: - Progressive Disclosure Component

struct ProgressiveDisclosureView<Summary: View, Details: View>: View {
    let summary: Summary
    let details: Details
    
    @State private var isExpanded = false
    
    init(
        @ViewBuilder summary: () -> Summary,
        @ViewBuilder details: () -> Details
    ) {
        self.summary = summary()
        self.details = details()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            Button(action: {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    summary
                    
                    Spacer()
                    
                    Image(systemName: "chevron.down")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                        .rotationEffect(.degrees(isExpanded ? 180 : 0))
                        .animation(.easeInOut(duration: 0.3), value: isExpanded)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            if isExpanded {
                details
                    .padding(.top, .vibeSpacing.md)
                    .transition(.asymmetric(
                        insertion: .move(edge: .top).combined(with: .opacity),
                        removal: .move(edge: .top).combined(with: .opacity)
                    ))
            }
        }
        .padding(.vibeSpacing.md)
        .background(
            RoundedRectangle(cornerRadius: .vibeRadius.md)
                .fill(Color.white.opacity(0.1))
                .background(
                    RoundedRectangle(cornerRadius: .vibeRadius.md)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}
