//
//  MicroInteractionsSystem.swift
//  VibeFinance - Advanced Micro-Interactions for Apple Design Award
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI
import AVFoundation

// MARK: - Micro-Interactions Manager

@MainActor
class MicroInteractionsManager: ObservableObject {
    @Published var isHapticsEnabled = true
    @Published var isSoundEnabled = true
    @Published var animationIntensity: Double = 1.0
    
    private var audioPlayer: AVAudioPlayer?
    
    init() {
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    // MARK: - Haptic Feedback
    
    func triggerHaptic(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        guard isHapticsEnabled else { return }
        let impactFeedback = UIImpactFeedbackGenerator(style: style)
        impactFeedback.impactOccurred()
    }
    
    func triggerNotificationHaptic(_ type: UINotificationFeedbackGenerator.FeedbackType) {
        guard isHapticsEnabled else { return }
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(type)
    }
    
    func triggerSelectionHaptic() {
        guard isHapticsEnabled else { return }
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }
    
    // MARK: - Sound Effects
    
    func playSound(_ soundName: String) {
        guard isSoundEnabled else { return }
        
        guard let url = Bundle.main.url(forResource: soundName, withExtension: "wav") else {
            // Create synthetic sound if file doesn't exist
            createSyntheticSound(for: soundName)
            return
        }
        
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.volume = 0.3
            audioPlayer?.play()
        } catch {
            print("Failed to play sound: \(error)")
        }
    }
    
    private func createSyntheticSound(for soundName: String) {
        // Create subtle system sounds for different interactions
        switch soundName {
        case "button_tap":
            triggerHaptic(.light)
        case "success":
            triggerNotificationHaptic(.success)
        case "error":
            triggerNotificationHaptic(.error)
        case "swipe":
            triggerSelectionHaptic()
        default:
            triggerHaptic(.light)
        }
    }
}

// MARK: - Advanced Animation Modifiers

struct DelightfulPressAnimation: ViewModifier {
    @StateObject private var microInteractions = MicroInteractionsManager()
    @State private var isPressed = false
    
    let onPress: () -> Void
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .opacity(isPressed ? 0.8 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
            .onTapGesture {
                withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                    isPressed = true
                }
                
                microInteractions.triggerHaptic(.light)
                microInteractions.playSound("button_tap")
                onPress()
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        isPressed = false
                    }
                }
            }
    }
}

struct FloatingAnimation: ViewModifier {
    @State private var isFloating = false
    let intensity: Double
    
    func body(content: Content) -> some View {
        content
            .offset(y: isFloating ? -intensity * 3 : 0)
            .animation(
                Animation.easeInOut(duration: 2.0 + intensity)
                    .repeatForever(autoreverses: true),
                value: isFloating
            )
            .onAppear {
                isFloating = true
            }
    }
}

struct ShimmerEffect: ViewModifier {
    @State private var phase: CGFloat = 0
    let isActive: Bool
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                .clear,
                                .white.opacity(0.3),
                                .clear
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(30))
                    .offset(x: phase)
                    .animation(
                        isActive ? 
                        Animation.linear(duration: 1.5).repeatForever(autoreverses: false) :
                        .default,
                        value: phase
                    )
            )
            .onAppear {
                if isActive {
                    phase = 300
                }
            }
            .clipped()
    }
}

struct PulseAnimation: ViewModifier {
    @State private var isPulsing = false
    let isActive: Bool
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPulsing ? 1.05 : 1.0)
            .opacity(isPulsing ? 0.8 : 1.0)
            .animation(
                isActive ?
                Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true) :
                .default,
                value: isPulsing
            )
            .onAppear {
                if isActive {
                    isPulsing = true
                }
            }
    }
}

// MARK: - Advanced Gesture Recognizers

struct AdvancedSwipeGesture: ViewModifier {
    @StateObject private var microInteractions = MicroInteractionsManager()
    let onSwipeLeft: (() -> Void)?
    let onSwipeRight: (() -> Void)?
    let onSwipeUp: (() -> Void)?
    let onSwipeDown: (() -> Void)?
    
    @State private var dragOffset = CGSize.zero
    @State private var isDragging = false
    
    func body(content: Content) -> some View {
        content
            .offset(dragOffset)
            .scaleEffect(isDragging ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: dragOffset)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isDragging)
            .gesture(
                DragGesture()
                    .onChanged { value in
                        if !isDragging {
                            isDragging = true
                            microInteractions.triggerSelectionHaptic()
                        }
                        dragOffset = value.translation
                    }
                    .onEnded { value in
                        isDragging = false
                        
                        let threshold: CGFloat = 50
                        let translation = value.translation
                        
                        if abs(translation.width) > abs(translation.height) {
                            // Horizontal swipe
                            if translation.width > threshold {
                                microInteractions.triggerHaptic(.medium)
                                microInteractions.playSound("swipe")
                                onSwipeRight?()
                            } else if translation.width < -threshold {
                                microInteractions.triggerHaptic(.medium)
                                microInteractions.playSound("swipe")
                                onSwipeLeft?()
                            }
                        } else {
                            // Vertical swipe
                            if translation.height > threshold {
                                microInteractions.triggerHaptic(.medium)
                                microInteractions.playSound("swipe")
                                onSwipeDown?()
                            } else if translation.height < -threshold {
                                microInteractions.triggerHaptic(.medium)
                                microInteractions.playSound("swipe")
                                onSwipeUp?()
                            }
                        }
                        
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            dragOffset = .zero
                        }
                    }
            )
    }
}

struct LongPressWithFeedback: ViewModifier {
    @StateObject private var microInteractions = MicroInteractionsManager()
    @State private var isLongPressing = false
    @State private var longPressProgress: Double = 0
    
    let minimumDuration: Double
    let onLongPress: () -> Void
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isLongPressing ? 1.05 : 1.0)
            .overlay(
                Circle()
                    .trim(from: 0, to: longPressProgress)
                    .stroke(VibeFinanceDesignSystem.Colors.accentGold, lineWidth: 3)
                    .rotationEffect(.degrees(-90))
                    .opacity(isLongPressing ? 1 : 0)
            )
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isLongPressing)
            .animation(.linear(duration: 0.1), value: longPressProgress)
            .onLongPressGesture(
                minimumDuration: minimumDuration,
                maximumDistance: 50,
                pressing: { pressing in
                    isLongPressing = pressing
                    if pressing {
                        microInteractions.triggerHaptic(.light)
                        withAnimation(.linear(duration: minimumDuration)) {
                            longPressProgress = 1.0
                        }
                    } else {
                        longPressProgress = 0
                    }
                },
                perform: {
                    microInteractions.triggerNotificationHaptic(.success)
                    microInteractions.playSound("success")
                    onLongPress()
                }
            )
    }
}

// MARK: - View Extensions

extension View {
    func delightfulPress(onPress: @escaping () -> Void) -> some View {
        modifier(DelightfulPressAnimation(onPress: onPress))
    }
    
    func floating(intensity: Double = 1.0) -> some View {
        modifier(FloatingAnimation(intensity: intensity))
    }
    
    func shimmer(isActive: Bool = true) -> some View {
        modifier(ShimmerEffect(isActive: isActive))
    }
    
    func pulse(isActive: Bool = true) -> some View {
        modifier(PulseAnimation(isActive: isActive))
    }
    
    func advancedSwipe(
        onSwipeLeft: (() -> Void)? = nil,
        onSwipeRight: (() -> Void)? = nil,
        onSwipeUp: (() -> Void)? = nil,
        onSwipeDown: (() -> Void)? = nil
    ) -> some View {
        modifier(AdvancedSwipeGesture(
            onSwipeLeft: onSwipeLeft,
            onSwipeRight: onSwipeRight,
            onSwipeUp: onSwipeUp,
            onSwipeDown: onSwipeDown
        ))
    }
    
    func longPressWithFeedback(
        minimumDuration: Double = 1.0,
        onLongPress: @escaping () -> Void
    ) -> some View {
        modifier(LongPressWithFeedback(
            minimumDuration: minimumDuration,
            onLongPress: onLongPress
        ))
    }
}

// MARK: - Warren Buffett Inspired Micro-Interactions

struct BuffettWisdomToast: View {
    let message: String
    let isShowing: Bool
    
    var body: some View {
        if isShowing {
            VStack {
                Spacer()
                
                HStack {
                    Image(systemName: "lightbulb.fill")
                        .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                        .font(.title3)
                    
                    Text(message)
                        .font(.caption)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.8))
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3), lineWidth: 1)
                        )
                )
                .padding(.horizontal, 20)
                .padding(.bottom, 100)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isShowing)
            }
        }
    }
}

struct SuccessConfetti: View {
    @State private var animate = false
    let isActive: Bool
    
    var body: some View {
        ZStack {
            ForEach(0..<20, id: \.self) { index in
                Circle()
                    .fill(VibeFinanceDesignSystem.Colors.accentGold)
                    .frame(width: 6, height: 6)
                    .offset(
                        x: animate ? CGFloat.random(in: -200...200) : 0,
                        y: animate ? CGFloat.random(in: -300...100) : 0
                    )
                    .opacity(animate ? 0 : 1)
                    .animation(
                        .easeOut(duration: 1.5).delay(Double(index) * 0.1),
                        value: animate
                    )
            }
        }
        .onChange(of: isActive) { _, newValue in
            if newValue {
                animate = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                    animate = false
                }
            }
        }
    }
}
