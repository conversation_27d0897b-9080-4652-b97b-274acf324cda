{"mcpServers": {"@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest"], "env": {"API_KEY": "d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019"}}, "ElevenLabs": {"command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": "***************************************************"}}, "supabase": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-supabase@latest"], "env": {"SUPABASE_URL": "https://mcrbwwkltigjawnlunlh.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}, "internet-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search@latest"], "env": {"BRAVE_API_KEY": "YOUR_BRAVE_API_KEY_HERE"}}}}