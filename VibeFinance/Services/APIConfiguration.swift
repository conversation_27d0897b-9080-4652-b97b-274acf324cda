//
//  APIConfiguration.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - API Configuration Manager
class APIConfiguration: ObservableObject {
    static let shared = APIConfiguration()

    init() {}
    
    // MARK: - Supabase Configuration
    struct SupabaseConfig {
        static let url = "https://mcrbwwkltigjawnlunlh.supabase.co"
        static let anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs"
        // Service role key should never be used in client-side code
        // Only use for server-side operations
    }
    
    // MARK: - Gemini AI Configuration
    struct GeminiConfig {
        static let apiKey = "AIzaSyBbYZexlq9JBLpgPayED7A3z7OPsx3Qg_s"
        static let baseURL = "https://generativelanguage.googleapis.com/v1beta"
    }
    
    // MARK: - Polygon.io Configuration
    struct PolygonConfig {
        static let apiKey = "km3MBsQp1wgDfetdB_jcW8nQommpcVtY"
        static let baseURL = "https://api.polygon.io"
    }
    
    // MARK: - Alpaca Markets Configuration (Paper Trading)
    struct AlpacaConfig {
        static let endpoint = "https://paper-api.alpaca.markets/v2"
        static let apiKey = "PK0VGIWNTEFHDMQXLYRX"
        static let secretKey = "5xp16pgQ8BxWv2f0eg6f2DEGjfx3loJCDxSYM0nt"
        static let dataEndpoint = "https://data.alpaca.markets/v2"
    }
    
    // MARK: - News API Configuration
    struct NewsAPIConfig {
        static let apiKey = "********************************"
        static let baseURL = "https://newsapi.org/v2"
    }
    
    // MARK: - API Headers
    func getSupabaseHeaders() -> [String: String] {
        return [
            "apikey": SupabaseConfig.anonKey,
            "Authorization": "Bearer \(SupabaseConfig.anonKey)",
            "Content-Type": "application/json"
        ]
    }
    
    func getGeminiHeaders() -> [String: String] {
        return [
            "Content-Type": "application/json"
        ]
    }
    
    func getPolygonHeaders() -> [String: String] {
        return [
            "Authorization": "Bearer \(PolygonConfig.apiKey)"
        ]
    }
    
    func getAlpacaHeaders() -> [String: String] {
        let credentials = "\(AlpacaConfig.apiKey):\(AlpacaConfig.secretKey)"
        let credentialsData = credentials.data(using: .utf8)!
        let base64Credentials = credentialsData.base64EncodedString()
        
        return [
            "Authorization": "Basic \(base64Credentials)",
            "Content-Type": "application/json"
        ]
    }
    
    func getNewsAPIHeaders() -> [String: String] {
        return [
            "X-API-Key": NewsAPIConfig.apiKey,
            "Content-Type": "application/json"
        ]
    }
    
    // MARK: - URL Builders
    func buildSupabaseURL(endpoint: String) -> URL? {
        return URL(string: "\(SupabaseConfig.url)/rest/v1/\(endpoint)")
    }
    
    func buildGeminiURL(model: String = "gemini-pro") -> URL? {
        return URL(string: "\(GeminiConfig.baseURL)/models/\(model):generateContent?key=\(GeminiConfig.apiKey)")
    }
    
    func buildPolygonURL(endpoint: String) -> URL? {
        return URL(string: "\(PolygonConfig.baseURL)/\(endpoint)?apikey=\(PolygonConfig.apiKey)")
    }
    
    func buildAlpacaURL(endpoint: String) -> URL? {
        return URL(string: "\(AlpacaConfig.endpoint)/\(endpoint)")
    }
    
    func buildAlpacaDataURL(endpoint: String) -> URL? {
        return URL(string: "\(AlpacaConfig.dataEndpoint)/\(endpoint)")
    }
    
    func buildNewsAPIURL(endpoint: String) -> URL? {
        return URL(string: "\(NewsAPIConfig.baseURL)/\(endpoint)")
    }
}

// MARK: - API Response Models
struct APIResponse<T: Codable>: Codable {
    let data: T?
    let error: APIError?
    let message: String?
}

struct APIError: Codable, Error {
    let code: String
    let message: String
    let details: String?
}

// MARK: - Network Manager
class NetworkManager: ObservableObject {
    static let shared = NetworkManager()
    private let session = URLSession.shared
    private let config = APIConfiguration.shared
    
    private init() {}
    
    // MARK: - Generic Request Method
    func request<T: Codable>(
        url: URL,
        method: HTTPMethod = .GET,
        headers: [String: String] = [:],
        body: Data? = nil,
        responseType: T.Type
    ) async throws -> T {
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body
        
        // Add headers
        for (key, value) in headers {
            request.setValue(value, forHTTPHeaderField: key)
        }
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError(code: "INVALID_RESPONSE", message: "Invalid response type", details: nil)
        }
        
        guard 200...299 ~= httpResponse.statusCode else {
            throw APIError(
                code: "HTTP_ERROR",
                message: "HTTP Error: \(httpResponse.statusCode)",
                details: nil
            )
        }
        
        do {
            let decodedResponse = try JSONDecoder().decode(T.self, from: data)
            return decodedResponse
        } catch {
            throw APIError(
                code: "DECODE_ERROR",
                message: "Failed to decode response: \(error.localizedDescription)",
                details: nil
            )
        }
    }
}

// MARK: - HTTP Methods
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case PATCH = "PATCH"
    case DELETE = "DELETE"
}


