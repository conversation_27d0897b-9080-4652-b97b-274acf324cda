//
//  AuthTestHelper.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

class AuthTestHelper {
    static let shared = AuthTestHelper()
    private let supabaseService = SupabaseService.shared

    private init() {}

    func testSupabaseConnection() async -> (success: Bool, message: String) {
        do {
            // Test basic connection by trying to fetch quests (public data)
            let quests = try await supabaseService.fetch(Quest.self, from: "quests")
            return (true, "✅ Supabase connection successful! Found \(quests.count) quests.")
        } catch {
            return (false, "❌ Supabase connection failed: \(error.localizedDescription)")
        }
    }

    func testUserCreation(email: String, username: String) async -> (success: Bool, message: String) {
        do {
            let testUser = User(
                id: UUID(),
                email: email,
                username: username
            )

            try await supabaseService.createUser(testUser)
            return (true, "✅ User creation successful!")
        } catch {
            return (false, "❌ User creation failed: \(error.localizedDescription)")
        }
    }

    func testUserRetrieval(userId: UUID) async -> (success: Bool, message: String) {
        do {
            let user = try await supabaseService.getUser(id: userId)
            return (true, "✅ User retrieval successful! User: \(user.username)")
        } catch {
            return (false, "❌ User retrieval failed: \(error.localizedDescription)")
        }
    }

    func testDatabaseConnection() async -> (success: Bool, message: String) {
        do {
            // Test if we can connect to the database by fetching users table structure
            let url = URL(string: "https://mcrbwwkltigjawnlunlh.supabase.co/rest/v1/users?limit=0")!
            var request = URLRequest(url: url)
            request.setValue("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs", forHTTPHeaderField: "apikey")
            request.setValue("Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs", forHTTPHeaderField: "Authorization")

            let (_, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    return (true, "✅ Database connection successful!")
                } else {
                    return (false, "❌ Database connection failed with status: \(httpResponse.statusCode)")
                }
            }
            return (false, "❌ Invalid response from database")
        } catch {
            return (false, "❌ Database connection error: \(error.localizedDescription)")
        }
    }

    func runFullAuthTest(email: String, password: String, username: String) async -> [String] {
        var results: [String] = []

        // Test 1: Database Connection
        let dbTest = await testDatabaseConnection()
        results.append(dbTest.message)

        if !dbTest.success {
            results.append("💡 Please run the fix_auth_issues.sql script in Supabase")
            return results
        }

        // Test 2: Supabase Connection
        let connectionTest = await testSupabaseConnection()
        results.append(connectionTest.message)

        if !connectionTest.success {
            return results
        }

        // Test 3: Sign up
        do {
            let authResult = try await supabaseService.signUp(email: email, password: password)
            results.append("✅ Sign up successful! User ID: \(authResult.user.id)")

            // Test 4: Create user profile
            let userTest = await testUserCreation(email: email, username: username)
            results.append(userTest.message)

            if userTest.success {
                // Test 5: Retrieve user
                let retrievalTest = await testUserRetrieval(userId: authResult.user.id)
                results.append(retrievalTest.message)
            }

        } catch {
            results.append("❌ Sign up failed: \(error.localizedDescription)")
        }

        return results
    }
}
