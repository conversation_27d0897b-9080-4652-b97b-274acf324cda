//
//  SupabaseService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Supabase Service
class SupabaseService {
    static let shared = SupabaseService()

    private let baseURL = "https://mcrbwwkltigjawnlunlh.supabase.co"
    private let apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs"
    private let session = URLSession.shared
    private var currentAccessToken: String?

    private init() {}

    // MARK: - Authentication

    func signUp(email: String, password: String) async throws -> AuthResult {
        let url = URL(string: "\(baseURL)/auth/v1/signup")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")

        let body = [
            "email": email,
            "password": password
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: body)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw SupabaseError.authenticationFailed
        }

        // Parse response manually like the working direct sign-in
        guard let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let idString = jsonData["id"] as? String,
              let userEmail = jsonData["email"] as? String,
              let userId = UUID(uuidString: idString) else {
            throw SupabaseError.authenticationFailed
        }

        let authUser = AuthUser(
            id: userId,
            email: userEmail,
            emailConfirmedAt: nil,
            createdAt: Date()
        )

        let authResult = AuthResult(user: authUser, session: nil)

        // Store the access token for authenticated requests
        if let session = authResult.session {
            currentAccessToken = session.accessToken
        }

        return authResult
    }

    func signIn(email: String, password: String) async throws -> AuthResult {
        let url = URL(string: "\(baseURL)/auth/v1/token?grant_type=password")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")

        let body = [
            "email": email,
            "password": password
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: body)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw SupabaseError.networkError
        }

        guard httpResponse.statusCode == 200 else {
            // Log the actual error response
            let errorString = String(data: data, encoding: .utf8) ?? "Unknown error"
            print("🔴 Sign-in failed with status \(httpResponse.statusCode): \(errorString)")
            throw SupabaseError.authenticationFailed
        }

        // Parse response manually like the working direct sign-in
        guard let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let userDict = jsonData["user"] as? [String: Any],
              let idString = userDict["id"] as? String,
              let userEmail = userDict["email"] as? String,
              let userId = UUID(uuidString: idString) else {
            throw SupabaseError.authenticationFailed
        }

        // Extract access token for authenticated requests
        if let accessToken = jsonData["access_token"] as? String {
            currentAccessToken = accessToken
        }

        let authUser = AuthUser(
            id: userId,
            email: userEmail,
            emailConfirmedAt: nil,
            createdAt: Date()
        )

        let authResult = AuthResult(user: authUser, session: nil)

        return authResult
    }

    func signOut() async throws {
        let url = URL(string: "\(baseURL)/auth/v1/logout")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue(apiKey, forHTTPHeaderField: "apikey")

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 204 else {
            throw SupabaseError.signOutFailed
        }
    }

    func resetPassword(email: String) async throws {
        let url = URL(string: "\(baseURL)/auth/v1/recover")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")

        let body = ["email": email]
        request.httpBody = try JSONSerialization.data(withJSONObject: body)

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw SupabaseError.resetPasswordFailed
        }
    }

    func getCurrentSession() async throws -> AuthSession? {
        // This would typically check stored tokens and validate them
        // For now, return nil to indicate no active session
        return nil
    }

    // MARK: - User Management

    func createUser(_ user: User) async throws {
        let url = URL(string: "\(baseURL)/rest/v1/users")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")

        // Use the access token if available, otherwise fall back to API key
        if let accessToken = currentAccessToken {
            request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        } else {
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        }

        // Convert User to database-compatible format
        let dbUser = DatabaseUser(from: user)
        request.httpBody = try JSONEncoder().encode(dbUser)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw SupabaseError.networkError
        }

        guard httpResponse.statusCode == 201 else {
            // Log the actual error response
            let errorString = String(data: data, encoding: .utf8) ?? "Unknown error"
            print("🔴 Create user failed with status \(httpResponse.statusCode): \(errorString)")
            throw SupabaseError.createUserFailed
        }
    }

    func getUser(id: UUID) async throws -> User {
        let url = URL(string: "\(baseURL)/rest/v1/users?id=eq.\(id.uuidString)")!
        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "apikey")

        // Use the access token if available, otherwise fall back to API key
        if let accessToken = currentAccessToken {
            request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        } else {
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        }

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw SupabaseError.userNotFound
        }

        let dbUsers = try JSONDecoder().decode([DatabaseUser].self, from: data)
        guard let dbUser = dbUsers.first else {
            throw SupabaseError.userNotFound
        }

        return dbUser.toUser()
    }

    func updateUser(_ user: User) async throws {
        let url = URL(string: "\(baseURL)/rest/v1/users?id=eq.\(user.id.uuidString)")!
        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        request.httpBody = try JSONEncoder().encode(user)

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 204 else {
            throw SupabaseError.updateUserFailed
        }
    }

    func deleteUser(id: UUID) async throws {
        let url = URL(string: "\(baseURL)/rest/v1/users?id=eq.\(id.uuidString)")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue(apiKey, forHTTPHeaderField: "apikey")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 204 else {
            throw SupabaseError.deleteUserFailed
        }
    }

    // MARK: - Generic CRUD Operations

    func create<T: Codable>(_ item: T, table: String) async throws {
        let url = URL(string: "\(baseURL)/rest/v1/\(table)")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        request.httpBody = try JSONEncoder().encode(item)

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 201 else {
            throw SupabaseError.createFailed
        }
    }

    func fetch<T: Codable>(_ type: T.Type, from table: String, where condition: String? = nil) async throws -> [T] {
        var urlString = "\(baseURL)/rest/v1/\(table)"
        if let condition = condition {
            urlString += "?\(condition)"
        }

        let url = URL(string: urlString)!
        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "apikey")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw SupabaseError.fetchFailed
        }

        return try JSONDecoder().decode([T].self, from: data)
    }

    func update(_ data: [String: Any], table: String, where condition: String) async throws {
        let url = URL(string: "\(baseURL)/rest/v1/\(table)?\(condition)")!
        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        request.httpBody = try JSONSerialization.data(withJSONObject: data)

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 || httpResponse.statusCode == 204 else {
            throw SupabaseError.updateFailed
        }
    }

    func delete(from table: String, where condition: String) async throws {
        let url = URL(string: "\(baseURL)/rest/v1/\(table)?\(condition)")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue(apiKey, forHTTPHeaderField: "apikey")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 || httpResponse.statusCode == 204 else {
            throw SupabaseError.deleteFailed
        }
    }

    func update<T: Codable>(_ item: T, in table: String, where condition: String) async throws {
        let url = URL(string: "\(baseURL)/rest/v1/\(table)?\(condition)")!
        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "apikey")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")

        request.httpBody = try JSONEncoder().encode(item)

        let (_, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 204 else {
            throw SupabaseError.updateFailed
        }
    }


}

// MARK: - Auth Models
struct AuthResult: Codable {
    let user: AuthUser
    let session: AuthSession?

    // Simple initializer for manual creation
    init(user: AuthUser, session: AuthSession?) {
        self.user = user
        self.session = session
    }

    // Simple approach - just extract what we need
    init(from decoder: Decoder) throws {
        // Try to decode as a complete response first
        if let container = try? decoder.container(keyedBy: CodingKeys.self) {
            if let user = try? container.decode(AuthUser.self, forKey: .user) {
                self.user = user
                self.session = try? container.decode(AuthSession.self, forKey: .session)
            } else {
                // Fallback: treat the whole response as user data
                self.user = try AuthUser(from: decoder)
                self.session = nil
            }
        } else {
            // Last resort: treat the whole response as user data
            self.user = try AuthUser(from: decoder)
            self.session = nil
        }
    }

    enum CodingKeys: String, CodingKey {
        case user, session
    }
}

struct AuthUser: Codable {
    let id: UUID
    let email: String
    let emailConfirmedAt: Date?
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id
        case email
        case emailConfirmedAt = "email_confirmed_at"
        case createdAt = "created_at"
    }

    // Simple initializer for manual creation
    init(id: UUID, email: String, emailConfirmedAt: Date?, createdAt: Date) {
        self.id = id
        self.email = email
        self.emailConfirmedAt = emailConfirmedAt
        self.createdAt = createdAt
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // Handle the id field - it might be a string that needs to be converted to UUID
        if let idString = try? container.decode(String.self, forKey: .id) {
            guard let uuid = UUID(uuidString: idString) else {
                throw DecodingError.dataCorrupted(DecodingError.Context(codingPath: [CodingKeys.id], debugDescription: "Invalid UUID string"))
            }
            self.id = uuid
        } else {
            self.id = try container.decode(UUID.self, forKey: .id)
        }

        self.email = try container.decode(String.self, forKey: .email)

        // Handle optional email confirmation date
        if let confirmationString = try? container.decode(String.self, forKey: .emailConfirmedAt) {
            let formatter = ISO8601DateFormatter()
            self.emailConfirmedAt = formatter.date(from: confirmationString)
        } else {
            self.emailConfirmedAt = nil
        }

        // Handle created_at date
        let createdAtString = try container.decode(String.self, forKey: .createdAt)
        let formatter = ISO8601DateFormatter()
        guard let date = formatter.date(from: createdAtString) else {
            throw DecodingError.dataCorrupted(DecodingError.Context(codingPath: [CodingKeys.createdAt], debugDescription: "Invalid date format"))
        }
        self.createdAt = date
    }
}

struct AuthSession: Codable {
    let accessToken: String
    let refreshToken: String
    let expiresIn: Int
    let user: AuthUser

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case expiresIn = "expires_in"
        case user
    }
}

// MARK: - Supabase Errors
enum SupabaseError: LocalizedError {
    case authenticationFailed
    case signOutFailed
    case resetPasswordFailed
    case createUserFailed
    case userNotFound
    case updateUserFailed
    case deleteUserFailed
    case createFailed
    case fetchFailed
    case updateFailed
    case deleteFailed
    case networkError

    var errorDescription: String? {
        switch self {
        case .authenticationFailed:
            return "Authentication failed"
        case .signOutFailed:
            return "Sign out failed"
        case .resetPasswordFailed:
            return "Password reset failed"
        case .createUserFailed:
            return "Failed to create user"
        case .userNotFound:
            return "User not found"
        case .updateUserFailed:
            return "Failed to update user"
        case .deleteUserFailed:
            return "Failed to delete user"
        case .createFailed:
            return "Failed to create record"
        case .fetchFailed:
            return "Failed to fetch data"
        case .updateFailed:
            return "Failed to update record"
        case .deleteFailed:
            return "Failed to delete record"
        case .networkError:
            return "Network error"
        }
    }
}

// MARK: - Database User Model
struct DatabaseUser: Codable {
    let id: UUID
    let email: String
    let username: String
    let preferences: DatabaseUserPreferences
    let subscription_status: String
    let xp: Int
    let level: Int
    let created_at: String
    let updated_at: String

    enum CodingKeys: String, CodingKey {
        case id, email, username, preferences
        case subscription_status = "subscription_status"
        case xp, level
        case created_at = "created_at"
        case updated_at = "updated_at"
    }
}

struct DatabaseUserPreferences: Codable {
    let interests: [String]
    let goals: [String]
    let riskTolerance: String
    let preferredLanguage: String
    let notificationsEnabled: Bool
    let dailyQuestTime: String
}

extension DatabaseUser {
    init(from user: User) {
        self.id = user.id
        self.email = user.email
        self.username = user.username
        self.preferences = DatabaseUserPreferences(
            interests: user.preferences.interests,
            goals: user.preferences.goals,
            riskTolerance: user.preferences.riskTolerance.rawValue,
            preferredLanguage: user.preferences.preferredLanguage,
            notificationsEnabled: user.preferences.notificationsEnabled,
            dailyQuestTime: user.preferences.dailyQuestTime
        )
        self.subscription_status = user.subscriptionStatus.rawValue
        self.xp = user.xp
        self.level = user.level

        let formatter = ISO8601DateFormatter()
        self.created_at = formatter.string(from: user.createdAt)
        self.updated_at = formatter.string(from: user.updatedAt)
    }

    func toUser() -> User {
        let formatter = ISO8601DateFormatter()
        let createdDate = formatter.date(from: created_at) ?? Date()
        let updatedDate = formatter.date(from: updated_at) ?? Date()

        var userPreferences = UserPreferences()
        userPreferences.interests = preferences.interests
        userPreferences.goals = preferences.goals
        userPreferences.riskTolerance = RiskTolerance(rawValue: preferences.riskTolerance) ?? .moderate
        userPreferences.preferredLanguage = preferences.preferredLanguage
        userPreferences.notificationsEnabled = preferences.notificationsEnabled
        userPreferences.dailyQuestTime = preferences.dailyQuestTime

        let subscriptionTier = SubscriptionTier(rawValue: subscription_status) ?? .free

        return User(
            id: id,
            email: email,
            username: username,
            preferences: userPreferences,
            subscriptionStatus: subscriptionTier,
            xp: xp,
            level: level,
            createdAt: createdDate,
            updatedAt: updatedDate
        )
    }
}

