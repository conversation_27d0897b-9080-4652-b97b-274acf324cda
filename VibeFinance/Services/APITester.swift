//
//  APITester.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - API Testing Service
class APITester {
    static let shared = APITester()
    private init() {}
    
    // MARK: - Test All APIs
    func testAllAPIs() async {
        print("🧪 Testing WealthVibe API Connections...")
        print("=" * 50)
        
        await testSupabase()
        await testGeminiAI()
        await testPolygonIO()
        await testNewsAPI()
        await testAlpaca()
        
        print("=" * 50)
        print("✅ API Testing Complete!")
    }
    
    // MARK: - Individual API Tests
    
    func testSupabase() async {
        print("🔵 Testing Supabase...")
        
        let url = "https://mcrbwwkltigjawnlunlh.supabase.co/rest/v1/"
        guard let requestURL = URL(string: url) else {
            print("❌ Supabase: Invalid URL")
            return
        }
        
        var request = URLRequest(url: requestURL)
        request.setValue("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI0NzE5NzQsImV4cCI6MjA0ODA0Nzk3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5E", forHTTPHeaderField: "apikey")
        
        do {
            let (_, response) = try await URLSession.shared.data(for: request)
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    print("✅ Supabase: Connected successfully")
                } else {
                    print("⚠️ Supabase: Connected but returned status \(httpResponse.statusCode)")
                }
            }
        } catch {
            print("❌ Supabase: Connection failed - \(error.localizedDescription)")
        }
    }
    
    func testGeminiAI() async {
        print("🤖 Testing Gemini AI...")
        
        let url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=AIzaSyBbYZexlq9JBLpgPayED7A3z7OPsx3Qg_s"
        guard let requestURL = URL(string: url) else {
            print("❌ Gemini AI: Invalid URL")
            return
        }
        
        var request = URLRequest(url: requestURL)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let testBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Say 'Hello WealthVibe!' in a Gen Z way"]
                    ]
                ]
            ]
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: testBody)
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    if let jsonResponse = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let candidates = jsonResponse["candidates"] as? [[String: Any]],
                       let firstCandidate = candidates.first,
                       let content = firstCandidate["content"] as? [String: Any],
                       let parts = content["parts"] as? [[String: Any]],
                       let firstPart = parts.first,
                       let text = firstPart["text"] as? String {
                        print("✅ Gemini AI: Connected successfully")
                        print("🤖 Response: \(text.trimmingCharacters(in: .whitespacesAndNewlines))")
                    } else {
                        print("⚠️ Gemini AI: Connected but couldn't parse response")
                    }
                } else {
                    print("❌ Gemini AI: HTTP \(httpResponse.statusCode)")
                }
            }
        } catch {
            print("❌ Gemini AI: Connection failed - \(error.localizedDescription)")
        }
    }
    
    func testPolygonIO() async {
        print("📈 Testing Polygon.io...")
        
        let url = "https://api.polygon.io/v2/last/trade/AAPL?apikey=km3MBsQp1wgDfetdB_jcW8nQommpcVtY"
        guard let requestURL = URL(string: url) else {
            print("❌ Polygon.io: Invalid URL")
            return
        }
        
        do {
            let (data, response) = try await URLSession.shared.data(from: requestURL)
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    if let jsonResponse = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let results = jsonResponse["results"] as? [String: Any],
                       let price = results["p"] as? Double {
                        print("✅ Polygon.io: Connected successfully")
                        print("📈 AAPL Price: $\(price)")
                    } else {
                        print("⚠️ Polygon.io: Connected but couldn't parse response")
                    }
                } else {
                    print("❌ Polygon.io: HTTP \(httpResponse.statusCode)")
                }
            }
        } catch {
            print("❌ Polygon.io: Connection failed - \(error.localizedDescription)")
        }
    }
    
    func testNewsAPI() async {
        print("📰 Testing NewsAPI...")
        
        let url = "https://newsapi.org/v2/everything?q=finance&language=en&pageSize=1&apiKey=17e9172b28904d22ac42dd64872e54d0"
        guard let requestURL = URL(string: url) else {
            print("❌ NewsAPI: Invalid URL")
            return
        }
        
        do {
            let (data, response) = try await URLSession.shared.data(from: requestURL)
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    if let jsonResponse = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let articles = jsonResponse["articles"] as? [[String: Any]],
                       let firstArticle = articles.first,
                       let title = firstArticle["title"] as? String {
                        print("✅ NewsAPI: Connected successfully")
                        print("📰 Latest: \(title)")
                    } else {
                        print("⚠️ NewsAPI: Connected but couldn't parse response")
                    }
                } else {
                    print("❌ NewsAPI: HTTP \(httpResponse.statusCode)")
                }
            }
        } catch {
            print("❌ NewsAPI: Connection failed - \(error.localizedDescription)")
        }
    }
    
    func testAlpaca() async {
        print("🦙 Testing Alpaca...")
        
        let url = "https://paper-api.alpaca.markets/v2/account"
        guard let requestURL = URL(string: url) else {
            print("❌ Alpaca: Invalid URL")
            return
        }
        
        var request = URLRequest(url: requestURL)
        let credentials = "PK0VGIWNTEFHDMQXLYRX:5xp16pgQ8BxWv2f0eg6f2DEGjfx3loJCDxSYM0nt"
        let credentialsData = credentials.data(using: .utf8)!
        let base64Credentials = credentialsData.base64EncodedString()
        request.setValue("Basic \(base64Credentials)", forHTTPHeaderField: "Authorization")
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode == 200 {
                    if let jsonResponse = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let buyingPower = jsonResponse["buying_power"] as? String {
                        print("✅ Alpaca: Connected successfully")
                        print("🦙 Buying Power: $\(buyingPower)")
                    } else {
                        print("⚠️ Alpaca: Connected but couldn't parse response")
                    }
                } else {
                    print("❌ Alpaca: HTTP \(httpResponse.statusCode)")
                }
            }
        } catch {
            print("❌ Alpaca: Connection failed - \(error.localizedDescription)")
        }
    }
}

// MARK: - String Extension for Repeat
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
