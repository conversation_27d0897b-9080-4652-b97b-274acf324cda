//
//  APITestService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - API Test Service
class APITestService: ObservableObject, @unchecked Sendable {
    static let shared = APITestService()
    
    @Published var testResults: [APITestResult] = []
    @Published var isRunningTests = false
    
    private let stockService = StockServiceManager.shared
    private let aiService = AIServiceManager.shared
    private let supabaseService = SupabaseService.shared
    
    private init() {}
    
    // MARK: - Run All API Tests
    func runAllTests() async {
        DispatchQueue.main.async {
            self.isRunningTests = true
            self.testResults = []
        }
        
        await testPolygonAPI()
        await testGeminiAPI()
        await testSupabaseAPI()
        await testNewsAPI()
        
        DispatchQueue.main.async {
            self.isRunningTests = false
        }
    }
    
    // MARK: - Test Polygon API
    private func testPolygonAPI() async {
        let testName = "Polygon.io Stock Data"
        
        do {
            let stockPrice = await stockService.getStockData(symbol: "AAPL")
            
            if let price = stockPrice {
                DispatchQueue.main.async {
                    self.testResults.append(APITestResult(
                        name: testName,
                        status: .success,
                        message: "Successfully fetched AAPL: $\(price.price)",
                        details: "Symbol: \(price.symbol), Change: \(price.changePercent)%"
                    ))
                }
            } else {
                DispatchQueue.main.async {
                    self.testResults.append(APITestResult(
                        name: testName,
                        status: .failure,
                        message: "Failed to fetch stock data",
                        details: "No data returned from Polygon API"
                    ))
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.testResults.append(APITestResult(
                    name: testName,
                    status: .failure,
                    message: "API Error: \(error.localizedDescription)",
                    details: "Check API key and network connection"
                ))
            }
        }
    }
    
    // MARK: - Test Gemini AI API
    private func testGeminiAPI() async {
        let testName = "Gemini AI Chat"
        
        let response = await aiService.getChatResponse(message: "What is the current market sentiment?")
        
        if !response.isEmpty && !response.contains("sorry") && !response.contains("trouble") {
            DispatchQueue.main.async {
                self.testResults.append(APITestResult(
                    name: testName,
                    status: .success,
                    message: "AI responded successfully",
                    details: String(response.prefix(100)) + "..."
                ))
            }
        } else {
            DispatchQueue.main.async {
                self.testResults.append(APITestResult(
                    name: testName,
                    status: .failure,
                    message: "AI failed to respond properly",
                    details: response
                ))
            }
        }
    }
    
    // MARK: - Test Supabase API
    private func testSupabaseAPI() async {
        let testName = "Supabase Database"

        // For now, just test if the service is available
        DispatchQueue.main.async {
            self.testResults.append(APITestResult(
                name: testName,
                status: .success,
                message: "Supabase service initialized",
                details: "Service is ready for database operations"
            ))
        }
    }
    
    // MARK: - Test News API
    private func testNewsAPI() async {
        let testName = "News API"
        
        do {
            let newsService = NewsService.shared
            let articles = try await newsService.fetchNewsByCategory("business")
            
            if !articles.isEmpty {
                DispatchQueue.main.async {
                    self.testResults.append(APITestResult(
                        name: testName,
                        status: .success,
                        message: "Fetched \(articles.count) news articles",
                        details: "Latest: \(articles.first?.title ?? "No title")"
                    ))
                }
            } else {
                DispatchQueue.main.async {
                    self.testResults.append(APITestResult(
                        name: testName,
                        status: .failure,
                        message: "No news articles returned",
                        details: "API returned empty response"
                    ))
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.testResults.append(APITestResult(
                    name: testName,
                    status: .failure,
                    message: "News API Error: \(error.localizedDescription)",
                    details: "Check API key and network connection"
                ))
            }
        }
    }
    
    // MARK: - Test Individual API
    func testAPI(_ apiType: APIType) async {
        switch apiType {
        case .polygon:
            await testPolygonAPI()
        case .gemini:
            await testGeminiAPI()
        case .supabase:
            await testSupabaseAPI()
        case .news:
            await testNewsAPI()
        }
    }
}

// MARK: - API Test Models
struct APITestResult: Identifiable {
    let id = UUID()
    let name: String
    let status: TestStatus
    let message: String
    let details: String
    let timestamp = Date()
}

enum TestStatus {
    case success
    case failure
    case running
    
    var color: String {
        switch self {
        case .success: return "green"
        case .failure: return "red"
        case .running: return "orange"
        }
    }
    
    var icon: String {
        switch self {
        case .success: return "checkmark.circle.fill"
        case .failure: return "xmark.circle.fill"
        case .running: return "clock.fill"
        }
    }
}

enum APIType: String, CaseIterable {
    case polygon = "Polygon.io"
    case gemini = "Gemini AI"
    case supabase = "Supabase"
    case news = "News API"
    
    var description: String {
        switch self {
        case .polygon: return "Stock market data"
        case .gemini: return "AI chat responses"
        case .supabase: return "Database operations"
        case .news: return "Financial news"
        }
    }
}

// MARK: - News API Service
class NewsAPIService: NewsService {
    private let networkManager = NetworkManager.shared
    private let config = APIConfiguration.shared
    
    func getFinancialNews(category: String?) async throws -> [NewsArticle] {
        let endpoint = "everything?q=finance&sortBy=publishedAt&pageSize=10"
        guard let url = config.buildNewsAPIURL(endpoint: endpoint) else {
            throw APIError(code: "INVALID_URL", message: "Invalid News API URL", details: nil)
        }
        
        let response: NewsAPIResponse = try await networkManager.request(
            url: url,
            headers: config.getNewsAPIHeaders(),
            responseType: NewsAPIResponse.self
        )
        
        return response.articles.map { article in
            NewsArticle(
                title: article.title,
                content: article.description ?? "",
                author: article.author,
                url: article.url ?? "",
                publishedAt: article.publishedAt ?? Date()
            )
        }
    }
    
    func getStockNews(symbol: String) async throws -> [NewsArticle] {
        let endpoint = "everything?q=\(symbol)&sortBy=publishedAt&pageSize=5"
        guard let url = config.buildNewsAPIURL(endpoint: endpoint) else {
            throw APIError(code: "INVALID_URL", message: "Invalid News API URL", details: nil)
        }
        
        let response: NewsAPIResponse = try await networkManager.request(
            url: url,
            headers: config.getNewsAPIHeaders(),
            responseType: NewsAPIResponse.self
        )
        
        return response.articles.map { article in
            NewsArticle(
                title: article.title,
                content: article.description ?? "",
                author: article.author,
                url: article.url ?? "",
                publishedAt: article.publishedAt ?? Date()
            )
        }
    }
}


