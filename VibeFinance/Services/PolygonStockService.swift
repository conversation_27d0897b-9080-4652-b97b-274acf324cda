//
//  PolygonStockService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Polygon Stock Data Service
class PolygonStockService: StockDataService, ObservableObject {
    private let networkManager = NetworkManager.shared
    private let config = APIConfiguration.shared

    init() {}
    
    // MARK: - Get Stock Price
    func getStockPrice(symbol: String) async throws -> StockPrice {
        guard let url = config.buildPolygonURL(endpoint: "v2/aggs/ticker/\(symbol)/prev") else {
            throw APIError(code: "INVALID_URL", message: "Invalid URL for stock price", details: nil)
        }
        
        let response: PolygonPrevCloseResponse = try await networkManager.request(
            url: url,
            headers: config.getPolygonHeaders(),
            responseType: PolygonPrevCloseResponse.self
        )
        
        guard let result = response.results?.first else {
            throw APIError(code: "NO_DATA", message: "No data available for symbol: \(symbol)", details: nil)
        }
        
        return StockPrice(
            symbol: symbol,
            price: result.c,
            change: result.c - result.o,
            changePercent: ((result.c - result.o) / result.o) * 100,
            timestamp: Date()
        )
    }
    
    // MARK: - Get Stock Quote
    func getStockQuote(symbol: String) async throws -> StockQuote {
        guard let url = config.buildPolygonURL(endpoint: "v2/aggs/ticker/\(symbol)/prev") else {
            throw APIError(code: "INVALID_URL", message: "Invalid URL for stock quote", details: nil)
        }
        
        let response: PolygonPrevCloseResponse = try await networkManager.request(
            url: url,
            headers: config.getPolygonHeaders(),
            responseType: PolygonPrevCloseResponse.self
        )
        
        guard let result = response.results?.first else {
            throw APIError(code: "NO_DATA", message: "No data available for symbol: \(symbol)", details: nil)
        }
        
        return StockQuote(
            symbol: symbol,
            open: result.o,
            high: result.h,
            low: result.l,
            close: result.c,
            volume: result.v,
            timestamp: Date()
        )
    }
    
    // MARK: - Get Market Data
    func getMarketData() async throws -> MarketData {
        // Get major indices
        let indices = try await getMarketIndices()
        
        // Get top gainers/losers (simplified - in real app would use different endpoints)
        let topGainers = try await getTopMovers(type: "gainers")
        let topLosers = try await getTopMovers(type: "losers")
        let mostActive = try await getTopMovers(type: "active")
        
        return MarketData(
            indices: indices,
            topGainers: topGainers,
            topLosers: topLosers,
            mostActive: mostActive
        )
    }
    
    // MARK: - Get Market Indices
    private func getMarketIndices() async throws -> [MarketIndex] {
        let symbols = ["SPY", "QQQ", "DIA", "IWM"] // ETFs representing major indices
        var indices: [MarketIndex] = []
        
        for symbol in symbols {
            do {
                let stockPrice = try await getStockPrice(symbol: symbol)
                let index = MarketIndex(
                    name: getIndexName(for: symbol),
                    symbol: symbol,
                    value: stockPrice.price,
                    change: stockPrice.change,
                    changePercent: stockPrice.changePercent
                )
                indices.append(index)
            } catch {
                print("Failed to get data for \(symbol): \(error)")
            }
        }
        
        return indices
    }
    
    // MARK: - Get Top Movers
    private func getTopMovers(type: String) async throws -> [StockPrice] {
        // Simplified implementation - in real app would use Polygon's grouped daily endpoint
        let popularStocks = ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA", "AMZN", "META", "NFLX"]
        var movers: [StockPrice] = []
        
        for symbol in popularStocks.prefix(5) {
            do {
                let stockPrice = try await getStockPrice(symbol: symbol)
                movers.append(stockPrice)
            } catch {
                print("Failed to get data for \(symbol): \(error)")
            }
        }
        
        // Sort based on type
        switch type {
        case "gainers":
            return movers.sorted { $0.changePercent > $1.changePercent }
        case "losers":
            return movers.sorted { $0.changePercent < $1.changePercent }
        case "active":
            return movers // In real app, would sort by volume
        default:
            return movers
        }
    }
    
    // MARK: - Helper Methods
    private func getIndexName(for symbol: String) -> String {
        switch symbol {
        case "SPY": return "S&P 500"
        case "QQQ": return "NASDAQ 100"
        case "DIA": return "Dow Jones"
        case "IWM": return "Russell 2000"
        default: return symbol
        }
    }
    
    // MARK: - Get Real-time Quote
    func getRealTimeQuote(symbol: String) async throws -> RealTimeQuote {
        guard let url = config.buildPolygonURL(endpoint: "v1/last/stocks/\(symbol)") else {
            throw APIError(code: "INVALID_URL", message: "Invalid URL for real-time quote", details: nil)
        }
        
        let response: PolygonLastTradeResponse = try await networkManager.request(
            url: url,
            headers: config.getPolygonHeaders(),
            responseType: PolygonLastTradeResponse.self
        )
        
        return RealTimeQuote(
            symbol: symbol,
            price: response.last.price,
            size: response.last.size,
            timestamp: Date(timeIntervalSince1970: TimeInterval(response.last.timestamp / 1000))
        )
    }
    
    // MARK: - Get Historical Data
    func getHistoricalData(symbol: String, timespan: String = "day", from: Date, to: Date) async throws -> [HistoricalDataPoint] {
        let fromString = ISO8601DateFormatter().string(from: from).prefix(10)
        let toString = ISO8601DateFormatter().string(from: to).prefix(10)
        
        guard let url = config.buildPolygonURL(endpoint: "v2/aggs/ticker/\(symbol)/range/1/\(timespan)/\(fromString)/\(toString)") else {
            throw APIError(code: "INVALID_URL", message: "Invalid URL for historical data", details: nil)
        }
        
        let response: PolygonAggregatesResponse = try await networkManager.request(
            url: url,
            headers: config.getPolygonHeaders(),
            responseType: PolygonAggregatesResponse.self
        )
        
        return response.results?.map { result in
            HistoricalDataPoint(
                timestamp: Date(timeIntervalSince1970: TimeInterval(result.t / 1000)),
                open: result.o,
                high: result.h,
                low: result.l,
                close: result.c,
                volume: result.v
            )
        } ?? []
    }
}

// MARK: - Polygon API Response Models
struct PolygonPrevCloseResponse: Codable {
    let status: String
    let results: [PolygonPrevCloseResult]?
}

struct PolygonPrevCloseResult: Codable {
    let o: Double // open
    let h: Double // high
    let l: Double // low
    let c: Double // close
    let v: Int    // volume
}

struct PolygonLastTradeResponse: Codable {
    let status: String
    let last: PolygonLastTrade
}

struct PolygonLastTrade: Codable {
    let price: Double
    let size: Int
    let timestamp: Int64
}

struct PolygonAggregatesResponse: Codable {
    let status: String
    let results: [PolygonAggregateResult]?
}

struct PolygonAggregateResult: Codable {
    let t: Int64  // timestamp
    let o: Double // open
    let h: Double // high
    let l: Double // low
    let c: Double // close
    let v: Int    // volume
}

// MARK: - Additional Data Models
struct RealTimeQuote: Codable {
    let symbol: String
    let price: Double
    let size: Int
    let timestamp: Date
}

struct HistoricalDataPoint: Codable {
    let timestamp: Date
    let open: Double
    let high: Double
    let low: Double
    let close: Double
    let volume: Int
}

// MARK: - Stock Service Manager
class StockServiceManager: ObservableObject, @unchecked Sendable {
    static let shared = StockServiceManager()
    
    private let polygonService = PolygonStockService()
    @Published var isLoading = false
    @Published var error: APIError?
    
    private init() {}
    
    func getStockData(symbol: String) async -> StockPrice? {
        isLoading = true
        error = nil
        
        do {
            let stockPrice = try await polygonService.getStockPrice(symbol: symbol)
            DispatchQueue.main.async {
                self.isLoading = false
            }
            return stockPrice
        } catch let apiError as APIError {
            DispatchQueue.main.async {
                self.error = apiError
                self.isLoading = false
            }
            return nil
        } catch {
            DispatchQueue.main.async {
                self.error = APIError(code: "UNKNOWN", message: error.localizedDescription, details: nil)
                self.isLoading = false
            }
            return nil
        }
    }
    
    func getMarketOverview() async -> MarketData? {
        isLoading = true
        error = nil
        
        do {
            let marketData = try await polygonService.getMarketData()
            DispatchQueue.main.async {
                self.isLoading = false
            }
            return marketData
        } catch let apiError as APIError {
            DispatchQueue.main.async {
                self.error = apiError
                self.isLoading = false
            }
            return nil
        } catch {
            DispatchQueue.main.async {
                self.error = APIError(code: "UNKNOWN", message: error.localizedDescription, details: nil)
                self.isLoading = false
            }
            return nil
        }
    }
}
