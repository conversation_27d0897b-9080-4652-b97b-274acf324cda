//
//  BuildTest.swift
//  VibeFinance - Build Test
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  Simple test to verify build success
//

import Foundation
import SwiftUI

/// Simple build test
struct BuildTest {
    
    /// Test that core managers can be instantiated
    @MainActor static func testManagerInstantiation() -> Bool {
        let _ = UserManager()
        let _ = AuthManager()
        let _ = FeedManager()
        let _ = QuestManager()
        let _ = SquadManager()
        let _ = SimulatorManager()
        let _ = CacheManager.shared
        let _ = ImageCacheManager.shared
        let _ = NetworkOptimizer.shared

        print("✅ All managers instantiated successfully")
        return true
    }
    
    /// Test that data models can be created
    static func testDataModels() -> Bool {
        let _ = User(email: "<EMAIL>", username: "test", preferences: UserPreferences())
        let _ = Portfolio(totalValue: 0, todayChange: 0, todayChangePercent: 0, holdings: [])
        let _ = Quest(title: "Test", description: "Test", category: .stocks, difficulty: .beginner, xpReward: 10, estimatedTime: 5, tasks: [])
        let _ = Squad(name: "Test", description: "Test", emoji: "🚀", creatorID: UUID(), isPublic: true, maxMembers: 10)

        print("✅ All data models created successfully")
        return true
    }
    
    /// Test that views can be instantiated
    static func testViewInstantiation() -> Bool {
        let _ = MainTabView()
        let _ = BuffettInspiredFeedView()
        let _ = BuffettQuestsView()
        let _ = BuffettChatView()
        let _ = ProfileView()

        print("✅ All views instantiated successfully")
        return true
    }
    
    /// Run all tests
    @MainActor static func runAllTests() async -> Bool {
        print("🔨 Running VibeFinance Build Tests...")
        
        let managersTest = testManagerInstantiation()
        let modelsTest = testDataModels()
        let viewsTest = testViewInstantiation()
        
        let allPassed = managersTest && modelsTest && viewsTest
        
        if allPassed {
            print("🎉 All build tests passed! Build is successful.")
        } else {
            print("❌ Some build tests failed.")
        }
        
        return allPassed
    }
}

/// Simple view to test build
struct BuildTestView: View {
    @State private var testResult: String = "Ready to test"
    @State private var isRunning = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("VibeFinance Build Test")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text(testResult)
                .font(.headline)
                .foregroundColor(testResult.contains("✅") ? .green : (testResult.contains("❌") ? .red : .blue))
            
            Button("Run Build Test") {
                runTest()
            }
            .disabled(isRunning)
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isRunning ? Color.gray : Color.blue)
            )
        }
        .padding()
    }
    
    private func runTest() {
        isRunning = true
        testResult = "Running tests..."
        
        Task {
            let success = await BuildTest.runAllTests()

            await MainActor.run {
                testResult = success ? "✅ Build Successful!" : "❌ Build Failed"
                isRunning = false
            }
        }
    }
}

#Preview {
    BuildTestView()
}
