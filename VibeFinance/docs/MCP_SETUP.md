# Complete MCP Setup Guide for VibeFinance

This guide will help you set up all MCP (Model Context Protocol) servers to supercharge your VibeFinance development experience with AI-powered tools.

## What are MCP Servers?

MCP servers provide specialized AI capabilities that integrate directly into your IDE. VibeFinance uses multiple MCP servers for different purposes:

### 🎨 21st.dev Magic MCP
AI-driven UI component generation through natural language descriptions.

### 🔊 ElevenLabs MCP  
High-quality text-to-speech generation for app audio features.

### 🗄️ Supabase MCP
Direct database operations and queries from your IDE.

### 🧠 Sequential Thinking MCP
Enhanced reasoning and step-by-step problem solving.

### 🔍 Internet Search MCP
Real-time web search capabilities for research and data gathering.

## Combined Features

- **🎨 AI-Powered UI Generation**: Create SwiftUI components with natural language
- **🔊 Voice Integration**: Generate speech for tutorials and accessibility
- **🗄️ Database Operations**: Query and manage your Supabase data directly
- **🧠 Enhanced Reasoning**: Complex problem solving and analysis
- **🔍 Real-time Search**: Access web data and financial information
- **📱 iOS Optimized**: All tools work seamlessly with SwiftUI development

## Prerequisites

- Node.js (Latest LTS version recommended)
- Python 3.8+ (for ElevenLabs MCP)
- Cursor IDE (recommended) or other supported IDEs
- API Keys (see setup section)

## Quick Setup (Recommended)

### Automated Installation

Run the comprehensive setup script:

```bash
./scripts/setup-magic-mcp.sh
```

This script will:
1. Check all prerequisites
2. Collect your API keys
3. Install all MCP servers
4. Configure your IDE
5. Verify the installation

## Manual Setup

### Step 1: Get Your API Keys

#### 21st.dev Magic API Key
1. Visit [21st.dev Magic Console](https://21st.dev/magic/console)
2. Sign up or log in to your account
3. Generate a new API key
4. Copy and save the API key securely

#### ElevenLabs API Key (Optional)
1. Visit [ElevenLabs API Keys](https://elevenlabs.io/app/settings/api-keys)
2. Create an account if needed
3. Generate a new API key
4. Copy and save the API key

#### Brave Search API Key (Optional)
1. Visit [Brave Search API](https://api.search.brave.com/app/keys)
2. Create an account if needed
3. Generate a new API key
4. Copy and save the API key

#### Supabase Configuration (Pre-configured)
Your VibeFinance Supabase configuration is already set up:
- URL: `https://mcrbwwkltigjawnlunlh.supabase.co`
- Anon Key: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### Step 2: Install MCP Servers

#### Install 21st.dev Magic MCP
```bash
# For Cursor
npx @21st-dev/cli@latest install cursor --api-key YOUR_MAGIC_API_KEY

# For VS Code
npx @21st-dev/cli@latest install code --api-key YOUR_MAGIC_API_KEY

# For Cline
npx @21st-dev/cli@latest install cline --api-key YOUR_MAGIC_API_KEY
```

#### Install ElevenLabs MCP
```bash
pip install elevenlabs-mcp
```

#### Other MCP Servers
The Supabase, Sequential Thinking, and Internet Search MCP servers are automatically installed when first used.

### Step 3: Configure Your IDE

The MCP configuration file is already created at `.cursor/mcp.json`. When you first use the MCP servers, you'll be prompted to enter your API keys.

## Usage Examples

### 🎨 UI Generation (Magic MCP)

Create SwiftUI components with natural language:

```
/ui create a SwiftUI financial portfolio card with:
- Portfolio value with percentage change
- Color-coded gains/losses (green/red)
- Small chart preview
- Tap gesture for detailed view
- Dark mode support
```

```
/ui design a SwiftUI quest completion animation with:
- Confetti effect
- XP points counter animation
- Achievement badge reveal
- Smooth fade transitions
```

```
/ui build a SwiftUI investment simulator interface with:
- Stock search bar with autocomplete
- Buy/sell buttons with confirmation
- Real-time price display
- Portfolio balance
- Transaction history list
```

### 🔊 Voice Generation (ElevenLabs MCP)

Generate voice content for your app:

```
Generate a welcome voice message for new VibeFinance users:
"Welcome to VibeFinance! Let's start building your financial future together. 
Complete your first quest to earn 100 XP points!"
```

```
Create audio feedback for successful investment:
"Congratulations! Your investment in Apple stock has been executed successfully."
```

### 🗄️ Database Operations (Supabase MCP)

Query and manage your database directly:

```
Query user portfolio data:
SELECT portfolios.*, users.username 
FROM portfolios 
JOIN users ON portfolios.user_id = users.id 
WHERE users.email = '<EMAIL>'
```

```
Get quest completion statistics:
SELECT quest_id, COUNT(*) as completions 
FROM quest_completions 
GROUP BY quest_id 
ORDER BY completions DESC
```

### 🧠 Enhanced Reasoning (Sequential Thinking MCP)

Complex problem solving and feature planning:

```
Plan the implementation of a new investment simulator feature:
1. What components do we need?
2. How should the user flow work?
3. What data structures are required?
4. How do we handle real-time price updates?
```

```
Analyze the best approach for implementing social features:
How should we design the squad investment feature to be both engaging and secure?
```

### 🔍 Internet Search (Brave Search MCP)

Research and gather information:

```
Search for current financial news:
"Latest cryptocurrency market trends 2024"
```

```
Research investment education content:
"Best practices for teaching financial literacy to Gen Z"
```

## Integration with VibeFinance

### Authentication Views
- Enhance login/signup forms with modern designs
- Add smooth transitions and animations
- Improve accessibility with voice feedback

### Financial Dashboard Components
- Create beautiful chart components
- Design modern card layouts for financial data
- Build interactive portfolio views

### Quest System
- Design engaging quest cards
- Create completion animations
- Add voice feedback for achievements

### Investment Simulator
- Build realistic trading interfaces
- Create educational tutorials with voice
- Design portfolio management tools

## Best Practices

### 1. Be Specific with Prompts
```
❌ "Create a button"
✅ "Create a SwiftUI button with rounded corners, gradient background, and haptic feedback"
```

### 2. Mention Platform and Framework
```
❌ "Design a card component"
✅ "Design a SwiftUI card component for iOS with dark mode support"
```

### 3. Include Styling Details
```
❌ "Make a chart"
✅ "Create a SwiftUI line chart with green/red colors, smooth animations, and accessibility labels"
```

### 4. Consider User Experience
```
❌ "Add a form"
✅ "Create a SwiftUI form with validation, error states, and smooth keyboard handling"
```

## Troubleshooting

### Common Issues

#### MCP Servers Not Working
1. Restart your IDE after installation
2. Check that API keys are correctly entered
3. Verify internet connection
4. Check IDE console for error messages

#### ElevenLabs MCP Issues
1. Ensure Python 3.8+ is installed
2. Verify `uvx` is available in PATH
3. Check ElevenLabs API key is valid
4. Ensure sufficient API credits

#### Supabase MCP Issues
1. Verify Supabase URL and key are correct
2. Check database permissions
3. Ensure network access to Supabase

### Getting Help

- **Discord Community**: [Join here](https://discord.gg/Qx4rFunHfm)
- **GitHub Issues**: Report bugs and request features
- **Documentation**: Check individual MCP server docs

## Example Prompts for VibeFinance

### Financial Components
```
/ui create a SwiftUI stock price ticker with:
- Real-time price updates
- Percentage change indicator
- Color-coded gains/losses
- Smooth number animations
```

### Quest System
```
/ui design a SwiftUI quest progress view with:
- Circular progress indicator
- XP points display
- Completion checkmarks
- Reward preview
```

### Social Features
```
/ui build a SwiftUI squad member card with:
- Profile picture
- Username and stats
- Investment performance
- Follow/unfollow button
```

### Educational Content
```
Generate educational voice content:
"Let's learn about compound interest. When you invest money, you earn returns not just on your initial investment, but also on the returns you've already earned..."
```

## Next Steps

1. **Run Setup Script**: `./scripts/setup-magic-mcp.sh`
2. **Get API Keys**: Collect keys from the services you want to use
3. **Test Installation**: Try a simple UI generation command
4. **Explore Features**: Experiment with different MCP capabilities
5. **Build Amazing Features**: Use MCP servers to enhance VibeFinance

## Configuration Files

- **Cursor**: `.cursor/mcp.json`
- **VS Code**: `.vscode/mcp.json` or user settings
- **Cline**: `~/.cline/mcp_config.json`

Happy coding with supercharged AI tools! 🚀
