# VibeFinance: Onboarding to Investment Tracking Flow

## 📊 **Complete Database Schema Overview**

### **Core User Data Flow:**
```
Onboarding Data → User Profile → Investment Portfolios → Trading History
```

## 🗄️ **Database Tables & Relationships**

### **1. User Profile & Onboarding Data**
```sql
users (Primary User Table)
├── id (UUID) - Primary Key
├── email, username - Authentication
├── preferences (JSONB) - Onboarding data storage
│   ├── interests[] - Investment interests from onboarding
│   ├── goals[] - Financial goals (retirement, house, etc.)
│   ├── riskTolerance - Conservative/Moderate/Aggressive
│   ├── preferredLanguage - Localization
│   └── notificationsEnabled - Communication preferences
├── xp, level - Gamification progress
└── subscription_status - Free/Pro tier
```

### **2. Investment Portfolio System**
```sql
portfolios (User Investment Accounts)
├── id (UUID) - Primary Key
├── user_id → users.id (Foreign Key)
├── type - 'virtual' or 'real'
├── balance - Available cash
├── total_value - Portfolio worth
├── total_invested - Amount invested
└── broker_account_id - External account link

positions (Individual Stock Holdings)
├── id (UUID) - Primary Key
├── portfolio_id → portfolios.id (Foreign Key)
├── symbol, company_name - Stock identification
├── quantity, average_cost - Position details
├── current_price, market_value - Real-time values
└── unrealized_pl - Profit/Loss tracking

transactions (Trading History)
├── id (UUID) - Primary Key
├── portfolio_id → portfolios.id (Foreign Key)
├── symbol, type (buy/sell/dividend)
├── quantity, price, total_amount
├── fees, order_id - Transaction details
└── status - pending/completed/cancelled
```

### **3. Social Investment Features**
```sql
squads (Investment Groups)
├── id (UUID) - Primary Key
├── creator_id → users.id (Foreign Key)
├── name, description - Squad details
├── investment_focus[] - Focus areas
├── risk_level - Group risk tolerance
└── total_value - Combined portfolio value

squad_members (Group Participation)
├── squad_id → squads.id (Foreign Key)
├── user_id → users.id (Foreign Key)
├── role - creator/admin/member
├── contribution - Investment amount
└── joined_at - Membership date

squad_investments (Group Holdings)
├── squad_id → squads.id (Foreign Key)
├── symbol, company_name - Stock details
├── total_shares, average_cost
├── proposed_by → users.id (Foreign Key)
└── status - proposed/voting/active/sold
```

### **4. Gamification & Learning**
```sql
achievements (Available Badges)
├── id (UUID) - Primary Key
├── title, description, icon
├── category - learning/investing/social/milestones
├── rarity - common/rare/epic/legendary
├── requirement_type - level/xp/investmentsMade/etc.
└── requirement_target - Achievement threshold

user_achievements (User Progress)
├── user_id → users.id (Foreign Key)
├── achievement_id → achievements.id (Foreign Key)
├── unlocked_at - Achievement date
├── progress - Current progress
└── is_completed - Achievement status

quests (Learning Challenges)
├── id (UUID) - Primary Key
├── title, description, category
├── difficulty, xp_reward
├── estimated_time - Minutes to complete
└── tasks (JSONB) - Quest steps

user_quest_progress (Learning Progress)
├── user_id → users.id (Foreign Key)
├── quest_id → quests.id (Foreign Key)
├── status - active/completed/abandoned
├── progress (JSONB) - Step completion
└── completed_at - Completion date
```

### **5. AI & Personalization**
```sql
investment_recommendations (AI Suggestions)
├── user_id → users.id (Foreign Key)
├── symbol, company_name - Stock details
├── recommendation_type - buy/sell/hold/watch
├── confidence_score - AI confidence (0-1)
├── expected_return - Projected return %
├── risk_level - low/medium/high
├── reasoning - AI explanation
└── source - ai_advisor/analyst/news

watchlists (User Stock Tracking)
├── user_id → users.id (Foreign Key)
├── symbol, company_name - Stock details
├── target_price - Price alert threshold
├── notes - User notes
└── added_from - onboarding/recommendation/manual/squad

chat_messages (AI Advisor History)
├── user_id → users.id (Foreign Key)
├── content - Message text
├── type - text/image/chart
├── sender - user/ai_advisor
└── metadata (JSONB) - Context data
```

## 🔄 **How Onboarding Data Links to Investments**

### **Step 1: Onboarding Data Collection**
```javascript
// GenZ Onboarding Profile Data
{
  personalityType: "adventurous",
  investmentStyle: "growth_focused", 
  financialGoals: ["retirement", "house_down_payment"],
  riskTolerance: "moderate",
  socialPreferences: {
    profileVisibility: "public",
    competitiveMode: true,
    squadParticipation: true
  },
  learningStyle: "visual",
  communicationStyle: "casual",
  interests: ["tech_stocks", "crypto", "sustainable_investing"]
}
```

### **Step 2: Data Transformation & Storage**
```sql
-- Onboarding data is mapped to user preferences
UPDATE users SET preferences = {
  "interests": ["stock_AAPL", "stock_TSLA", "feed_crypto", "feed_sustainable"],
  "goals": ["retirement", "house_down_payment", "profile_public"],
  "riskTolerance": "moderate",
  "preferredLanguage": "en",
  "notificationsEnabled": true,
  "dailyQuestTime": "06:00"
} WHERE id = user_id;
```

### **Step 3: Automatic Portfolio Creation**
```sql
-- Trigger creates default virtual portfolio
INSERT INTO portfolios (user_id, name, type, balance, total_value)
VALUES (user_id, 'Virtual Portfolio', 'virtual', 10000.00, 10000.00);
```

### **Step 4: Personalized Recommendations**
```sql
-- AI generates recommendations based on onboarding data
INSERT INTO investment_recommendations (user_id, symbol, recommendation_type, reasoning)
VALUES 
  (user_id, 'AAPL', 'buy', 'Matches tech stock interest from onboarding'),
  (user_id, 'TSLA', 'watch', 'Aligns with growth-focused investment style'),
  (user_id, 'ESG', 'buy', 'Sustainable investing preference detected');
```

### **Step 5: Watchlist Population**
```sql
-- Add recommended stocks to user watchlist
INSERT INTO watchlists (user_id, symbol, company_name, added_from)
VALUES 
  (user_id, 'AAPL', 'Apple Inc.', 'onboarding'),
  (user_id, 'TSLA', 'Tesla Inc.', 'onboarding');
```

### **Step 6: Squad Matching**
```sql
-- Find squads matching user preferences
SELECT s.* FROM squads s 
WHERE s.risk_level = 'moderate' 
  AND s.investment_focus && ARRAY['tech_stocks', 'growth']
  AND s.is_public = true
ORDER BY s.performance_percent DESC;
```

## 📈 **Investment Tracking Lifecycle**

### **1. Virtual Trading (Starter Experience)**
- User starts with $10,000 virtual money
- Makes practice investments to learn
- Tracks performance and gains XP
- Unlocks achievements for milestones

### **2. Real Trading (Pro Tier)**
- Links real brokerage account (Alpaca)
- Transfers virtual strategies to real money
- Real-time portfolio synchronization
- Advanced analytics and insights

### **3. Social Investing (Squad Features)**
- Joins investment groups based on interests
- Participates in group investment decisions
- Shares strategies and learns from others
- Competes in challenges and leaderboards

### **4. Continuous Learning (Quest System)**
- Personalized learning paths based on goals
- Interactive quests teach investment concepts
- Progress tracking and skill development
- Achievement system motivates engagement

## 🔐 **Data Security & Privacy**

### **Row Level Security (RLS)**
- Users can only access their own data
- Squad data visible to members only
- Public achievements and quests for all
- Secure API endpoints with authentication

### **Data Relationships**
- Foreign key constraints ensure data integrity
- Cascade deletes maintain consistency
- Real-time updates for live data sync
- Automatic triggers for calculated fields

## 🚀 **Implementation Status**

### ✅ **Completed:**
- Complete database schema design
- User onboarding data collection
- Portfolio and position tracking
- Social squad features
- Gamification system
- AI recommendation engine

### 🔄 **Next Steps:**
1. Run `investment_tracking_schema.sql` in Supabase
2. Update SupabaseService.swift with new table methods
3. Implement portfolio sync with real brokers
4. Add real-time price updates
5. Deploy AI recommendation algorithms

This comprehensive system ensures that every piece of onboarding data directly influences the user's investment journey, creating a personalized and engaging financial experience! 🎯
