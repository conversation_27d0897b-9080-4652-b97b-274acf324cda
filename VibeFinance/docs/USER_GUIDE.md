# VibeFinance - User Guide

## 🚀 Getting Started

Welcome to VibeFinance! This guide will help you navigate and make the most of all the features available in the app.

### **First Launch**
1. **Onboarding**: Complete the welcome flow to set up your profile
2. **Development Mode**: In debug builds, the app auto-logs you in for easy testing
3. **Explore**: Navigate through the tabs to discover all features

---

## 📱 Tab Navigation Guide

### 🏠 **Feed Tab - Your Financial Dashboard**

**What You'll See**:
- Personalized financial content and market insights
- Real-time stock prices and market updates
- Educational articles and investment opportunities
- Interactive feed items you can engage with

**How to Use**:
1. **Scroll** through your personalized feed
2. **Pull down** to refresh content
3. **Tap the filter icon** to filter by category (Stocks, Crypto, News, Education)
4. **Like** posts by tapping the fire icon
5. **Bookmark** interesting content for later
6. **Tap "Invest"** on recommendations to start investing

**Pro Tips**:
- The AI learns from your interactions to show better content
- Bookmarked items are saved in the "Bookmarked" filter
- Feed content updates throughout the day with market changes

---

### 🎯 **Quests Tab - Learn & Earn**

**What You'll See**:
- 3 daily quests with different difficulty levels
- Weekly challenges to compete with other users
- Your XP progress and current level
- Achievement badges you've earned

**How to Use**:
1. **Tap on a quest** to start it
2. **Complete tasks** like analyzing stocks or answering questions
3. **Earn XP** for each completed task
4. **Level up** as you gain more experience
5. **Check achievements** to see what badges you can unlock

**Quest Types**:
- **📊 Research**: Analyze stocks and make predictions
- **❓ Multiple Choice**: Test your financial knowledge
- **🎮 Simulation**: Practice trading scenarios
- **📺 Video**: Watch educational content
- **📖 Reading**: Study financial concepts

**Pro Tips**:
- Complete all daily quests for bonus XP
- Higher difficulty quests give more XP
- Streaks multiply your XP rewards

---

### 👥 **Squads Tab - Invest Together** (Pro Feature)

**What You'll See**:
- Investment squads you can join
- Your current squad memberships
- Squad performance statistics
- AI-recommended squads based on your interests

**How to Use**:
1. **Browse squads** by scrolling through the list
2. **Tap "Join"** to join a public squad
3. **Create your own squad** using the + button
4. **Chat with squad members** in real-time
5. **Vote on investment proposals** from squad members
6. **Track squad performance** in the analytics section

**Squad Features**:
- **Public Squads**: Open to everyone
- **Private Squads**: Invitation only
- **Themed Squads**: Focus on specific sectors (Tech, Green Energy, etc.)
- **Collaborative Voting**: Make investment decisions together

**Pro Tips**:
- Join squads that match your investment interests
- Participate actively in discussions for better results
- Create proposals for investments you believe in

---

### 📈 **Trading Tab - Investment Platform**

#### **For Free Users: Investment Simulator**

**What You'll See**:
- Virtual portfolio with $100,000 starting money
- Real-time stock prices and market data
- Your virtual holdings and performance
- Leaderboard showing top performers

**How to Use**:
1. **Search for stocks** using the search icon
2. **Tap on a stock** to see details and charts
3. **Buy stocks** with your virtual money
4. **Monitor your portfolio** in the Portfolio tab
5. **Sell stocks** when you want to take profits or cut losses
6. **Check the leaderboard** to see how you rank

**Simulator Features**:
- **Portfolio Tracking**: See all your virtual investments
- **Performance Analytics**: Track gains, losses, and returns
- **Market Data**: Real-time prices from actual markets
- **Learning Mode**: Safe environment to practice trading

#### **For Pro Users: Real Trading**

**What You'll See**:
- Live trading interface connected to real markets
- Your actual investment portfolio
- Real money buying power
- Professional trading tools and analytics

**How to Use**:
1. **Connect your brokerage account** (Alpaca Markets)
2. **Fund your account** with real money
3. **Place real trades** with actual market impact
4. **Monitor real positions** and performance
5. **Use advanced tools** for analysis and risk management

**Pro Tips**:
- Start with the simulator to learn before using real money
- Set stop-losses to manage risk
- Diversify your portfolio across different sectors
- Use the AI advisor for investment guidance

---

### 📊 **Analytics Tab - Performance Insights** (Pro Feature)

**What You'll See**:
- Detailed portfolio performance charts
- Risk analysis and metrics
- Asset allocation breakdowns
- Benchmark comparisons

**How to Use**:
1. **View performance charts** to see how your investments are doing
2. **Check risk metrics** to understand your portfolio risk
3. **Analyze asset allocation** to see if you're diversified
4. **Compare to benchmarks** like the S&P 500
5. **Generate reports** for tax purposes or record keeping

**Key Metrics Explained**:
- **Total Return**: How much your portfolio has gained/lost
- **Sharpe Ratio**: Risk-adjusted return measure
- **Beta**: How much your portfolio moves with the market
- **Maximum Drawdown**: Largest peak-to-trough decline

---

### 💬 **Vibe Chat Tab - AI Financial Advisor**

**What You'll See**:
- Chat interface with AI financial advisor
- Quick action buttons for common questions
- Chat history of your previous conversations
- Typing indicator when AI is responding

**How to Use**:
1. **Type your question** in the text field at the bottom
2. **Use quick action buttons** for common queries:
   - Market Analysis
   - Investment Ideas
   - Portfolio Review
   - AI Insights
3. **Ask follow-up questions** for more detailed advice
4. **Review chat history** to reference previous conversations

**What You Can Ask**:
- "What stocks should I invest in?"
- "How is my portfolio performing?"
- "What's happening in the market today?"
- "Should I buy or sell [stock name]?"
- "How can I reduce my portfolio risk?"
- "What are some good long-term investments?"

**Pro Tips**:
- Be specific in your questions for better answers
- Mention your risk tolerance and investment goals
- Ask for explanations if you don't understand something
- The AI knows your portfolio and can give personalized advice

---

### 🧪 **Testing Tab - Development Tools** (Debug Only)

**What You'll See**:
- API testing interface
- Performance monitoring tools
- Development settings and configurations
- Build information and diagnostics

**How to Use** (For Developers):
1. **Test APIs** to ensure all integrations are working
2. **Monitor performance** to identify bottlenecks
3. **Toggle settings** for different testing scenarios
4. **View diagnostics** for troubleshooting

---

### 👤 **Profile Tab - Your Account**

**What You'll See**:
- Your profile information and avatar
- Current subscription tier and status
- User statistics (level, XP, days active)
- Settings and account management options

**How to Use**:
1. **View your stats** to see your progress
2. **Tap subscription** to upgrade or manage your plan
3. **Access settings** for app preferences
4. **Sign out** when you're done using the app

**Subscription Tiers**:
- **Free**: Basic features, simulator access, limited quests
- **Basic**: Enhanced features, some real trading, more quests
- **Pro**: Full access, unlimited trading, all analytics, squads

---

## 🎮 Gamification Features

### **XP and Leveling System**
- **Earn XP** by completing quests and achieving milestones
- **Level up** to unlock new features and higher-tier quests
- **Track progress** in your profile and quest tabs

### **Achievement System**
- **Unlock badges** for various accomplishments
- **Share achievements** with your squads
- **Collect rare badges** for special milestones

### **Daily Streaks**
- **Complete daily quests** to maintain streaks
- **Earn bonus XP** for consecutive days
- **Unlock special rewards** for long streaks

---

## 💡 Pro Tips for Success

### **Getting Started**
1. Complete the onboarding to set up your preferences
2. Start with daily quests to learn the basics
3. Use the simulator before investing real money
4. Chat with the AI advisor to understand concepts

### **Building Wealth**
1. Diversify your portfolio across different sectors
2. Start with index funds for stable growth
3. Use dollar-cost averaging for regular investments
4. Don't panic during market downturns

### **Using Social Features**
1. Join squads that match your investment style
2. Learn from experienced investors in your squads
3. Share your knowledge to help others
4. Participate in squad discussions and voting

### **Maximizing Learning**
1. Complete all daily quests for maximum XP
2. Read educational content in the feed
3. Ask the AI advisor to explain complex concepts
4. Practice with the simulator before real trading

---

## 🔒 Security & Privacy

### **Account Security**
- Use a strong, unique password
- Enable two-factor authentication when available
- Log out when using shared devices
- Monitor your account for unusual activity

### **Financial Security**
- Only connect verified brokerage accounts
- Review all trades before confirming
- Set appropriate risk limits
- Keep your financial information private

---

## 🆘 Getting Help

### **In-App Support**
- Use the AI chat for immediate help
- Check the testing tab for diagnostic information
- Review your transaction history in analytics

### **Common Issues**
- **App not loading**: Check your internet connection
- **Login problems**: Try the mock authentication in development
- **Missing features**: Ensure you have the right subscription tier
- **Performance issues**: Check the performance monitor in testing tab

---

## 🎯 Next Steps

1. **Explore each tab** to understand all available features
2. **Complete your first quest** to start earning XP
3. **Try the investment simulator** to practice trading
4. **Chat with the AI advisor** to get personalized advice
5. **Consider upgrading to Pro** for advanced features
6. **Join a squad** to connect with other investors

Welcome to your financial journey with VibeFinance! 🚀
