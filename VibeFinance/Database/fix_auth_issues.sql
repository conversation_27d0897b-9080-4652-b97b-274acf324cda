-- WealthVibe Database Fix Script
-- Run this in your Supabase SQL Editor to fix authentication issues

-- 1. Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own data" ON users;
DROP POLICY IF EXISTS "Users can update own data" ON users;
DROP POLICY IF EXISTS "Users can insert own data" ON users;

-- 2. Drop and recreate the users table with correct structure
DROP TABLE IF EXISTS users CASCADE;

-- 3. Create Users table with proper structure
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    preferences JSONB DEFAULT '{"interests": [], "goals": [], "riskTolerance": "moderate", "preferredLanguage": "en", "notificationsEnabled": true, "dailyQuestTime": "06:00"}',
    subscription_status TEXT DEFAULT 'free',
    xp INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 5. Create proper RLS policies
-- Allow users to see their own data
CREATE POLICY "Users can view own data" ON users 
    FOR SELECT USING (auth.uid() = id);

-- Allow users to update their own data
CREATE POLICY "Users can update own data" ON users 
    FOR UPDATE USING (auth.uid() = id);

-- Allow users to insert their own data (critical for sign-up)
CREATE POLICY "Users can insert own data" ON users 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 6. Create function to update user level based on XP
CREATE OR REPLACE FUNCTION update_user_level()
RETURNS TRIGGER AS $$
BEGIN
    NEW.level = CASE
        WHEN NEW.xp >= 10000 THEN 10
        WHEN NEW.xp >= 5000 THEN 9
        WHEN NEW.xp >= 2500 THEN 8
        WHEN NEW.xp >= 1250 THEN 7
        WHEN NEW.xp >= 625 THEN 6
        WHEN NEW.xp >= 300 THEN 5
        WHEN NEW.xp >= 150 THEN 4
        WHEN NEW.xp >= 75 THEN 3
        WHEN NEW.xp >= 25 THEN 2
        ELSE 1
    END;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Create trigger to automatically update level when XP changes
DROP TRIGGER IF EXISTS update_user_level_trigger ON users;
CREATE TRIGGER update_user_level_trigger
    BEFORE UPDATE OF xp ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_user_level();

-- 8. Create a test function to verify the setup
CREATE OR REPLACE FUNCTION test_user_creation(test_user_id UUID, test_email TEXT, test_username TEXT)
RETURNS TEXT AS $$
DECLARE
    result TEXT;
BEGIN
    -- Try to insert a test user
    INSERT INTO users (id, email, username) 
    VALUES (test_user_id, test_email, test_username);
    
    result := 'SUCCESS: User creation test passed';
    
    -- Clean up test data
    DELETE FROM users WHERE id = test_user_id;
    
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- 9. Success message
SELECT 'WealthVibe authentication fix completed! 🚀' as status;
SELECT 'You can now test user creation and authentication.' as next_step;

-- 10. Test the setup (optional - uncomment to run)
-- SELECT test_user_creation(
--     'f47ac10b-58cc-4372-a567-0e02b2c3d479'::UUID,
--     '<EMAIL>',
--     'testuser'
-- ) as test_result;
