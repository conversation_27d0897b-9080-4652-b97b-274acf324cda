-- VibeFinance Investment Tracking Schema
-- Complete database schema for linking onboarding data to user investments
-- Run this in your Supabase SQL Editor AFTER setup.sql

-- 1. Create Portfolios table (Virtual & Real)
CREATE TABLE IF NOT EXISTS portfolios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name TEXT NOT NULL DEFAULT 'My Portfolio',
    type TEXT NOT NULL CHECK (type IN ('virtual', 'real')),
    balance DECIMAL(15,2) DEFAULT 0.00, -- Available cash
    total_value DECIMAL(15,2) DEFAULT 0.00, -- Total portfolio value
    total_invested DECIMAL(15,2) DEFAULT 0.00, -- Total amount invested
    day_change DECIMAL(15,2) DEFAULT 0.00,
    day_change_percent DECIMAL(5,2) DEFAULT 0.00,
    broker_account_id TEXT, -- For real portfolios (Alpaca, etc.)
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create Positions table (Individual stock holdings)
CREATE TABLE IF NOT EXISTS positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    company_name TEXT NOT NULL,
    quantity DECIMAL(15,6) NOT NULL,
    average_cost DECIMAL(10,4) NOT NULL,
    current_price DECIMAL(10,4) DEFAULT 0.00,
    market_value DECIMAL(15,2) DEFAULT 0.00,
    unrealized_pl DECIMAL(15,2) DEFAULT 0.00,
    unrealized_pl_percent DECIMAL(5,2) DEFAULT 0.00,
    sector TEXT,
    industry TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(portfolio_id, symbol)
);

-- 3. Create Transactions table (Buy/sell history)
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    company_name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('buy', 'sell', 'dividend')),
    quantity DECIMAL(15,6) NOT NULL,
    price DECIMAL(10,4) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    fees DECIMAL(10,2) DEFAULT 0.00,
    order_id TEXT, -- External broker order ID
    status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled', 'failed')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create Squads table (Investment groups)
CREATE TABLE IF NOT EXISTS squads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    creator_id UUID REFERENCES users(id) ON DELETE CASCADE,
    avatar_url TEXT,
    is_public BOOLEAN DEFAULT true,
    max_members INTEGER DEFAULT 50,
    total_value DECIMAL(15,2) DEFAULT 0.00,
    member_count INTEGER DEFAULT 1,
    performance_percent DECIMAL(5,2) DEFAULT 0.00,
    investment_focus TEXT[], -- Array of focus areas
    risk_level TEXT DEFAULT 'moderate' CHECK (risk_level IN ('conservative', 'moderate', 'aggressive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create Squad Members table
CREATE TABLE IF NOT EXISTS squad_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    squad_id UUID REFERENCES squads(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'member' CHECK (role IN ('creator', 'admin', 'member')),
    contribution DECIMAL(15,2) DEFAULT 0.00,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    UNIQUE(squad_id, user_id)
);

-- 6. Create Squad Investments table
CREATE TABLE IF NOT EXISTS squad_investments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    squad_id UUID REFERENCES squads(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    company_name TEXT NOT NULL,
    total_shares DECIMAL(15,6) DEFAULT 0.00,
    average_cost DECIMAL(10,4) DEFAULT 0.00,
    current_value DECIMAL(15,2) DEFAULT 0.00,
    proposed_by UUID REFERENCES users(id),
    status TEXT DEFAULT 'active' CHECK (status IN ('proposed', 'voting', 'active', 'sold')),
    votes_for INTEGER DEFAULT 0,
    votes_against INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create Achievements table
CREATE TABLE IF NOT EXISTS achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('learning', 'investing', 'social', 'milestones')),
    rarity TEXT NOT NULL CHECK (rarity IN ('common', 'rare', 'epic', 'legendary')),
    xp_reward INTEGER DEFAULT 0,
    requirement_type TEXT NOT NULL CHECK (requirement_type IN ('level', 'xp', 'investmentsMade', 'daysActive', 'questsCompleted', 'squadsJoined')),
    requirement_target INTEGER NOT NULL,
    requirement_description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create User Achievements table
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    progress INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT false,
    UNIQUE(user_id, achievement_id)
);

-- 9. Create Investment Recommendations table
CREATE TABLE IF NOT EXISTS investment_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    company_name TEXT NOT NULL,
    recommendation_type TEXT NOT NULL CHECK (recommendation_type IN ('buy', 'sell', 'hold', 'watch')),
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    expected_return DECIMAL(5,2),
    risk_level TEXT CHECK (risk_level IN ('low', 'medium', 'high')),
    reasoning TEXT,
    source TEXT DEFAULT 'ai_advisor',
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. Create Watchlists table
CREATE TABLE IF NOT EXISTS watchlists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    company_name TEXT NOT NULL,
    target_price DECIMAL(10,4),
    notes TEXT,
    added_from TEXT, -- 'onboarding', 'recommendation', 'manual', 'squad'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, symbol)
);

-- 11. Enable Row Level Security on all tables
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE positions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE squads ENABLE ROW LEVEL SECURITY;
ALTER TABLE squad_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE squad_investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE investment_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE watchlists ENABLE ROW LEVEL SECURITY;

-- 12. Create RLS Policies for Portfolios
CREATE POLICY "Users can view own portfolios" ON portfolios FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own portfolios" ON portfolios FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own portfolios" ON portfolios FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own portfolios" ON portfolios FOR DELETE USING (auth.uid() = user_id);

-- 13. Create RLS Policies for Positions
CREATE POLICY "Users can view own positions" ON positions FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM portfolios WHERE id = portfolio_id)
);
CREATE POLICY "Users can insert own positions" ON positions FOR INSERT WITH CHECK (
    auth.uid() IN (SELECT user_id FROM portfolios WHERE id = portfolio_id)
);
CREATE POLICY "Users can update own positions" ON positions FOR UPDATE USING (
    auth.uid() IN (SELECT user_id FROM portfolios WHERE id = portfolio_id)
);
CREATE POLICY "Users can delete own positions" ON positions FOR DELETE USING (
    auth.uid() IN (SELECT user_id FROM portfolios WHERE id = portfolio_id)
);

-- 14. Create RLS Policies for Transactions
CREATE POLICY "Users can view own transactions" ON transactions FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM portfolios WHERE id = portfolio_id)
);
CREATE POLICY "Users can insert own transactions" ON transactions FOR INSERT WITH CHECK (
    auth.uid() IN (SELECT user_id FROM portfolios WHERE id = portfolio_id)
);

-- 15. Create RLS Policies for Squads
CREATE POLICY "Anyone can view public squads" ON squads FOR SELECT USING (is_public = true OR creator_id = auth.uid());
CREATE POLICY "Users can create squads" ON squads FOR INSERT WITH CHECK (auth.uid() = creator_id);
CREATE POLICY "Squad creators can update squads" ON squads FOR UPDATE USING (auth.uid() = creator_id);
CREATE POLICY "Squad creators can delete squads" ON squads FOR DELETE USING (auth.uid() = creator_id);

-- 16. Create RLS Policies for Squad Members
CREATE POLICY "Squad members can view squad membership" ON squad_members FOR SELECT USING (
    auth.uid() = user_id OR
    auth.uid() IN (SELECT creator_id FROM squads WHERE id = squad_id)
);
CREATE POLICY "Users can join squads" ON squad_members FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can leave squads" ON squad_members FOR DELETE USING (auth.uid() = user_id);

-- 17. Create RLS Policies for Squad Investments
CREATE POLICY "Squad members can view squad investments" ON squad_investments FOR SELECT USING (
    auth.uid() IN (SELECT user_id FROM squad_members WHERE squad_id = squad_investments.squad_id AND is_active = true)
);
CREATE POLICY "Squad members can propose investments" ON squad_investments FOR INSERT WITH CHECK (
    auth.uid() IN (SELECT user_id FROM squad_members WHERE squad_id = squad_investments.squad_id AND is_active = true)
);

-- 18. Create RLS Policies for Achievements
CREATE POLICY "Anyone can view achievements" ON achievements FOR SELECT TO authenticated;

-- 19. Create RLS Policies for User Achievements
CREATE POLICY "Users can view own achievements" ON user_achievements FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can unlock achievements" ON user_achievements FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update achievement progress" ON user_achievements FOR UPDATE USING (auth.uid() = user_id);

-- 20. Create RLS Policies for Investment Recommendations
CREATE POLICY "Users can view own recommendations" ON investment_recommendations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can create recommendations" ON investment_recommendations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update recommendation status" ON investment_recommendations FOR UPDATE USING (auth.uid() = user_id);

-- 21. Create RLS Policies for Watchlists
CREATE POLICY "Users can view own watchlist" ON watchlists FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can add to watchlist" ON watchlists FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update watchlist" ON watchlists FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can remove from watchlist" ON watchlists FOR DELETE USING (auth.uid() = user_id);

-- 22. Create Functions for Portfolio Updates
CREATE OR REPLACE FUNCTION update_portfolio_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Update portfolio total value when positions change
    UPDATE portfolios
    SET
        total_value = (
            SELECT COALESCE(SUM(market_value), 0) + balance
            FROM positions
            WHERE portfolio_id = NEW.portfolio_id
        ),
        updated_at = NOW()
    WHERE id = NEW.portfolio_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 23. Create Function to Update Squad Member Count
CREATE OR REPLACE FUNCTION update_squad_member_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE squads
        SET member_count = (
            SELECT COUNT(*)
            FROM squad_members
            WHERE squad_id = NEW.squad_id AND is_active = true
        )
        WHERE id = NEW.squad_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE squads
        SET member_count = (
            SELECT COUNT(*)
            FROM squad_members
            WHERE squad_id = OLD.squad_id AND is_active = true
        )
        WHERE id = OLD.squad_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 24. Create Function to Auto-Create Default Portfolio
CREATE OR REPLACE FUNCTION create_default_portfolio()
RETURNS TRIGGER AS $$
BEGIN
    -- Create a default virtual portfolio for new users
    INSERT INTO portfolios (user_id, name, type, balance, total_value)
    VALUES (NEW.id, 'Virtual Portfolio', 'virtual', 10000.00, 10000.00);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 25. Create Triggers
CREATE TRIGGER update_portfolio_on_position_change
    AFTER INSERT OR UPDATE OR DELETE ON positions
    FOR EACH ROW
    EXECUTE FUNCTION update_portfolio_totals();

CREATE TRIGGER update_squad_member_count_trigger
    AFTER INSERT OR DELETE ON squad_members
    FOR EACH ROW
    EXECUTE FUNCTION update_squad_member_count();

CREATE TRIGGER create_default_portfolio_trigger
    AFTER INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_portfolio();

-- 26. Enable Real-time for Investment Tables
ALTER PUBLICATION supabase_realtime ADD TABLE portfolios;
ALTER PUBLICATION supabase_realtime ADD TABLE positions;
ALTER PUBLICATION supabase_realtime ADD TABLE transactions;
ALTER PUBLICATION supabase_realtime ADD TABLE squad_investments;
ALTER PUBLICATION supabase_realtime ADD TABLE user_achievements;

-- 27. Insert Default Achievements
INSERT INTO achievements (title, description, icon, category, rarity, xp_reward, requirement_type, requirement_target, requirement_description) VALUES
('Welcome Aboard! 🎉', 'Completed your first onboarding journey', 'party.popper', 'milestones', 'common', 50, 'level', 1, 'Complete onboarding'),
('First Investment 💰', 'Made your first investment decision', 'dollarsign.circle', 'investing', 'rare', 100, 'investmentsMade', 1, 'Make your first investment'),
('Streak Master 🔥', 'Maintained a 7-day learning streak', 'flame', 'learning', 'epic', 200, 'daysActive', 7, 'Stay active for 7 days'),
('Squad Leader 👥', 'Created your first investment squad', 'person.3', 'social', 'rare', 150, 'squadsJoined', 1, 'Create or join a squad'),
('Quest Conqueror 🏆', 'Completed 10 learning quests', 'trophy', 'learning', 'epic', 300, 'questsCompleted', 10, 'Complete 10 quests'),
('Portfolio Builder 📊', 'Built a diversified portfolio with 5+ stocks', 'chart.line.uptrend.xyaxis', 'investing', 'epic', 250, 'investmentsMade', 5, 'Invest in 5 different stocks'),
('Level Up Legend ⭐', 'Reached level 5', 'star.fill', 'milestones', 'legendary', 500, 'level', 5, 'Reach level 5'),
('Social Butterfly 🦋', 'Joined 3 different investment squads', 'person.2.circle', 'social', 'rare', 200, 'squadsJoined', 3, 'Join 3 squads');

-- Success message
SELECT 'VibeFinance Investment Tracking Schema created successfully! 🚀' as status;
