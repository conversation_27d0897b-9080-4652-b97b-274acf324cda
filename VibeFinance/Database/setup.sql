-- WealthVibe Database Setup Script
-- Run this in your Supabase SQL Editor

-- 1. Create Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    preferences JSONB DEFAULT '{"interests": [], "goals": [], "riskTolerance": "moderate", "preferredLanguage": "en", "notificationsEnabled": true, "dailyQuestTime": "06:00"}',
    subscription_status TEXT DEFAULT 'free',
    xp INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create Feed items table
CREATE TABLE IF NOT EXISTS feed_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    content JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create Quests table
CREATE TABLE IF NOT EXISTS quests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    difficulty TEXT NOT NULL,
    xp_reward INTEGER NOT NULL,
    estimated_time INTEGER NOT NULL,
    tasks JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create User quest progress table
CREATE TABLE IF NOT EXISTS user_quest_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    quest_id UUID REFERENCES quests(id),
    status TEXT DEFAULT 'active',
    progress JSONB DEFAULT '{}',
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create Chat messages table
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    type TEXT DEFAULT 'text',
    sender TEXT DEFAULT 'user',
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE feed_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_quest_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS Policies
-- Users can only see their own data
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own data" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- Feed items are user-specific
CREATE POLICY "Users can view own feed" ON feed_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own feed" ON feed_items FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Quests are public but progress is private
CREATE POLICY "Anyone can view quests" ON quests FOR SELECT TO authenticated;
CREATE POLICY "Users can view own progress" ON user_quest_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own progress" ON user_quest_progress FOR ALL USING (auth.uid() = user_id);

-- Chat messages are user-specific
CREATE POLICY "Users can view own messages" ON chat_messages FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own messages" ON chat_messages FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 8. Create function to update user level based on XP
CREATE OR REPLACE FUNCTION update_user_level()
RETURNS TRIGGER AS $$
BEGIN
    NEW.level = CASE
        WHEN NEW.xp >= 10000 THEN 10
        WHEN NEW.xp >= 5000 THEN 9
        WHEN NEW.xp >= 2500 THEN 8
        WHEN NEW.xp >= 1250 THEN 7
        WHEN NEW.xp >= 625 THEN 6
        WHEN NEW.xp >= 300 THEN 5
        WHEN NEW.xp >= 150 THEN 4
        WHEN NEW.xp >= 75 THEN 3
        WHEN NEW.xp >= 25 THEN 2
        ELSE 1
    END;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. Create trigger to automatically update level when XP changes
CREATE TRIGGER update_user_level_trigger
    BEFORE UPDATE OF xp ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_user_level();

-- 10. Enable real-time for relevant tables
ALTER PUBLICATION supabase_realtime ADD TABLE feed_items;
ALTER PUBLICATION supabase_realtime ADD TABLE chat_messages;
ALTER PUBLICATION supabase_realtime ADD TABLE user_quest_progress;

-- 11. Insert some sample quests
INSERT INTO quests (title, description, category, difficulty, xp_reward, estimated_time, tasks) VALUES
('Welcome to WealthVibe! 🎉', 'Complete your first financial learning quest', 'basics', 'beginner', 25, 5,
 '[{"title": "Read about investing", "description": "Learn the basics", "type": "reading", "xpReward": 10, "order": 1},
   {"title": "Take a quiz", "description": "Test your knowledge", "type": "quiz", "xpReward": 15, "order": 2}]'),

('Stock Market Basics 📈', 'Learn how the stock market works', 'stocks', 'beginner', 50, 10,
 '[{"title": "What are stocks?", "description": "Understanding stock ownership", "type": "reading", "xpReward": 20, "order": 1},
   {"title": "Stock quiz", "description": "Test your stock knowledge", "type": "quiz", "xpReward": 30, "order": 2}]'),

('Crypto Fundamentals ₿', 'Understand cryptocurrency basics', 'crypto', 'intermediate', 75, 15,
 '[{"title": "What is Bitcoin?", "description": "Learn about cryptocurrency", "type": "reading", "xpReward": 25, "order": 1},
   {"title": "Crypto safety", "description": "Learn about wallet security", "type": "reading", "xpReward": 25, "order": 2},
   {"title": "Crypto quiz", "description": "Test your crypto knowledge", "type": "quiz", "xpReward": 25, "order": 3}]');

-- Success message
SELECT 'WealthVibe database setup completed successfully! 🚀' as status;
