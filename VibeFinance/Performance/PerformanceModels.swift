//
//  PerformanceModels.swift
//  VibeFinance - Performance Optimization Data Models
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI

// MARK: - Performance Enums

enum PerformanceProfile: String, CaseIterable {
    case battery = "battery"
    case performance = "performance"
    case balanced = "balanced"
    case adaptive = "adaptive"
    
    var displayName: String {
        switch self {
        case .battery: return "Battery Saver"
        case .performance: return "High Performance"
        case .balanced: return "Balanced"
        case .adaptive: return "Adaptive"
        }
    }
    
    var description: String {
        switch self {
        case .battery: return "Optimizes for maximum battery life"
        case .performance: return "Optimizes for maximum performance"
        case .balanced: return "Balances performance and battery life"
        case .adaptive: return "Automatically adjusts based on conditions"
        }
    }
    
    var iconName: String {
        switch self {
        case .battery: return "battery.100"
        case .performance: return "bolt.fill"
        case .balanced: return "scale.3d"
        case .adaptive: return "brain.head.profile"
        }
    }
    
    var color: Color {
        switch self {
        case .battery: return .green
        case .performance: return .red
        case .balanced: return .blue
        case .adaptive: return .purple
        }
    }
}

enum OptimizationLevel: String, CaseIterable {
    case minimal = "minimal"
    case standard = "standard"
    case aggressive = "aggressive"
    case adaptive = "adaptive"
    
    var displayName: String {
        switch self {
        case .minimal: return "Minimal"
        case .standard: return "Standard"
        case .aggressive: return "Aggressive"
        case .adaptive: return "Adaptive"
        }
    }
    
    var intensity: Double {
        switch self {
        case .minimal: return 0.2
        case .standard: return 0.5
        case .aggressive: return 0.9
        case .adaptive: return 0.7
        }
    }
}

enum NetworkQuality: String, CaseIterable {
    case poor = "poor"
    case fair = "fair"
    case good = "good"
    
    var displayName: String {
        switch self {
        case .poor: return "Poor"
        case .fair: return "Fair"
        case .good: return "Good"
        }
    }
    
    var color: Color {
        switch self {
        case .poor: return .red
        case .fair: return .orange
        case .good: return .green
        }
    }
}

// MARK: - Performance Data Models

struct SystemMetrics {
    let memoryUsage: Double
    let cpuUsage: Double
    let batteryLevel: Double
    let thermalState: ProcessInfo.ThermalState
    let networkQuality: NetworkQuality
    let frameRate: Double
    let timestamp: Date
    
    init(memoryUsage: Double, cpuUsage: Double, batteryLevel: Double, thermalState: ProcessInfo.ThermalState, networkQuality: NetworkQuality, frameRate: Double, timestamp: Date = Date()) {
        self.memoryUsage = memoryUsage
        self.cpuUsage = cpuUsage
        self.batteryLevel = batteryLevel
        self.thermalState = thermalState
        self.networkQuality = networkQuality
        self.frameRate = frameRate
        self.timestamp = timestamp
    }
    
    var overallHealth: Double {
        let memoryScore = 1.0 - memoryUsage
        let cpuScore = 1.0 - cpuUsage
        let batteryScore = batteryLevel
        let thermalScore = thermalStateScore
        let networkScore = networkQualityScore
        let frameRateScore = min(1.0, frameRate / 60.0)
        
        return (memoryScore + cpuScore + batteryScore + thermalScore + networkScore + frameRateScore) / 6.0
    }
    
    private var thermalStateScore: Double {
        switch thermalState {
        case .nominal: return 1.0
        case .fair: return 0.8
        case .serious: return 0.4
        case .critical: return 0.1
        @unknown default: return 0.5
        }
    }
    
    private var networkQualityScore: Double {
        switch networkQuality {
        case .good: return 1.0
        case .fair: return 0.6
        case .poor: return 0.3
        }
    }
}

struct OptimizationDecision {
    var memoryOptimization: OptimizationLevel = .standard
    var cpuOptimization: OptimizationLevel = .standard
    var batteryOptimization: OptimizationLevel = .standard
    var thermalOptimization: OptimizationLevel = .standard
    var networkOptimization: OptimizationLevel = .standard
    
    var overallLevel: OptimizationLevel {
        let levels = [memoryOptimization, cpuOptimization, batteryOptimization, thermalOptimization, networkOptimization]
        let averageIntensity = levels.map { $0.intensity }.reduce(0, +) / Double(levels.count)
        
        switch averageIntensity {
        case 0.0..<0.3: return .minimal
        case 0.3..<0.7: return .standard
        case 0.7..<1.0: return .aggressive
        default: return .adaptive
        }
    }
}

struct PerformanceReport {
    let frameRate: Double
    let memoryEfficiency: Double
    let batteryEfficiency: Double
    let thermalEfficiency: Double
    let optimizationLevel: OptimizationLevel
    let profile: PerformanceProfile
    let timestamp: Date
    
    init(frameRate: Double, memoryEfficiency: Double, batteryEfficiency: Double, thermalEfficiency: Double, optimizationLevel: OptimizationLevel, profile: PerformanceProfile, timestamp: Date = Date()) {
        self.frameRate = frameRate
        self.memoryEfficiency = memoryEfficiency
        self.batteryEfficiency = batteryEfficiency
        self.thermalEfficiency = thermalEfficiency
        self.optimizationLevel = optimizationLevel
        self.profile = profile
        self.timestamp = timestamp
    }
    
    var overallScore: Double {
        let frameRateScore = min(1.0, frameRate / 60.0)
        return (frameRateScore + memoryEfficiency + batteryEfficiency + thermalEfficiency) / 4.0
    }
    
    var grade: PerformanceGrade {
        switch overallScore {
        case 0.9...1.0: return .excellent
        case 0.8..<0.9: return .good
        case 0.7..<0.8: return .fair
        case 0.6..<0.7: return .poor
        default: return .critical
        }
    }
}

enum PerformanceGrade: String, CaseIterable {
    case excellent = "excellent"
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .fair: return "Fair"
        case .poor: return "Poor"
        case .critical: return "Critical"
        }
    }
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        case .critical: return .purple
        }
    }
    
    var iconName: String {
        switch self {
        case .excellent: return "star.fill"
        case .good: return "checkmark.circle.fill"
        case .fair: return "exclamationmark.circle.fill"
        case .poor: return "xmark.circle.fill"
        case .critical: return "exclamationmark.triangle.fill"
        }
    }
}

// MARK: - Optimization Configuration

struct OptimizationConfiguration {
    var enableMemoryOptimization: Bool = true
    var enableCPUOptimization: Bool = true
    var enableBatteryOptimization: Bool = true
    var enableThermalOptimization: Bool = true
    var enableNetworkOptimization: Bool = true
    var enableAdaptiveOptimization: Bool = true
    
    var memoryThreshold: Double = 0.8
    var cpuThreshold: Double = 0.8
    var batteryThreshold: Double = 0.2
    var thermalThreshold: ProcessInfo.ThermalState = .serious
    
    var optimizationInterval: TimeInterval = 10.0
    var aggressiveOptimizationDuration: TimeInterval = 300.0 // 5 minutes
    
    static let `default` = OptimizationConfiguration()
    
    static let conservative = OptimizationConfiguration(
        memoryThreshold: 0.6,
        cpuThreshold: 0.6,
        batteryThreshold: 0.3,
        optimizationInterval: 5.0
    )
    
    static let aggressive = OptimizationConfiguration(
        memoryThreshold: 0.9,
        cpuThreshold: 0.9,
        batteryThreshold: 0.1,
        optimizationInterval: 15.0
    )
}

// MARK: - Performance Metrics History

struct PerformanceMetricsHistory {
    private var metrics: [SystemMetrics] = []
    private let maxHistorySize = 1000
    
    mutating func addMetrics(_ newMetrics: SystemMetrics) {
        metrics.append(newMetrics)
        
        // Keep only the most recent metrics
        if metrics.count > maxHistorySize {
            metrics.removeFirst(metrics.count - maxHistorySize)
        }
    }
    
    func getMetrics(for timeRange: TimeInterval) -> [SystemMetrics] {
        let cutoffTime = Date().addingTimeInterval(-timeRange)
        return metrics.filter { $0.timestamp >= cutoffTime }
    }
    
    func getAverageMetrics(for timeRange: TimeInterval) -> SystemMetrics? {
        let recentMetrics = getMetrics(for: timeRange)
        guard !recentMetrics.isEmpty else { return nil }
        
        let avgMemory = recentMetrics.map { $0.memoryUsage }.reduce(0, +) / Double(recentMetrics.count)
        let avgCPU = recentMetrics.map { $0.cpuUsage }.reduce(0, +) / Double(recentMetrics.count)
        let avgBattery = recentMetrics.map { $0.batteryLevel }.reduce(0, +) / Double(recentMetrics.count)
        let avgFrameRate = recentMetrics.map { $0.frameRate }.reduce(0, +) / Double(recentMetrics.count)
        
        // Use the most recent thermal state and network quality
        let latestMetrics = recentMetrics.last!
        
        return SystemMetrics(
            memoryUsage: avgMemory,
            cpuUsage: avgCPU,
            batteryLevel: avgBattery,
            thermalState: latestMetrics.thermalState,
            networkQuality: latestMetrics.networkQuality,
            frameRate: avgFrameRate
        )
    }
    
    mutating func clear() {
        metrics.removeAll()
    }
    
    var count: Int {
        return metrics.count
    }
    
    var isEmpty: Bool {
        return metrics.isEmpty
    }
}

// MARK: - Performance Alerts

struct PerformanceAlert {
    let id: UUID
    let type: AlertType
    let severity: AlertSeverity
    let message: String
    let timestamp: Date
    let metrics: SystemMetrics?
    
    init(type: AlertType, severity: AlertSeverity, message: String, metrics: SystemMetrics? = nil) {
        self.id = UUID()
        self.type = type
        self.severity = severity
        self.message = message
        self.timestamp = Date()
        self.metrics = metrics
    }
    
    enum AlertType: String, CaseIterable {
        case memory = "memory"
        case cpu = "cpu"
        case battery = "battery"
        case thermal = "thermal"
        case network = "network"
        case frameRate = "frameRate"
        
        var displayName: String {
            switch self {
            case .memory: return "Memory"
            case .cpu: return "CPU"
            case .battery: return "Battery"
            case .thermal: return "Thermal"
            case .network: return "Network"
            case .frameRate: return "Frame Rate"
            }
        }
        
        var iconName: String {
            switch self {
            case .memory: return "memorychip"
            case .cpu: return "cpu"
            case .battery: return "battery.25"
            case .thermal: return "thermometer"
            case .network: return "wifi.exclamationmark"
            case .frameRate: return "speedometer"
            }
        }
    }
    
    enum AlertSeverity: String, CaseIterable {
        case info = "info"
        case warning = "warning"
        case critical = "critical"
        
        var color: Color {
            switch self {
            case .info: return .blue
            case .warning: return .orange
            case .critical: return .red
            }
        }
        
        var iconName: String {
            switch self {
            case .info: return "info.circle"
            case .warning: return "exclamationmark.triangle"
            case .critical: return "exclamationmark.octagon"
            }
        }
    }
}

// MARK: - Performance Benchmarks

struct PerformanceBenchmark {
    let name: String
    let category: BenchmarkCategory
    let targetValue: Double
    let currentValue: Double
    let unit: String
    let isHigherBetter: Bool
    
    var performance: Double {
        if isHigherBetter {
            return min(1.0, currentValue / targetValue)
        } else {
            return min(1.0, targetValue / currentValue)
        }
    }
    
    var grade: PerformanceGrade {
        switch performance {
        case 0.9...1.0: return .excellent
        case 0.8..<0.9: return .good
        case 0.7..<0.8: return .fair
        case 0.6..<0.7: return .poor
        default: return .critical
        }
    }
    
    enum BenchmarkCategory: String, CaseIterable {
        case rendering = "rendering"
        case memory = "memory"
        case network = "network"
        case battery = "battery"
        case thermal = "thermal"
        
        var displayName: String {
            switch self {
            case .rendering: return "Rendering"
            case .memory: return "Memory"
            case .network: return "Network"
            case .battery: return "Battery"
            case .thermal: return "Thermal"
            }
        }
    }
    
    static let defaultBenchmarks: [PerformanceBenchmark] = [
        PerformanceBenchmark(name: "Frame Rate", category: .rendering, targetValue: 60.0, currentValue: 60.0, unit: "fps", isHigherBetter: true),
        PerformanceBenchmark(name: "Memory Usage", category: .memory, targetValue: 0.7, currentValue: 0.5, unit: "%", isHigherBetter: false),
        PerformanceBenchmark(name: "Network Latency", category: .network, targetValue: 100.0, currentValue: 50.0, unit: "ms", isHigherBetter: false),
        PerformanceBenchmark(name: "Battery Efficiency", category: .battery, targetValue: 0.95, currentValue: 0.90, unit: "%", isHigherBetter: true),
        PerformanceBenchmark(name: "Thermal Efficiency", category: .thermal, targetValue: 0.9, currentValue: 0.95, unit: "%", isHigherBetter: true)
    ]
}
