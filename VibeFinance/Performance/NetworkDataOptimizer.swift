//
//  NetworkDataOptimizer.swift
//  VibeFinance - Network and Data Processing Optimizers
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import Network
import Combine

// MARK: - Network Performance Optimizer

class NetworkPerformanceOptimizer: ObservableObject {
    @Published var networkQuality: NetworkQuality = .good
    @Published var connectionType: ConnectionType = .wifi
    @Published var optimizationLevel: OptimizationLevel = .standard
    @Published var isAdaptiveCompressionEnabled = true
    
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    private var cancellables = Set<AnyCancellable>()
    
    // Network optimization settings
    private var requestTimeout: TimeInterval = 30.0
    private var maxConcurrentRequests: Int = 6
    private var compressionLevel: Double = 0.8
    private var cachePolicy: URLRequest.CachePolicy = .useProtocolCachePolicy
    private var retryAttempts: Int = 3
    
    // Request throttling
    private var requestThrottler = RequestThrottler()
    private var bandwidthMonitor = BandwidthMonitor()
    
    init() {
        setupNetworkMonitoring()
        configureNetworkOptimizations()
    }
    
    deinit {
        networkMonitor.cancel()
    }
    
    // MARK: - Setup
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path: path)
            }
        }
        
        networkMonitor.start(queue: networkQueue)
        
        // Monitor bandwidth changes
        bandwidthMonitor.onBandwidthChange = { [weak self] bandwidth in
            DispatchQueue.main.async {
                self?.adaptToNetworkConditions(bandwidth: bandwidth)
            }
        }
    }
    
    private func updateNetworkStatus(path: NWPath) {
        // Determine connection type
        if path.usesInterfaceType(.wifi) {
            connectionType = .wifi
        } else if path.usesInterfaceType(.cellular) {
            connectionType = .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            connectionType = .ethernet
        } else {
            connectionType = .unknown
        }
        
        // Assess network quality
        if path.status == .satisfied {
            if path.isExpensive {
                networkQuality = .fair
            } else {
                networkQuality = .good
            }
        } else {
            networkQuality = .poor
        }
        
        // Apply optimizations based on network conditions
        applyNetworkOptimizations()
        
        ProductionConfig.log("📶 Network status updated: \(connectionType.displayName) - \(networkQuality.displayName)", category: "NETWORK", level: .info)
    }
    
    private func configureNetworkOptimizations() {
        // Configure based on device and network capabilities
        switch connectionType {
        case .wifi, .ethernet:
            configureHighBandwidthSettings()
        case .cellular:
            configureCellularSettings()
        case .unknown:
            configureConservativeSettings()
        }
    }
    
    // MARK: - Network Optimization Modes
    
    func enablePerformanceMode() {
        optimizationLevel = .minimal
        
        requestTimeout = 60.0
        maxConcurrentRequests = 10
        compressionLevel = 0.5
        cachePolicy = .reloadIgnoringLocalCacheData
        retryAttempts = 5
        
        applyNetworkOptimizations()
        
        ProductionConfig.log("⚡ Network performance mode enabled", category: "NETWORK", level: .info)
    }
    
    func enableBalancedMode() {
        optimizationLevel = .standard
        
        requestTimeout = 30.0
        maxConcurrentRequests = 6
        compressionLevel = 0.8
        cachePolicy = .useProtocolCachePolicy
        retryAttempts = 3
        
        applyNetworkOptimizations()
        
        ProductionConfig.log("⚖️ Network balanced mode enabled", category: "NETWORK", level: .info)
    }
    
    func enableBatterySavingMode() {
        optimizationLevel = .aggressive
        
        requestTimeout = 15.0
        maxConcurrentRequests = 3
        compressionLevel = 0.9
        cachePolicy = .returnCacheDataElseLoad
        retryAttempts = 1
        
        applyNetworkOptimizations()
        
        ProductionConfig.log("🔋 Network battery saving mode enabled", category: "NETWORK", level: .info)
    }
    
    func enableAdaptiveMode() {
        optimizationLevel = .adaptive
        
        // Adapt based on current network conditions
        adaptToNetworkConditions()
        
        ProductionConfig.log("🤖 Network adaptive mode enabled", category: "NETWORK", level: .info)
    }
    
    // MARK: - Adaptive Optimization
    
    private func adaptToNetworkConditions(bandwidth: Double? = nil) {
        switch networkQuality {
        case .good:
            if connectionType == .wifi || connectionType == .ethernet {
                configureHighBandwidthSettings()
            } else {
                configureBalancedSettings()
            }
        case .fair:
            configureBalancedSettings()
        case .poor:
            configureLowBandwidthSettings()
        }
        
        // Adjust based on bandwidth if available
        if let bandwidth = bandwidth {
            adjustForBandwidth(bandwidth)
        }
    }
    
    private func configureHighBandwidthSettings() {
        requestTimeout = 45.0
        maxConcurrentRequests = 8
        compressionLevel = 0.6
        retryAttempts = 4
    }
    
    private func configureBalancedSettings() {
        requestTimeout = 30.0
        maxConcurrentRequests = 6
        compressionLevel = 0.8
        retryAttempts = 3
    }
    
    private func configureLowBandwidthSettings() {
        requestTimeout = 20.0
        maxConcurrentRequests = 3
        compressionLevel = 0.9
        retryAttempts = 2
    }
    
    private func configureCellularSettings() {
        // Optimize for cellular data usage
        requestTimeout = 25.0
        maxConcurrentRequests = 4
        compressionLevel = 0.85
        cachePolicy = .returnCacheDataElseLoad
        retryAttempts = 2
    }
    
    private func configureConservativeSettings() {
        requestTimeout = 15.0
        maxConcurrentRequests = 2
        compressionLevel = 0.9
        cachePolicy = .returnCacheDataElseLoad
        retryAttempts = 1
    }
    
    private func adjustForBandwidth(_ bandwidth: Double) {
        // Adjust settings based on measured bandwidth
        if bandwidth > 10.0 { // High bandwidth (>10 Mbps)
            maxConcurrentRequests = min(maxConcurrentRequests + 2, 10)
            compressionLevel = max(compressionLevel - 0.1, 0.5)
        } else if bandwidth < 1.0 { // Low bandwidth (<1 Mbps)
            maxConcurrentRequests = max(maxConcurrentRequests - 1, 2)
            compressionLevel = min(compressionLevel + 0.1, 0.95)
        }
    }
    
    // MARK: - Optimization Application
    
    func applyOptimization(level: OptimizationLevel) {
        optimizationLevel = level
        
        switch level {
        case .minimal:
            enablePerformanceMode()
        case .standard:
            enableBalancedMode()
        case .aggressive:
            enableBatterySavingMode()
        case .adaptive:
            enableAdaptiveMode()
        }
    }
    
    private func applyNetworkOptimizations() {
        // Configure request throttler
        requestThrottler.configure(
            maxConcurrentRequests: maxConcurrentRequests,
            timeout: requestTimeout,
            retryAttempts: retryAttempts
        )
        
        // Notify other components of network optimization changes
        NotificationCenter.default.post(
            name: .networkOptimizationChanged,
            object: nil,
            userInfo: [
                "timeout": requestTimeout,
                "maxConcurrent": maxConcurrentRequests,
                "compression": compressionLevel,
                "cachePolicy": cachePolicy.rawValue,
                "retryAttempts": retryAttempts
            ]
        )
    }
    
    // MARK: - Public Interface
    
    func getOptimizedURLRequest(for url: URL) -> URLRequest {
        var request = URLRequest(url: url)
        request.timeoutInterval = requestTimeout
        request.cachePolicy = cachePolicy
        
        // Add compression headers if enabled
        if isAdaptiveCompressionEnabled {
            request.setValue("gzip, deflate", forHTTPHeaderField: "Accept-Encoding")
        }
        
        return request
    }
    
    func shouldThrottleRequest() -> Bool {
        return requestThrottler.shouldThrottle()
    }
    
    func getNetworkEfficiency() -> Double {
        switch networkQuality {
        case .good: return 0.9
        case .fair: return 0.7
        case .poor: return 0.4
        }
    }
}

// MARK: - Data Processing Optimizer

class DataProcessingOptimizer: ObservableObject {
    @Published var processingMode: ProcessingMode = .balanced
    @Published var optimizationLevel: OptimizationLevel = .standard
    @Published var isParallelProcessingEnabled = true
    @Published var isDataCompressionEnabled = true
    
    // Processing settings
    private var maxConcurrentOperations: Int = 4
    private var chunkSize: Int = 1000
    private var compressionRatio: Double = 0.8
    private var cacheSize: Int = 100
    
    // Processing queues
    private let highPriorityQueue = OperationQueue()
    private let normalPriorityQueue = OperationQueue()
    private let lowPriorityQueue = OperationQueue()
    
    // Data cache
    private let dataCache = NSCache<NSString, NSData>()
    
    init() {
        setupProcessingQueues()
        configureDataCache()
    }
    
    // MARK: - Setup
    
    private func setupProcessingQueues() {
        highPriorityQueue.name = "HighPriorityDataProcessing"
        highPriorityQueue.maxConcurrentOperationCount = 2
        highPriorityQueue.qualityOfService = .userInteractive
        
        normalPriorityQueue.name = "NormalPriorityDataProcessing"
        normalPriorityQueue.maxConcurrentOperationCount = maxConcurrentOperations
        normalPriorityQueue.qualityOfService = .userInitiated
        
        lowPriorityQueue.name = "LowPriorityDataProcessing"
        lowPriorityQueue.maxConcurrentOperationCount = 1
        lowPriorityQueue.qualityOfService = .utility
    }
    
    private func configureDataCache() {
        dataCache.countLimit = cacheSize
        dataCache.totalCostLimit = 50 * 1024 * 1024 // 50MB
    }
    
    // MARK: - Processing Modes
    
    func enableHighPerformanceMode() {
        processingMode = .performance
        optimizationLevel = .minimal
        
        maxConcurrentOperations = 8
        chunkSize = 2000
        compressionRatio = 0.6
        cacheSize = 200
        
        applyOptimizations()
        
        ProductionConfig.log("⚡ High performance data processing enabled", category: "DATA", level: .info)
    }
    
    func enableBalancedMode() {
        processingMode = .balanced
        optimizationLevel = .standard
        
        maxConcurrentOperations = 4
        chunkSize = 1000
        compressionRatio = 0.8
        cacheSize = 100
        
        applyOptimizations()
        
        ProductionConfig.log("⚖️ Balanced data processing enabled", category: "DATA", level: .info)
    }
    
    func enableLowPowerMode() {
        processingMode = .efficiency
        optimizationLevel = .aggressive
        
        maxConcurrentOperations = 2
        chunkSize = 500
        compressionRatio = 0.9
        cacheSize = 50
        
        applyOptimizations()
        
        ProductionConfig.log("🔋 Low power data processing enabled", category: "DATA", level: .info)
    }
    
    func enableAdaptiveMode() {
        processingMode = .adaptive
        optimizationLevel = .adaptive
        
        // Adapt based on system conditions
        adaptToSystemConditions()
        
        ProductionConfig.log("🤖 Adaptive data processing enabled", category: "DATA", level: .info)
    }
    
    // MARK: - Adaptive Processing
    
    private func adaptToSystemConditions() {
        let systemMetrics = getCurrentSystemMetrics()
        
        if systemMetrics.memoryUsage > 0.8 {
            // High memory usage - reduce processing
            maxConcurrentOperations = 2
            chunkSize = 500
            cacheSize = 25
        } else if systemMetrics.cpuUsage > 0.8 {
            // High CPU usage - reduce concurrent operations
            maxConcurrentOperations = 2
            chunkSize = 1000
        } else if systemMetrics.batteryLevel < 0.2 {
            // Low battery - optimize for efficiency
            enableLowPowerMode()
            return
        } else {
            // Normal conditions - use balanced settings
            enableBalancedMode()
            return
        }
        
        applyOptimizations()
    }
    
    // MARK: - Optimization Application
    
    func applyOptimization(level: OptimizationLevel) {
        optimizationLevel = level
        
        switch level {
        case .minimal:
            enableHighPerformanceMode()
        case .standard:
            enableBalancedMode()
        case .aggressive:
            enableLowPowerMode()
        case .adaptive:
            enableAdaptiveMode()
        }
    }
    
    private func applyOptimizations() {
        // Update processing queues
        normalPriorityQueue.maxConcurrentOperationCount = maxConcurrentOperations
        
        // Update cache settings
        dataCache.countLimit = cacheSize
        
        // Notify other components
        NotificationCenter.default.post(
            name: .dataProcessingOptimizationChanged,
            object: nil,
            userInfo: [
                "maxConcurrentOperations": maxConcurrentOperations,
                "chunkSize": chunkSize,
                "compressionRatio": compressionRatio,
                "cacheSize": cacheSize
            ]
        )
    }
    
    // MARK: - Public Interface
    
    func processData<T>(_ data: [T], priority: ProcessingPriority = .normal, completion: @escaping ([T]) -> Void) {
        let queue = getQueue(for: priority)
        
        queue.addOperation {
            let processedData = self.performDataProcessing(data)
            DispatchQueue.main.async {
                completion(processedData)
            }
        }
    }
    
    private func getQueue(for priority: ProcessingPriority) -> OperationQueue {
        switch priority {
        case .high:
            return highPriorityQueue
        case .normal:
            return normalPriorityQueue
        case .low:
            return lowPriorityQueue
        }
    }
    
    private func performDataProcessing<T>(_ data: [T]) -> [T] {
        // Chunk processing for large datasets
        if data.count > chunkSize {
            return processInChunks(data)
        } else {
            return processDirectly(data)
        }
    }
    
    private func processInChunks<T>(_ data: [T]) -> [T] {
        var result: [T] = []
        
        for chunk in data.chunked(into: chunkSize) {
            let processedChunk = processDirectly(chunk)
            result.append(contentsOf: processedChunk)
        }
        
        return result
    }
    
    private func processDirectly<T>(_ data: [T]) -> [T] {
        // Simplified data processing
        return data
    }
    
    private func getCurrentSystemMetrics() -> SystemMetrics {
        // This would get actual system metrics
        return SystemMetrics(
            memoryUsage: 0.5,
            cpuUsage: 0.3,
            batteryLevel: 0.8,
            thermalState: .nominal,
            networkQuality: .good,
            frameRate: 60.0
        )
    }
}

// MARK: - Supporting Types

enum ConnectionType: String, CaseIterable {
    case wifi = "wifi"
    case cellular = "cellular"
    case ethernet = "ethernet"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .wifi: return "Wi-Fi"
        case .cellular: return "Cellular"
        case .ethernet: return "Ethernet"
        case .unknown: return "Unknown"
        }
    }
}

enum ProcessingMode: String, CaseIterable {
    case performance = "performance"
    case balanced = "balanced"
    case efficiency = "efficiency"
    case adaptive = "adaptive"
    
    var displayName: String {
        switch self {
        case .performance: return "Performance"
        case .balanced: return "Balanced"
        case .efficiency: return "Efficiency"
        case .adaptive: return "Adaptive"
        }
    }
}

enum ProcessingPriority: String, CaseIterable {
    case high = "high"
    case normal = "normal"
    case low = "low"
}

// MARK: - Helper Classes

class RequestThrottler {
    private var currentRequests = 0
    private var maxRequests = 6
    private var timeout: TimeInterval = 30.0
    private var retryAttempts = 3
    
    func configure(maxConcurrentRequests: Int, timeout: TimeInterval, retryAttempts: Int) {
        self.maxRequests = maxConcurrentRequests
        self.timeout = timeout
        self.retryAttempts = retryAttempts
    }
    
    func shouldThrottle() -> Bool {
        return currentRequests >= maxRequests
    }
}

class BandwidthMonitor {
    var onBandwidthChange: ((Double) -> Void)?
    
    // Implementation would monitor actual bandwidth
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let networkOptimizationChanged = Notification.Name("networkOptimizationChanged")
    static let dataProcessingOptimizationChanged = Notification.Name("dataProcessingOptimizationChanged")
}

// MARK: - Array Extension

extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}
