//
//  ContextualPerformanceIndicator.swift
//  VibeFinance - Subtle Performance Status Indicator
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI

// MARK: - Contextual Performance Indicator

struct ContextualPerformanceIndicator: View {
    @StateObject private var performanceEngine = InvisiblePerformanceEngine.shared
    
    var body: some View {
        Group {
            if performanceEngine.shouldShowStatusIndicator {
                PerformanceStatusView(
                    status: performanceEngine.optimizationStatus,
                    message: performanceEngine.contextualMessage
                )
                .transition(.asymmetric(
                    insertion: .move(edge: .top).combined(with: .opacity),
                    removal: .opacity
                ))
                .animation(.easeInOut(duration: 0.3), value: performanceEngine.shouldShowStatusIndicator)
            }
        }
    }
}

// MARK: - Performance Status View

struct PerformanceStatusView: View {
    let status: OptimizationStatus
    let message: String
    
    var body: some View {
        HStack(spacing: 8) {
            // Status icon (subtle)
            if !status.iconName.isEmpty {
                Image(systemName: status.iconName)
                    .font(.caption)
                    .foregroundColor(status.color)
                    .opacity(0.8)
            }
            
            // Message
            Text(message)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .lineLimit(1)
            
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(status.color.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal)
    }
}

// MARK: - Performance Status Bar (Alternative Minimal Design)

struct PerformanceStatusBar: View {
    @StateObject private var performanceEngine = InvisiblePerformanceEngine.shared
    
    var body: some View {
        Group {
            if performanceEngine.shouldShowStatusIndicator {
                VStack(spacing: 0) {
                    // Thin status bar at top
                    Rectangle()
                        .fill(performanceEngine.optimizationStatus.color)
                        .frame(height: 2)
                        .opacity(0.8)
                    
                    // Optional message (only for critical optimizations)
                    if !performanceEngine.contextualMessage.isEmpty && shouldShowMessage {
                        HStack {
                            Text(performanceEngine.contextualMessage)
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.7))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 4)
                            
                            Spacer()
                        }
                        .background(Color.black.opacity(0.2))
                    }
                }
                .transition(.move(edge: .top))
                .animation(.easeInOut(duration: 0.3), value: performanceEngine.shouldShowStatusIndicator)
            }
        }
    }
    
    private var shouldShowMessage: Bool {
        performanceEngine.optimizationStatus == .optimizing
    }
}

// MARK: - Floating Performance Hint

struct FloatingPerformanceHint: View {
    @StateObject private var performanceEngine = InvisiblePerformanceEngine.shared
    @State private var isVisible = false
    
    var body: some View {
        Group {
            if performanceEngine.shouldShowStatusIndicator && performanceEngine.optimizationStatus == .optimized {
                HStack(spacing: 8) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                    
                    Text(performanceEngine.contextualMessage)
                        .font(.caption)
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.green.opacity(0.3), lineWidth: 1)
                        )
                )
                .padding(.horizontal)
                .offset(y: isVisible ? 0 : -50)
                .opacity(isVisible ? 1 : 0)
                .onAppear {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        isVisible = true
                    }
                    
                    // Auto-hide after 3 seconds
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        withAnimation(.easeOut(duration: 0.3)) {
                            isVisible = false
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Performance Integration Views

struct PerformanceAwareView<Content: View>: View {
    let content: Content
    @StateObject private var performanceEngine = InvisiblePerformanceEngine.shared
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Contextual performance indicator (only when needed)
            ContextualPerformanceIndicator()
            
            // Main content
            content
        }
        .onAppear {
            // Trigger optimization check when view appears
            performanceEngine.forceOptimization()
        }
    }
}

// MARK: - Warren Buffett Inspired Performance Wisdom

struct PerformanceWisdomCard: View {
    let optimizationStatus: OptimizationStatus
    
    var body: some View {
        if optimizationStatus == .optimizing {
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "quote.bubble.fill")
                        .foregroundColor(.yellow)
                        .font(.caption)
                    
                    Text("Warren's Wisdom")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                
                Text(getWisdomQuote())
                    .font(.caption2)
                    .italic()
                    .foregroundColor(Color.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.yellow.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                    )
            )
            .padding(.horizontal)
        }
    }
    
    private func getWisdomQuote() -> String {
        let quotes = [
            "Patience is a virtue in investing and in technology.",
            "The best investment is in yourself and your tools.",
            "Quality over quantity applies to both stocks and performance.",
            "Efficiency today creates wealth tomorrow."
        ]
        
        return quotes.randomElement() ?? quotes[0]
    }
}

// MARK: - Performance Settings (Hidden in App Settings)

struct PerformanceSettingsView: View {
    @StateObject private var performanceEngine = InvisiblePerformanceEngine.shared
    @State private var showAdvancedSettings = false
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Image(systemName: "gear")
                    .foregroundColor(.blue)
                    .font(.title3)
                
                Text("Performance")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // Simple toggle for users who want control
            VStack(alignment: .leading, spacing: 12) {
                Toggle("Smart Optimization", isOn: .constant(true))
                    .tint(VibeFinanceDesignSystem.Colors.accentGold)
                
                Text("Automatically optimizes performance based on your device conditions and usage patterns.")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            // Advanced settings (hidden by default)
            if showAdvancedSettings {
                Divider()
                    .background(Color.white.opacity(0.3))
                
                VStack(alignment: .leading, spacing: 12) {
                    Text("Advanced Options")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Toggle("Battery Optimization", isOn: .constant(true))
                        .tint(VibeFinanceDesignSystem.Colors.accentGold)
                    
                    Toggle("Thermal Management", isOn: .constant(true))
                        .tint(VibeFinanceDesignSystem.Colors.accentGold)
                    
                    Toggle("Memory Optimization", isOn: .constant(true))
                        .tint(VibeFinanceDesignSystem.Colors.accentGold)
                    
                    Toggle("Network Optimization", isOn: .constant(true))
                        .tint(VibeFinanceDesignSystem.Colors.accentGold)
                }
            }
            
            // Advanced settings toggle
            Button(showAdvancedSettings ? "Hide Advanced" : "Show Advanced") {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showAdvancedSettings.toggle()
                }
            }
            .font(.caption)
            .foregroundColor(.blue)
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Usage Examples

struct ExampleUsage: View {
    var body: some View {
        // Example 1: Main app view with performance awareness
        PerformanceAwareView {
            VStack {
                Text("Main App Content")
                // Your app content here
            }
        }
        
        // Example 2: Just the floating hint for specific views
        ZStack {
            // Your view content
            VStack {
                Text("Trading View")
                // Trading interface
            }
            
            // Performance hint overlay
            VStack {
                FloatingPerformanceHint()
                Spacer()
            }
        }
        
        // Example 3: Status bar integration
        VStack(spacing: 0) {
            PerformanceStatusBar()
            
            // Your main content
            ScrollView {
                // App content
            }
        }
    }
}
