//
//  ProductionConfig.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Production Configuration Manager
struct ProductionConfig {
    
    // MARK: - Environment Detection
    
    /// Determines if app is running in production mode
    static var isProduction: Bool {
        #if DEBUG
        return false
        #else
        return true
        #endif
    }
    
    /// Determines if real APIs should be used
    static var useRealAPIs: Bool {
        return isProduction || DevelopmentConfig.enableRealAPIs
    }
    
    /// Determines if real-time updates should be enabled
    static var enableRealTimeUpdates: Bool {
        return isProduction || DevelopmentConfig.enableRealTimeUpdates
    }
    
    // MARK: - API Configuration
    
    /// Get appropriate API configuration based on environment
    static func getAPIConfig() -> APIEnvironment {
        if isProduction {
            return .production
        } else {
            return .development
        }
    }
    
    // MARK: - Feature Flags
    
    /// Features that should be enabled in production
    struct Features {
        static let realTimeMarketData = useRealAPIs
        static let liveAIChat = useRealAPIs
        static let realTrading = isProduction // Only enable real trading in production
        static let pushNotifications = isProduction
        static let analytics = true
        static let crashReporting = isProduction
        static let performanceMonitoring = true
    }
    
    // MARK: - Data Sources
    
    /// Determines which data source to use for different features
    struct DataSources {
        static let stockData: StockDataSource = useRealAPIs ? .polygon : .mock
        static let aiChat: AIChatSource = useRealAPIs ? .gemini : .mock
        static let news: NewsDataSource = useRealAPIs ? .newsAPI : .mock
        static let trading: TradingSource = isProduction ? .alpacaLive : .alpacaPaper
        static let database: DatabaseSource = .supabase // Always use Supabase
    }
    
    // MARK: - Performance Settings
    
    struct Performance {
        static let cacheTimeout: TimeInterval = isProduction ? 30 : 60
        static let maxConcurrentRequests = isProduction ? 10 : 5
        static let requestTimeout: TimeInterval = isProduction ? 30 : 60
        static let enableImageCaching = true
        static let enableNetworkOptimization = isProduction
    }
    
    // MARK: - Security Settings
    
    struct Security {
        static let enableSSLPinning = isProduction
        static let enableBiometricAuth = isProduction
        static let enableDataEncryption = isProduction
        static let logSensitiveData = !isProduction
    }
    
    // MARK: - Logging Configuration
    
    static func log(_ message: String, category: String = "PRODUCTION", level: LogLevel = .info) {
        let timestamp = DateFormatter.logFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] [\(category)] [\(level.rawValue)] \(message)"
        
        if isProduction {
            // In production, only log warnings and errors
            if level == .warning || level == .error {
                print(logMessage)
                // TODO: Send to crash reporting service
            }
        } else {
            // In development, log everything
            print(logMessage)
        }
    }
}

// MARK: - Supporting Enums

enum APIEnvironment {
    case development
    case production
    
    var baseURL: String {
        switch self {
        case .development:
            return "https://dev-api.vibefi.com"
        case .production:
            return "https://api.vibefi.com"
        }
    }
}

enum StockDataSource {
    case polygon
    case mock
}

enum AIChatSource {
    case gemini
    case mock
}

enum NewsDataSource {
    case newsAPI
    case mock
}

enum TradingSource {
    case alpacaLive
    case alpacaPaper
    case mock
}

enum DatabaseSource {
    case supabase
    case mock
}

enum LogLevel: String {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
}

// MARK: - Extensions

extension DateFormatter {
    static let logFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        return formatter
    }()
}

// MARK: - Production Validation

extension ProductionConfig {
    
    /// Validates that all required production configurations are set
    static func validateProductionReadiness() -> [String] {
        var issues: [String] = []
        
        // Check API keys
        if APIConfiguration.PolygonConfig.apiKey.isEmpty {
            issues.append("Polygon API key not configured")
        }
        
        if APIConfiguration.GeminiConfig.apiKey.isEmpty {
            issues.append("Gemini AI API key not configured")
        }
        
        if APIConfiguration.AlpacaConfig.apiKey.isEmpty {
            issues.append("Alpaca API key not configured")
        }
        
        if APIConfiguration.SupabaseConfig.url.isEmpty {
            issues.append("Supabase URL not configured")
        }
        
        // Check feature flags (only warn in production)
        if isProduction && DevelopmentConfig.enableMockAuth {
            issues.append("Mock authentication should be disabled in production")
        }
        
        if isProduction && DevelopmentConfig.showDeveloperTools {
            issues.append("Developer tools should be hidden in production")
        }
        
        // Check security settings
        if isProduction && !Security.enableSSLPinning {
            issues.append("SSL pinning should be enabled in production")
        }
        
        return issues
    }
    
    /// Logs production readiness status
    static func logProductionReadiness() {
        let issues = validateProductionReadiness()
        
        if issues.isEmpty {
            log("✅ Production readiness check passed", category: "PRODUCTION", level: .info)
        } else {
            log("❌ Production readiness issues found:", category: "PRODUCTION", level: .warning)
            for issue in issues {
                log("  - \(issue)", category: "PRODUCTION", level: .warning)
            }
        }
    }
}

// MARK: - Real-time Data Configuration

extension ProductionConfig {
    
    struct RealTimeData {
        static let enableWebSockets = isProduction
        static let marketDataUpdateInterval: TimeInterval = isProduction ? 15 : 30
        static let portfolioUpdateInterval: TimeInterval = isProduction ? 30 : 60
        static let newsUpdateInterval: TimeInterval = isProduction ? 300 : 600 // 5 min vs 10 min
        static let maxSubscribedSymbols = isProduction ? 50 : 20
    }
}
