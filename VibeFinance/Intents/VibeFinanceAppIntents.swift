//
//  VibeFinanceAppIntents.swift
//  VibeFinance - App Intents for Siri and Shortcuts
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import AppIntents
import Foundation

// MARK: - App Shortcuts Provider

struct VibeFinanceShortcuts: AppShortcutsProvider {
    static var appShortcuts: [AppShortcut] {
        AppShortcut(
            intent: CheckPortfolioIntent(),
            phrases: [
                "Check my portfolio in ${applicationName}",
                "Show my investments in ${applicationName}",
                "What's my portfolio worth in ${applicationName}"
            ],
            shortTitle: "Check Portfolio",
            systemImageName: "chart.pie.fill"
        )
        
        AppShortcut(
            intent: GetAIAdviceIntent(),
            phrases: [
                "Get investment advice from ${applicationName}",
                "Ask Warren Buffett in ${applicationName}",
                "Get financial advice in ${applicationName}"
            ],
            shortTitle: "Get AI Advice",
            systemImageName: "brain.head.profile"
        )
        
        AppShortcut(
            intent: ViewQuestsIntent(),
            phrases: [
                "Show my quests in ${applicationName}",
                "Check my learning progress in ${applicationName}",
                "What quests do I have in ${applicationName}"
            ],
            shortTitle: "View Quests",
            systemImageName: "target"
        )
        
        AppShortcut(
            intent: CheckMarketIntent(),
            phrases: [
                "Check the market in ${applicationName}",
                "Show market status in ${applicationName}",
                "What's happening in the market in ${applicationName}"
            ],
            shortTitle: "Check Market",
            systemImageName: "chart.line.uptrend.xyaxis"
        )
        
        AppShortcut(
            intent: StartLearningIntent(),
            phrases: [
                "Start learning in ${applicationName}",
                "Begin a quest in ${applicationName}",
                "Learn about investing in ${applicationName}"
            ],
            shortTitle: "Start Learning",
            systemImageName: "graduationcap.fill"
        )
    }
}

// MARK: - Portfolio Intent

struct CheckPortfolioIntent: AppIntent {
    static var title: LocalizedStringResource = "Check Portfolio"
    static var description = IntentDescription("Check your investment portfolio performance and current value.")
    static var openAppWhenRun: Bool = false
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        // Get portfolio data from SimulatorManager
        let simulatorManager = await SimulatorManager()
        let portfolio = await simulatorManager.portfolio

        let totalValue = portfolio?.totalValue ?? 0.0
        let todayChange = (portfolio?.totalValue ?? 0.0) * 0.02 // Simulate 2% change
        let todayChangePercent = 2.0 // Simulate 2% change
        
        let dialog = IntentDialog(stringLiteral: """
        Your portfolio is worth $\(String(format: "%.2f", totalValue)). \
        Today's change: \(todayChange >= 0 ? "+" : "")$\(String(format: "%.2f", todayChange)) \
        (\(todayChangePercent >= 0 ? "+" : "")\(String(format: "%.2f", todayChangePercent))%)
        """)
        
        return .result(
            dialog: dialog,
            view: PortfolioSnippetView(
                totalValue: totalValue,
                todayChange: todayChange,
                todayChangePercent: todayChangePercent
            )
        )
    }
}

// MARK: - AI Advice Intent

struct GetAIAdviceIntent: AppIntent {
    static var title: LocalizedStringResource = "Get AI Investment Advice"
    static var description = IntentDescription("Get personalized investment advice from AI financial advisors.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Question", description: "What would you like to ask about investing?")
    var question: String?
    
    @Parameter(title: "Advisor", description: "Which AI advisor would you like to consult?")
    var advisor: AIAdvisorEntity?
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let selectedAdvisor = advisor?.name ?? "Warren Buffett"
        let userQuestion = question ?? "What's a good investment strategy for beginners?"
        
        // Simulate AI response (in production, this would call the actual AI service)
        let advice = generateAdvice(for: userQuestion, from: selectedAdvisor)
        
        let dialog = IntentDialog(stringLiteral: "\(selectedAdvisor) says: \(advice)")
        
        return .result(dialog: dialog)
    }
    
    private func generateAdvice(for question: String, from advisor: String) -> String {
        switch advisor {
        case "Warren Buffett":
            return "Focus on companies with strong fundamentals and hold for the long term. Remember, time in the market beats timing the market."
        case "Ray Dalio":
            return "Diversification is key. Don't put all your eggs in one basket. Consider a balanced portfolio across different asset classes."
        case "Peter Lynch":
            return "Invest in what you know. Look for companies with strong growth potential in industries you understand."
        default:
            return "Start with low-cost index funds and gradually learn about individual stock picking as you gain experience."
        }
    }
}

// MARK: - Quests Intent

struct ViewQuestsIntent: AppIntent {
    static var title: LocalizedStringResource = "View Learning Quests"
    static var description = IntentDescription("Check your current learning quests and progress.")
    static var openAppWhenRun: Bool = false
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        // Get quest data from QuestManager
        let _ = await QuestManager()
        // Simulate quest data since QuestManager doesn't have quests property
        let activeQuests = 3
        let completedCount = 5
        let totalXP = 250
        
        let questSummary = activeQuests == 0 ?
            "You have no active quests. Start learning to unlock new challenges!" :
            "You have \(activeQuests) active quest\(activeQuests == 1 ? "" : "s")."
        
        let dialog = IntentDialog(stringLiteral: """
        \(questSummary) You've completed \(completedCount) quests and earned \(totalXP) XP total.
        """)
        
        return .result(
            dialog: dialog,
            view: QuestSnippetView(
                activeQuests: activeQuests,
                completedQuests: completedCount,
                totalXP: totalXP
            )
        )
    }
}

// MARK: - Market Intent

struct CheckMarketIntent: AppIntent {
    static var title: LocalizedStringResource = "Check Market Status"
    static var description = IntentDescription("Get current market status and key indices.")
    static var openAppWhenRun: Bool = false
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        // In production, this would fetch real market data
        let marketStatus = "Market is open"
        let spyPrice = "$450.25 (+0.75%)"
        let vtiPrice = "$245.80 (+0.45%)"
        
        let dialog = IntentDialog(stringLiteral: """
        \(marketStatus). SPY: \(spyPrice), VTI: \(vtiPrice). \
        Overall market sentiment is positive with moderate gains across major indices.
        """)
        
        return .result(dialog: dialog)
    }
}

// MARK: - Learning Intent

struct StartLearningIntent: AppIntent {
    static var title: LocalizedStringResource = "Start Learning"
    static var description = IntentDescription("Begin a new learning quest or continue your financial education.")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Topic", description: "What would you like to learn about?")
    var topic: LearningTopicEntity?
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let selectedTopic = topic?.name ?? "investing basics"
        
        let dialog = IntentDialog(stringLiteral: """
        Great! Let's learn about \(selectedTopic). Opening VibeFinance to start your learning journey.
        """)
        
        return .result(dialog: dialog)
    }
}

// MARK: - Entity Definitions

struct AIAdvisorEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "AI Advisor")
    static var defaultQuery = AIAdvisorQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let warrenBuffett = AIAdvisorEntity(id: "warren", name: "Warren Buffett")
    static let rayDalio = AIAdvisorEntity(id: "ray", name: "Ray Dalio")
    static let peterLynch = AIAdvisorEntity(id: "peter", name: "Peter Lynch")
    static let benjaminGraham = AIAdvisorEntity(id: "benjamin", name: "Benjamin Graham")
    static let johnBogle = AIAdvisorEntity(id: "john", name: "John Bogle")
    static let cathieWood = AIAdvisorEntity(id: "cathie", name: "Cathie Wood")
}

struct AIAdvisorQuery: EntityQuery {
    func entities(for identifiers: [AIAdvisorEntity.ID]) async throws -> [AIAdvisorEntity] {
        return allAdvisors.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [AIAdvisorEntity] {
        return allAdvisors
    }
    
    private var allAdvisors: [AIAdvisorEntity] {
        [
            .warrenBuffett, .rayDalio, .peterLynch,
            .benjaminGraham, .johnBogle, .cathieWood
        ]
    }
}

struct LearningTopicEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Learning Topic")
    static var defaultQuery = LearningTopicQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let basics = LearningTopicEntity(id: "basics", name: "investing basics")
    static let stocks = LearningTopicEntity(id: "stocks", name: "stocks")
    static let bonds = LearningTopicEntity(id: "bonds", name: "bonds")
    static let etfs = LearningTopicEntity(id: "etfs", name: "ETFs")
    static let crypto = LearningTopicEntity(id: "crypto", name: "cryptocurrency")
    static let portfolio = LearningTopicEntity(id: "portfolio", name: "portfolio management")
}

struct LearningTopicQuery: EntityQuery {
    func entities(for identifiers: [LearningTopicEntity.ID]) async throws -> [LearningTopicEntity] {
        return allTopics.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [LearningTopicEntity] {
        return allTopics
    }
    
    private var allTopics: [LearningTopicEntity] {
        [.basics, .stocks, .bonds, .etfs, .crypto, .portfolio]
    }
}
