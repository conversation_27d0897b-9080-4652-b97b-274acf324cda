//
//  ShortcutsAutomation.swift
//  VibeFinance - Advanced Shortcuts and Automation
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import AppIntents
import Foundation

// MARK: - Complex Financial Workflows

struct DailyFinancialBriefingIntent: AppIntent {
    static var title: LocalizedStringResource = "Daily Financial Briefing"
    static var description = IntentDescription("Get a comprehensive daily briefing of your portfolio, market, and learning progress.")
    static var openAppWhenRun: Bool = false
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        // Gather all financial data
        let portfolioData = await getPortfolioSummary()
        let marketData = await getMarketSummary()
        let questData = await getQuestSummary()
        let aiInsight = await getDailyAIInsight()
        
        let briefing = generateDailyBriefing(
            portfolio: portfolioData,
            market: marketData,
            quests: questData,
            insight: aiInsight
        )
        
        return .result(
            dialog: IntentDialog(stringLiteral: briefing.summary),
            view: DailyBriefingSnippetView(briefing: briefing)
        )
    }
    
    private func getPortfolioSummary() async -> PortfolioSummary {
        // In production, fetch real portfolio data
        return PortfolioSummary(
            totalValue: 112847.50,
            todayChange: 1247.50,
            todayChangePercent: 1.12,
            topPerformer: "NVDA (+3.2%)",
            worstPerformer: "META (-1.8%)"
        )
    }
    
    private func getMarketSummary() async -> MarketSummary {
        return MarketSummary(
            status: "Open",
            spyChange: 0.75,
            nasdaqChange: 1.25,
            vixLevel: 18.5,
            sentiment: "Bullish"
        )
    }
    
    private func getQuestSummary() async -> QuestSummary {
        return QuestSummary(
            activeQuests: 2,
            completedToday: 1,
            xpEarned: 150,
            nextQuest: "Understanding ETFs"
        )
    }
    
    private func getDailyAIInsight() async -> String {
        return "Market volatility is low today. Consider rebalancing your tech allocation as it's currently 35% of your portfolio."
    }
    
    private func generateDailyBriefing(
        portfolio: PortfolioSummary,
        market: MarketSummary,
        quests: QuestSummary,
        insight: String
    ) -> DailyBriefing {
        let summary = """
        Good morning! Your portfolio is up $\(String(format: "%.2f", portfolio.todayChange)) today. \
        The market is \(market.sentiment.lowercased()) with the S&P 500 up \(String(format: "%.2f", market.spyChange))%. \
        You completed \(quests.completedToday) quest today and earned \(quests.xpEarned) XP. \
        Today's insight: \(insight)
        """
        
        return DailyBriefing(
            summary: summary,
            portfolio: portfolio,
            market: market,
            quests: quests,
            insight: insight,
            timestamp: Date()
        )
    }
}

// MARK: - Portfolio Rebalancing Intent

struct PortfolioRebalancingIntent: AppIntent {
    static var title: LocalizedStringResource = "Portfolio Rebalancing Analysis"
    static var description = IntentDescription("Analyze your portfolio and get rebalancing recommendations.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Target Allocation", description: "What's your target allocation strategy?")
    var targetStrategy: AllocationStrategyEntity?
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let strategy = targetStrategy?.name ?? "balanced"
        let analysis = await analyzePortfolioBalance(strategy: strategy)
        
        let dialog = IntentDialog(stringLiteral: """
        Portfolio analysis complete. Your current allocation deviates from target by \(analysis.deviation)%. \
        \(analysis.recommendation)
        """)
        
        return .result(
            dialog: dialog,
            view: RebalancingSnippetView(analysis: analysis)
        )
    }
    
    private func analyzePortfolioBalance(strategy: String) async -> RebalancingAnalysis {
        // Mock analysis - in production, calculate real deviations
        switch strategy {
        case "aggressive":
            return RebalancingAnalysis(
                strategy: strategy,
                deviation: 8.5,
                recommendation: "Increase growth stock allocation by 5% and reduce bonds.",
                actions: [
                    "Sell $2,500 in bonds",
                    "Buy $2,500 in growth ETF",
                    "Consider adding small-cap exposure"
                ]
            )
        case "conservative":
            return RebalancingAnalysis(
                strategy: strategy,
                deviation: 12.3,
                recommendation: "Your portfolio is too aggressive. Increase bond allocation.",
                actions: [
                    "Sell $5,000 in tech stocks",
                    "Buy $5,000 in bond ETF",
                    "Add dividend-focused stocks"
                ]
            )
        default:
            return RebalancingAnalysis(
                strategy: strategy,
                deviation: 5.2,
                recommendation: "Minor rebalancing needed. Reduce tech overweight.",
                actions: [
                    "Sell $1,500 in tech stocks",
                    "Buy $1,500 in international ETF"
                ]
            )
        }
    }
}

// MARK: - Investment Research Intent

struct InvestmentResearchIntent: AppIntent {
    static var title: LocalizedStringResource = "Investment Research"
    static var description = IntentDescription("Research a stock or ETF with AI-powered analysis.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Symbol", description: "Stock or ETF symbol to research")
    var symbol: String
    
    @Parameter(title: "Research Depth", description: "How detailed should the analysis be?")
    var depth: ResearchDepthEntity?
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let ticker = symbol.uppercased()
        let analysisDepth = depth?.name ?? "standard"
        
        let research = await performInvestmentResearch(symbol: ticker, depth: analysisDepth)
        
        let dialog = IntentDialog(stringLiteral: """
        \(ticker) analysis: \(research.summary). \
        Current rating: \(research.rating). \
        Price target: $\(String(format: "%.2f", research.priceTarget))
        """)
        
        return .result(
            dialog: dialog,
            view: ResearchSnippetView(research: research)
        )
    }
    
    private func performInvestmentResearch(symbol: String, depth: String) async -> InvestmentResearch {
        // Mock research - in production, use real financial data APIs
        switch symbol {
        case "AAPL":
            return InvestmentResearch(
                symbol: symbol,
                companyName: "Apple Inc.",
                summary: "Strong fundamentals with consistent revenue growth",
                rating: "Buy",
                priceTarget: 200.0,
                keyMetrics: [
                    "P/E Ratio": "28.5",
                    "Revenue Growth": "8.2%",
                    "Profit Margin": "25.3%",
                    "Debt/Equity": "1.73"
                ],
                risks: ["Regulatory pressure", "China market exposure"],
                opportunities: ["Services growth", "AI integration"]
            )
        case "TSLA":
            return InvestmentResearch(
                symbol: symbol,
                companyName: "Tesla Inc.",
                summary: "High growth potential but elevated valuation",
                rating: "Hold",
                priceTarget: 280.0,
                keyMetrics: [
                    "P/E Ratio": "65.2",
                    "Revenue Growth": "35.1%",
                    "Profit Margin": "9.6%",
                    "Debt/Equity": "0.17"
                ],
                risks: ["Valuation concerns", "Competition increase"],
                opportunities: ["Energy storage", "Autonomous driving"]
            )
        default:
            return InvestmentResearch(
                symbol: symbol,
                companyName: "Unknown Company",
                summary: "Limited data available for analysis",
                rating: "Neutral",
                priceTarget: 100.0,
                keyMetrics: [:],
                risks: ["Data unavailable"],
                opportunities: ["Research needed"]
            )
        }
    }
}

// MARK: - Automated Learning Intent

struct AutomatedLearningIntent: AppIntent {
    static var title: LocalizedStringResource = "Start Personalized Learning"
    static var description = IntentDescription("Begin a learning session tailored to your knowledge level and interests.")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Learning Focus", description: "What would you like to focus on?")
    var focus: LearningFocusEntity?
    
    @Parameter(title: "Session Duration", description: "How long do you want to learn?")
    var duration: LearningDurationEntity?
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let learningFocus = focus?.name ?? "general investing"
        let sessionLength = duration?.minutes ?? 15
        
        let session = await createLearningSession(focus: learningFocus, duration: sessionLength)
        
        let dialog = IntentDialog(stringLiteral: """
        Perfect! I've created a \(sessionLength)-minute learning session on \(learningFocus). \
        You'll earn up to \(session.maxXP) XP. Opening VibeFinance to start your session.
        """)
        
        return .result(dialog: dialog)
    }
    
    private func createLearningSession(focus: String, duration: Int) async -> LearningSession {
        let questCount = duration / 5 // Roughly 5 minutes per quest
        let maxXP = questCount * 50
        
        return LearningSession(
            focus: focus,
            duration: duration,
            questCount: questCount,
            maxXP: maxXP,
            difficulty: "adaptive"
        )
    }
}

// MARK: - Supporting Data Structures

struct PortfolioSummary {
    let totalValue: Double
    let todayChange: Double
    let todayChangePercent: Double
    let topPerformer: String
    let worstPerformer: String
}

struct MarketSummary {
    let status: String
    let spyChange: Double
    let nasdaqChange: Double
    let vixLevel: Double
    let sentiment: String
}

struct QuestSummary {
    let activeQuests: Int
    let completedToday: Int
    let xpEarned: Int
    let nextQuest: String
}

struct DailyBriefing {
    let summary: String
    let portfolio: PortfolioSummary
    let market: MarketSummary
    let quests: QuestSummary
    let insight: String
    let timestamp: Date
}

struct RebalancingAnalysis {
    let strategy: String
    let deviation: Double
    let recommendation: String
    let actions: [String]
}

struct InvestmentResearch {
    let symbol: String
    let companyName: String
    let summary: String
    let rating: String
    let priceTarget: Double
    let keyMetrics: [String: String]
    let risks: [String]
    let opportunities: [String]
}

struct LearningSession {
    let focus: String
    let duration: Int
    let questCount: Int
    let maxXP: Int
    let difficulty: String
}

// MARK: - Additional Entity Definitions

struct AllocationStrategyEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Allocation Strategy")
    static var defaultQuery = AllocationStrategyQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let conservative = AllocationStrategyEntity(id: "conservative", name: "conservative")
    static let balanced = AllocationStrategyEntity(id: "balanced", name: "balanced")
    static let aggressive = AllocationStrategyEntity(id: "aggressive", name: "aggressive")
}

struct AllocationStrategyQuery: EntityQuery {
    func entities(for identifiers: [AllocationStrategyEntity.ID]) async throws -> [AllocationStrategyEntity] {
        return allStrategies.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [AllocationStrategyEntity] {
        return allStrategies
    }
    
    private var allStrategies: [AllocationStrategyEntity] {
        [.conservative, .balanced, .aggressive]
    }
}

struct ResearchDepthEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Research Depth")
    static var defaultQuery = ResearchDepthQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let quick = ResearchDepthEntity(id: "quick", name: "quick")
    static let standard = ResearchDepthEntity(id: "standard", name: "standard")
    static let detailed = ResearchDepthEntity(id: "detailed", name: "detailed")
}

struct ResearchDepthQuery: EntityQuery {
    func entities(for identifiers: [ResearchDepthEntity.ID]) async throws -> [ResearchDepthEntity] {
        return allDepths.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [ResearchDepthEntity] {
        return allDepths
    }
    
    private var allDepths: [ResearchDepthEntity] {
        [.quick, .standard, .detailed]
    }
}

struct LearningFocusEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Learning Focus")
    static var defaultQuery = LearningFocusQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let general = LearningFocusEntity(id: "general", name: "general investing")
    static let stocks = LearningFocusEntity(id: "stocks", name: "stock analysis")
    static let portfolio = LearningFocusEntity(id: "portfolio", name: "portfolio management")
    static let risk = LearningFocusEntity(id: "risk", name: "risk management")
}

struct LearningFocusQuery: EntityQuery {
    func entities(for identifiers: [LearningFocusEntity.ID]) async throws -> [LearningFocusEntity] {
        return allFoci.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [LearningFocusEntity] {
        return allFoci
    }
    
    private var allFoci: [LearningFocusEntity] {
        [.general, .stocks, .portfolio, .risk]
    }
}

struct LearningDurationEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Learning Duration")
    static var defaultQuery = LearningDurationQuery()
    
    let id: String
    let name: String
    let minutes: Int
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let short = LearningDurationEntity(id: "short", name: "5 minutes", minutes: 5)
    static let medium = LearningDurationEntity(id: "medium", name: "15 minutes", minutes: 15)
    static let long = LearningDurationEntity(id: "long", name: "30 minutes", minutes: 30)
}

struct LearningDurationQuery: EntityQuery {
    func entities(for identifiers: [LearningDurationEntity.ID]) async throws -> [LearningDurationEntity] {
        return allDurations.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [LearningDurationEntity] {
        return allDurations
    }
    
    private var allDurations: [LearningDurationEntity] {
        [.short, .medium, .long]
    }
}
