//
//  AdvancedFinancialIntents.swift
//  VibeFinance - Advanced Financial App Intents
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import AppIntents
import Foundation

// MARK: - Stock Price Intent

struct CheckStockPriceIntent: AppIntent {
    static var title: LocalizedStringResource = "Check Stock Price"
    static var description = IntentDescription("Get the current price and performance of a specific stock.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Stock Symbol", description: "Enter the stock symbol (e.g., AAPL, TSLA)")
    var stockSymbol: String
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let symbol = stockSymbol.uppercased()
        
        // In production, this would fetch real stock data from Polygon.io
        let stockData = getStockData(for: symbol)
        
        let dialog = IntentDialog(stringLiteral: """
        \(symbol) is trading at $\(String(format: "%.2f", stockData.price)). \
        Today's change: \(stockData.change >= 0 ? "+" : "")$\(String(format: "%.2f", stockData.change)) \
        (\(stockData.changePercent >= 0 ? "+" : "")\(String(format: "%.2f", stockData.changePercent))%)
        """)
        
        return .result(
            dialog: dialog,
            view: StockPriceSnippetView(
                symbol: symbol,
                price: stockData.price,
                change: stockData.change,
                changePercent: stockData.changePercent,
                volume: stockData.volume
            )
        )
    }
    
    private func getStockData(for symbol: String) -> (price: Double, change: Double, changePercent: Double, volume: Int) {
        // Mock data - in production, fetch from real API
        switch symbol {
        case "AAPL":
            return (185.25, 2.15, 1.17, 45_234_567)
        case "TSLA":
            return (245.80, -3.45, -1.38, 32_567_890)
        case "GOOGL":
            return (142.50, 1.25, 0.88, 28_456_123)
        case "MSFT":
            return (378.90, 4.20, 1.12, 19_876_543)
        default:
            return (100.00, 0.50, 0.50, 10_000_000)
        }
    }
}

// MARK: - Portfolio Analysis Intent

struct AnalyzePortfolioIntent: AppIntent {
    static var title: LocalizedStringResource = "Analyze Portfolio"
    static var description = IntentDescription("Get detailed analysis of your investment portfolio performance.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Analysis Type", description: "What type of analysis would you like?")
    var analysisType: AnalysisTypeEntity?
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let type = analysisType?.name ?? "overall performance"
        
        // Get portfolio analysis data
        let analysis = getPortfolioAnalysis(type: type)
        
        let dialog = IntentDialog(stringLiteral: analysis.summary)
        
        return .result(
            dialog: dialog,
            view: PortfolioAnalysisSnippetView(
                analysisType: type,
                metrics: analysis.metrics,
                recommendation: analysis.recommendation
            )
        )
    }
    
    private func getPortfolioAnalysis(type: String) -> (summary: String, metrics: [String: String], recommendation: String) {
        switch type {
        case "risk analysis":
            return (
                summary: "Your portfolio has a moderate risk level with a beta of 1.15. Diversification score: 7.5/10.",
                metrics: [
                    "Beta": "1.15",
                    "Sharpe Ratio": "1.42",
                    "Max Drawdown": "-8.5%",
                    "Volatility": "12.3%"
                ],
                recommendation: "Consider adding bonds to reduce overall portfolio risk."
            )
        case "diversification":
            return (
                summary: "Your portfolio is 75% diversified across 8 sectors. Tech allocation is high at 35%.",
                metrics: [
                    "Sectors": "8",
                    "Tech Allocation": "35%",
                    "International": "15%",
                    "Bonds": "10%"
                ],
                recommendation: "Reduce tech exposure and increase international diversification."
            )
        default:
            return (
                summary: "Your portfolio has gained 12.5% this year, outperforming the S&P 500 by 2.3%.",
                metrics: [
                    "YTD Return": "+12.5%",
                    "vs S&P 500": "+2.3%",
                    "Best Performer": "NVDA (+45%)",
                    "Worst Performer": "META (-8%)"
                ],
                recommendation: "Consider taking profits on high performers and rebalancing."
            )
        }
    }
}

// MARK: - Investment Recommendation Intent

struct GetInvestmentRecommendationIntent: AppIntent {
    static var title: LocalizedStringResource = "Get Investment Recommendation"
    static var description = IntentDescription("Receive personalized investment recommendations based on your profile.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Investment Amount", description: "How much are you looking to invest?")
    var amount: Double?
    
    @Parameter(title: "Risk Tolerance", description: "What's your risk tolerance?")
    var riskTolerance: RiskToleranceEntity?
    
    @Parameter(title: "Time Horizon", description: "What's your investment time horizon?")
    var timeHorizon: TimeHorizonEntity?
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let investmentAmount = amount ?? 1000.0
        let risk = riskTolerance?.name ?? "moderate"
        let horizon = timeHorizon?.name ?? "long-term"
        
        let recommendation = generateRecommendation(
            amount: investmentAmount,
            risk: risk,
            horizon: horizon
        )
        
        let dialog = IntentDialog(stringLiteral: """
        For $\(String(format: "%.0f", investmentAmount)) with \(risk) risk tolerance and \(horizon) horizon: \
        \(recommendation.summary)
        """)
        
        return .result(
            dialog: dialog,
            view: InvestmentRecommendationSnippetView(
                amount: investmentAmount,
                recommendations: recommendation.allocations,
                reasoning: recommendation.reasoning
            )
        )
    }
    
    private func generateRecommendation(amount: Double, risk: String, horizon: String) -> (summary: String, allocations: [String: Double], reasoning: String) {
        switch (risk, horizon) {
        case ("conservative", _):
            return (
                summary: "Focus on bonds and dividend stocks for stability.",
                allocations: ["Bonds": 60, "Dividend Stocks": 30, "Cash": 10],
                reasoning: "Conservative approach prioritizes capital preservation with steady income."
            )
        case ("aggressive", "long-term"):
            return (
                summary: "Emphasize growth stocks and emerging markets for maximum returns.",
                allocations: ["Growth Stocks": 70, "International": 20, "Small Cap": 10],
                reasoning: "Long-term aggressive strategy focuses on capital appreciation."
            )
        default:
            return (
                summary: "Balanced approach with mix of stocks, bonds, and international exposure.",
                allocations: ["US Stocks": 50, "International": 20, "Bonds": 20, "REITs": 10],
                reasoning: "Moderate risk balanced portfolio provides growth with stability."
            )
        }
    }
}

// MARK: - Market Alert Intent

struct SetMarketAlertIntent: AppIntent {
    static var title: LocalizedStringResource = "Set Market Alert"
    static var description = IntentDescription("Set up alerts for stock prices or market movements.")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Stock Symbol", description: "Which stock would you like to monitor?")
    var stockSymbol: String
    
    @Parameter(title: "Alert Type", description: "What type of alert?")
    var alertType: AlertTypeEntity
    
    @Parameter(title: "Target Price", description: "At what price should we alert you?")
    var targetPrice: Double
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let symbol = stockSymbol.uppercased()
        let type = alertType.name
        
        // In production, this would save the alert to the database
        let alertId = UUID().uuidString
        
        let dialog = IntentDialog(stringLiteral: """
        Alert set! I'll notify you when \(symbol) \(type) $\(String(format: "%.2f", targetPrice)). \
        Alert ID: \(alertId.prefix(8))
        """)
        
        return .result(dialog: dialog)
    }
}

// MARK: - Entity Definitions

struct AnalysisTypeEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Analysis Type")
    static var defaultQuery = AnalysisTypeQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let performance = AnalysisTypeEntity(id: "performance", name: "overall performance")
    static let risk = AnalysisTypeEntity(id: "risk", name: "risk analysis")
    static let diversification = AnalysisTypeEntity(id: "diversification", name: "diversification")
    static let allocation = AnalysisTypeEntity(id: "allocation", name: "asset allocation")
}

struct AnalysisTypeQuery: EntityQuery {
    func entities(for identifiers: [AnalysisTypeEntity.ID]) async throws -> [AnalysisTypeEntity] {
        return allTypes.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [AnalysisTypeEntity] {
        return allTypes
    }
    
    private var allTypes: [AnalysisTypeEntity] {
        [.performance, .risk, .diversification, .allocation]
    }
}

struct RiskToleranceEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Risk Tolerance")
    static var defaultQuery = RiskToleranceQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let conservative = RiskToleranceEntity(id: "conservative", name: "conservative")
    static let moderate = RiskToleranceEntity(id: "moderate", name: "moderate")
    static let aggressive = RiskToleranceEntity(id: "aggressive", name: "aggressive")
}

struct RiskToleranceQuery: EntityQuery {
    func entities(for identifiers: [RiskToleranceEntity.ID]) async throws -> [RiskToleranceEntity] {
        return allRiskLevels.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [RiskToleranceEntity] {
        return allRiskLevels
    }
    
    private var allRiskLevels: [RiskToleranceEntity] {
        [.conservative, .moderate, .aggressive]
    }
}

struct TimeHorizonEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Time Horizon")
    static var defaultQuery = TimeHorizonQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let shortTerm = TimeHorizonEntity(id: "short", name: "short-term")
    static let mediumTerm = TimeHorizonEntity(id: "medium", name: "medium-term")
    static let longTerm = TimeHorizonEntity(id: "long", name: "long-term")
}

struct TimeHorizonQuery: EntityQuery {
    func entities(for identifiers: [TimeHorizonEntity.ID]) async throws -> [TimeHorizonEntity] {
        return allHorizons.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [TimeHorizonEntity] {
        return allHorizons
    }
    
    private var allHorizons: [TimeHorizonEntity] {
        [.shortTerm, .mediumTerm, .longTerm]
    }
}

struct AlertTypeEntity: AppEntity {
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Alert Type")
    static var defaultQuery = AlertTypeQuery()
    
    let id: String
    let name: String
    
    var displayRepresentation: DisplayRepresentation {
        DisplayRepresentation(title: "\(name)")
    }
    
    static let above = AlertTypeEntity(id: "above", name: "goes above")
    static let below = AlertTypeEntity(id: "below", name: "goes below")
    static let reaches = AlertTypeEntity(id: "reaches", name: "reaches")
}

struct AlertTypeQuery: EntityQuery {
    func entities(for identifiers: [AlertTypeEntity.ID]) async throws -> [AlertTypeEntity] {
        return allTypes.filter { identifiers.contains($0.id) }
    }
    
    func suggestedEntities() async throws -> [AlertTypeEntity] {
        return allTypes
    }
    
    private var allTypes: [AlertTypeEntity] {
        [.above, .below, .reaches]
    }
}
