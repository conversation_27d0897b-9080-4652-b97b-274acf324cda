//
//  IntentSnippetViews.swift
//  VibeFinance - Snippet Views for App Intents
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import AppIntents

// MARK: - Portfolio Snippet View

struct PortfolioSnippetView: View {
    let totalValue: Double
    let todayChange: Double
    let todayChangePercent: Double
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "chart.pie.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text("Portfolio")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            // Portfolio Value
            VStack(alignment: .leading, spacing: 4) {
                Text("Total Value")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("$\(String(format: "%.2f", totalValue))")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
            }
            
            // Today's Change
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("Today's Change")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 4) {
                        Image(systemName: todayChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                            .foregroundColor(todayChange >= 0 ? .green : .red)
                        
                        Text("\(todayChange >= 0 ? "+" : "")$\(String(format: "%.2f", todayChange))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(todayChange >= 0 ? .green : .red)
                        
                        Text("(\(todayChangePercent >= 0 ? "+" : "")\(String(format: "%.2f", todayChangePercent))%)")
                            .font(.caption)
                            .foregroundColor(todayChange >= 0 ? .green : .red)
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Quest Snippet View

struct QuestSnippetView: View {
    let activeQuests: Int
    let completedQuests: Int
    let totalXP: Int
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "target")
                    .foregroundColor(.orange)
                    .font(.title2)
                
                Text("Learning Progress")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            // Stats Grid
            HStack(spacing: 16) {
                // Active Quests
                VStack(spacing: 4) {
                    Text("\(activeQuests)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    
                    Text("Active")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Divider()
                    .frame(height: 30)
                
                // Completed Quests
                VStack(spacing: 4) {
                    Text("\(completedQuests)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("Completed")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Divider()
                    .frame(height: 30)
                
                // Total XP
                VStack(spacing: 4) {
                    Text("\(totalXP)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                    
                    Text("XP")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Progress Indicator
            if activeQuests > 0 {
                HStack {
                    Image(systemName: "play.circle.fill")
                        .foregroundColor(.blue)
                        .font(.caption)
                    
                    Text("Continue learning to earn more XP!")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Market Snippet View

struct MarketSnippetView: View {
    let marketStatus: String
    let indices: [(name: String, value: String, change: String, isPositive: Bool)]
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Market Status")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                // Market Status Indicator
                HStack(spacing: 4) {
                    Circle()
                        .fill(marketStatus.contains("open") ? .green : .red)
                        .frame(width: 8, height: 8)
                    
                    Text(marketStatus)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Indices
            VStack(spacing: 8) {
                ForEach(indices, id: \.name) { index in
                    HStack {
                        Text(index.name)
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 2) {
                            Text(index.value)
                                .font(.subheadline)
                                .fontWeight(.semibold)
                            
                            HStack(spacing: 2) {
                                Image(systemName: index.isPositive ? "arrow.up.right" : "arrow.down.right")
                                    .font(.caption2)
                                
                                Text(index.change)
                                    .font(.caption)
                            }
                            .foregroundColor(index.isPositive ? .green : .red)
                        }
                    }
                    
                    if index.name != indices.last?.name {
                        Divider()
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - AI Advice Snippet View

struct AIAdviceSnippetView: View {
    let advisor: String
    let advice: String
    let timestamp: Date
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.purple)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("AI Advice")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("from \(advisor)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            // Advice Content
            VStack(alignment: .leading, spacing: 8) {
                Text(advice)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.leading)
                    .lineLimit(4)
                
                HStack {
                    Spacer()
                    
                    Text(timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Preview Providers

#Preview("Portfolio Snippet") {
    PortfolioSnippetView(
        totalValue: 112847.50,
        todayChange: 1247.50,
        todayChangePercent: 1.12
    )
    .padding()
}

#Preview("Quest Snippet") {
    QuestSnippetView(
        activeQuests: 3,
        completedQuests: 12,
        totalXP: 2450
    )
    .padding()
}

#Preview("Market Snippet") {
    MarketSnippetView(
        marketStatus: "Market is open",
        indices: [
            (name: "S&P 500", value: "$4,502.25", change: "+0.75%", isPositive: true),
            (name: "NASDAQ", value: "$14,125.80", change: "+1.25%", isPositive: true),
            (name: "DOW", value: "$34,890.45", change: "-0.15%", isPositive: false)
        ]
    )
    .padding()
}

// MARK: - Stock Price Snippet View

struct StockPriceSnippetView: View {
    let symbol: String
    let price: Double
    let change: Double
    let changePercent: Double
    let volume: Int

    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.blue)
                    .font(.title2)

                Text(symbol)
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                // Live indicator
                HStack(spacing: 4) {
                    Circle()
                        .fill(.green)
                        .frame(width: 6, height: 6)

                    Text("LIVE")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                }
            }

            // Price and Change
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("$\(String(format: "%.2f", price))")
                        .font(.title)
                        .fontWeight(.bold)

                    HStack(spacing: 4) {
                        Image(systemName: change >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)

                        Text("\(change >= 0 ? "+" : "")$\(String(format: "%.2f", change))")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Text("(\(changePercent >= 0 ? "+" : "")\(String(format: "%.2f", changePercent))%)")
                            .font(.caption)
                    }
                    .foregroundColor(change >= 0 ? .green : .red)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Volume")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("\(formatVolume(volume))")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    private func formatVolume(_ volume: Int) -> String {
        if volume >= 1_000_000 {
            return String(format: "%.1fM", Double(volume) / 1_000_000)
        } else if volume >= 1_000 {
            return String(format: "%.1fK", Double(volume) / 1_000)
        } else {
            return "\(volume)"
        }
    }
}

// MARK: - Portfolio Analysis Snippet View

struct PortfolioAnalysisSnippetView: View {
    let analysisType: String
    let metrics: [String: String]
    let recommendation: String

    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "chart.bar.doc.horizontal")
                    .foregroundColor(.purple)
                    .font(.title2)

                Text("Portfolio Analysis")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            // Analysis Type
            Text(analysisType.capitalized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Metrics
            VStack(spacing: 8) {
                ForEach(Array(metrics.keys.sorted()), id: \.self) { key in
                    HStack {
                        Text(key)
                            .font(.subheadline)

                        Spacer()

                        Text(metrics[key] ?? "")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )

            // Recommendation
            VStack(alignment: .leading, spacing: 4) {
                Text("Recommendation")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Text(recommendation)
                    .font(.subheadline)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Investment Recommendation Snippet View

struct InvestmentRecommendationSnippetView: View {
    let amount: Double
    let recommendations: [String: Double]
    let reasoning: String

    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.orange)
                    .font(.title2)

                Text("Investment Recommendation")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            // Amount
            Text("For $\(String(format: "%.0f", amount))")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Allocation Chart
            VStack(spacing: 6) {
                ForEach(Array(recommendations.keys.sorted()), id: \.self) { category in
                    let percentage = recommendations[category] ?? 0
                    let dollarAmount = amount * (percentage / 100)

                    HStack {
                        Text(category)
                            .font(.subheadline)

                        Spacer()

                        VStack(alignment: .trailing, spacing: 2) {
                            Text("$\(String(format: "%.0f", dollarAmount))")
                                .font(.subheadline)
                                .fontWeight(.semibold)

                            Text("\(String(format: "%.0f", percentage))%")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    // Progress bar
                    GeometryReader { geometry in
                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color(.systemGray5))
                            .frame(height: 4)
                            .overlay(
                                HStack {
                                    RoundedRectangle(cornerRadius: 2)
                                        .fill(.blue)
                                        .frame(width: geometry.size.width * (percentage / 100), height: 4)

                                    Spacer()
                                }
                            )
                    }
                    .frame(height: 4)
                }
            }

            // Reasoning
            Text(reasoning)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

#Preview("AI Advice Snippet") {
    AIAdviceSnippetView(
        advisor: "Warren Buffett",
        advice: "Focus on companies with strong fundamentals and hold for the long term. Remember, time in the market beats timing the market.",
        timestamp: Date()
    )
    .padding()
}

#Preview("Stock Price Snippet") {
    StockPriceSnippetView(
        symbol: "AAPL",
        price: 185.25,
        change: 2.15,
        changePercent: 1.17,
        volume: 45_234_567
    )
    .padding()
}

#Preview("Portfolio Analysis Snippet") {
    PortfolioAnalysisSnippetView(
        analysisType: "risk analysis",
        metrics: [
            "Beta": "1.15",
            "Sharpe Ratio": "1.42",
            "Max Drawdown": "-8.5%",
            "Volatility": "12.3%"
        ],
        recommendation: "Consider adding bonds to reduce overall portfolio risk."
    )
    .padding()
}

// MARK: - Daily Briefing Snippet View

struct DailyBriefingSnippetView: View {
    let briefing: DailyBriefing

    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                Image(systemName: "newspaper.fill")
                    .foregroundColor(.blue)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    Text("Daily Financial Briefing")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text(briefing.timestamp, style: .date)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }

            // Portfolio Summary
            VStack(spacing: 8) {
                HStack {
                    Text("Portfolio")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        Text("$\(String(format: "%.2f", briefing.portfolio.totalValue))")
                            .font(.subheadline)
                            .fontWeight(.semibold)

                        HStack(spacing: 2) {
                            Image(systemName: briefing.portfolio.todayChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                                .font(.caption2)

                            Text("\(briefing.portfolio.todayChange >= 0 ? "+" : "")$\(String(format: "%.2f", briefing.portfolio.todayChange))")
                                .font(.caption)
                        }
                        .foregroundColor(briefing.portfolio.todayChange >= 0 ? .green : .red)
                    }
                }

                HStack {
                    Text("Best: \(briefing.portfolio.topPerformer)")
                        .font(.caption)
                        .foregroundColor(.green)

                    Spacer()

                    Text("Worst: \(briefing.portfolio.worstPerformer)")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6))
            )

            // Market Summary
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Market")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Text("\(briefing.market.sentiment) • \(briefing.market.status)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text("S&P: +\(String(format: "%.2f", briefing.market.spyChange))%")
                        .font(.caption)
                        .foregroundColor(.green)

                    Text("NASDAQ: +\(String(format: "%.2f", briefing.market.nasdaqChange))%")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }

            // Learning Progress
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Learning")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Text("Next: \(briefing.quests.nextQuest)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Text("+\(briefing.quests.xpEarned) XP")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.purple)
            }

            // AI Insight
            VStack(alignment: .leading, spacing: 4) {
                Text("Today's Insight")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Text(briefing.insight)
                    .font(.caption)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Rebalancing Snippet View

struct RebalancingSnippetView: View {
    let analysis: RebalancingAnalysis

    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "scale.3d")
                    .foregroundColor(.orange)
                    .font(.title2)

                Text("Portfolio Rebalancing")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            // Strategy and Deviation
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Strategy")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(analysis.strategy.capitalized)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("Deviation")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text("\(String(format: "%.1f", analysis.deviation))%")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(analysis.deviation > 10 ? .red : analysis.deviation > 5 ? .orange : .green)
                }
            }

            // Recommendation
            VStack(alignment: .leading, spacing: 8) {
                Text("Recommendation")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Text(analysis.recommendation)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .leading)

            // Actions
            VStack(alignment: .leading, spacing: 6) {
                Text("Suggested Actions")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                ForEach(Array(analysis.actions.enumerated()), id: \.offset) { index, action in
                    HStack(spacing: 8) {
                        Text("\(index + 1).")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(action)
                            .font(.caption)
                            .foregroundColor(.primary)

                        Spacer()
                    }
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Research Snippet View

struct ResearchSnippetView: View {
    let research: InvestmentResearch

    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                Image(systemName: "magnifyingglass.circle.fill")
                    .foregroundColor(.blue)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    Text(research.symbol)
                        .font(.headline)
                        .fontWeight(.bold)

                    Text(research.companyName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text(research.rating)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(ratingColor(research.rating))

                    Text("Target: $\(String(format: "%.2f", research.priceTarget))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Summary
            Text(research.summary)
                .font(.subheadline)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Key Metrics
            if !research.keyMetrics.isEmpty {
                VStack(spacing: 6) {
                    Text("Key Metrics")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    ForEach(Array(research.keyMetrics.keys.sorted()), id: \.self) { key in
                        HStack {
                            Text(key)
                                .font(.caption)

                            Spacer()

                            Text(research.keyMetrics[key] ?? "")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                    }
                }
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color(.systemGray6))
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }

    private func ratingColor(_ rating: String) -> Color {
        switch rating.lowercased() {
        case "buy", "strong buy":
            return .green
        case "hold", "neutral":
            return .orange
        case "sell", "strong sell":
            return .red
        default:
            return .primary
        }
    }
}

#Preview("Investment Recommendation Snippet") {
    InvestmentRecommendationSnippetView(
        amount: 5000,
        recommendations: [
            "US Stocks": 50,
            "International": 20,
            "Bonds": 20,
            "REITs": 10
        ],
        reasoning: "Moderate risk balanced portfolio provides growth with stability."
    )
    .padding()
}

#Preview("Daily Briefing Snippet") {
    DailyBriefingSnippetView(
        briefing: DailyBriefing(
            summary: "Portfolio up $1,247.50 today",
            portfolio: PortfolioSummary(
                totalValue: 112847.50,
                todayChange: 1247.50,
                todayChangePercent: 1.12,
                topPerformer: "NVDA (+3.2%)",
                worstPerformer: "META (-1.8%)"
            ),
            market: MarketSummary(
                status: "Open",
                spyChange: 0.75,
                nasdaqChange: 1.25,
                vixLevel: 18.5,
                sentiment: "Bullish"
            ),
            quests: QuestSummary(
                activeQuests: 2,
                completedToday: 1,
                xpEarned: 150,
                nextQuest: "Understanding ETFs"
            ),
            insight: "Market volatility is low today. Consider rebalancing your tech allocation.",
            timestamp: Date()
        )
    )
    .padding()
}
