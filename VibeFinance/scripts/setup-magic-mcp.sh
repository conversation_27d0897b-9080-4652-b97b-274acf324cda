#!/bin/bash

# Comprehensive MCP Setup Script for VibeFinance
# Sets up all MCP servers: Magic, ElevenLabs, Supabase, Sequential Thinking, Internet Search

set -e

echo "🚀 Setting up All MCP Servers for VibeFinance"
echo "=============================================="
echo "This will install:"
echo "• 21st.dev Magic MCP (UI Generation)"
echo "• ElevenLabs MCP (Text-to-Speech)"
echo "• Supabase MCP (Database Operations)"
echo "• Sequential Thinking MCP (Enhanced Reasoning)"
echo "• Internet Search MCP (Web Search)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js first."
        echo "Visit: https://nodejs.org/"
        exit 1
    fi
}

# Check if npx is available
check_npx() {
    print_status "Checking npx availability..."
    
    if command -v npx &> /dev/null; then
        print_success "npx is available"
    else
        print_error "npx is not available. Please update Node.js."
        exit 1
    fi
}

# Check Python and uvx for ElevenLabs
check_python() {
    print_status "Checking Python installation..."

    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python is installed: $PYTHON_VERSION"
    else
        print_error "Python 3 is not installed. Please install Python 3 first."
        echo "Visit: https://python.org/"
        exit 1
    fi

    # Check for uvx (pipx alternative)
    if command -v uvx &> /dev/null; then
        print_success "uvx is available"
    else
        print_status "Installing uvx..."
        pip3 install --user uv
        if ! command -v uvx &> /dev/null; then
            print_warning "uvx not found in PATH. You may need to add ~/.local/bin to your PATH"
        fi
    fi
}

# Get API keys from user
get_api_keys() {
    echo ""
    print_status "API Keys Setup"
    echo "=============="
    echo ""

    # 21st.dev Magic API Key
    print_status "21st.dev Magic API Key (for UI generation)"
    echo "Get one from: https://21st.dev/magic/console"
    read -p "Enter your 21st.dev Magic API key: " MAGIC_API_KEY

    # ElevenLabs API Key
    echo ""
    print_status "ElevenLabs API Key (for text-to-speech)"
    echo "Get one from: https://elevenlabs.io/app/settings/api-keys"
    read -p "Enter your ElevenLabs API key (or press Enter to skip): " ELEVENLABS_API_KEY

    # Brave Search API Key
    echo ""
    print_status "Brave Search API Key (for internet search)"
    echo "Get one from: https://api.search.brave.com/app/keys"
    read -p "Enter your Brave Search API key (or press Enter to skip): " BRAVE_API_KEY

    # Supabase credentials
    echo ""
    print_status "Supabase Configuration (for database operations)"
    echo "Your Supabase URL: https://mcrbwwkltigjawnlunlh.supabase.co"
    SUPABASE_URL="https://mcrbwwkltigjawnlunlh.supabase.co"
    SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1jcmJ3d2tsdGlnamF3bmx1bmxoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzI2OTgsImV4cCI6MjA2Mzg0ODY5OH0.d83C4KZXNqpheRdtbMXzu4xxNd1P6d554jMBEzQNlKs"

    if [ -z "$MAGIC_API_KEY" ]; then
        print_error "21st.dev Magic API key is required. Exiting."
        exit 1
    fi

    print_success "API keys collected"
}

# Detect IDE
detect_ide() {
    print_status "Detecting IDE..."
    
    if [ -d "/Applications/Cursor.app" ] || command -v cursor &> /dev/null; then
        IDE="cursor"
        print_success "Cursor IDE detected"
    elif command -v code &> /dev/null; then
        IDE="code"
        print_success "VS Code detected"
    else
        print_warning "No supported IDE detected automatically"
        echo "Supported IDEs: Cursor, VS Code, Windsurf, Cline"
        read -p "Which IDE are you using? (cursor/code/windsurf/cline): " IDE
    fi
}

# Install ElevenLabs MCP
install_elevenlabs_mcp() {
    if [ -n "$ELEVENLABS_API_KEY" ]; then
        print_status "Installing ElevenLabs MCP..."
        pip3 install elevenlabs-mcp
        print_success "ElevenLabs MCP installed!"
    else
        print_warning "Skipping ElevenLabs MCP (no API key provided)"
    fi
}

# Install all MCP servers
install_all_mcp_servers() {
    print_status "Installing MCP servers..."

    # Install 21st.dev Magic MCP
    case $IDE in
        "cursor")
            print_status "Installing 21st.dev Magic for Cursor..."
            npx @21st-dev/cli@latest install cursor --api-key "$MAGIC_API_KEY"
            ;;
        "code")
            print_status "Installing 21st.dev Magic for VS Code..."
            npx @21st-dev/cli@latest install code --api-key "$MAGIC_API_KEY"
            ;;
        "windsurf")
            print_status "Installing 21st.dev Magic for Windsurf..."
            npx @21st-dev/cli@latest install windsurf --api-key "$MAGIC_API_KEY"
            ;;
        "cline")
            print_status "Installing 21st.dev Magic for Cline..."
            npx @21st-dev/cli@latest install cline --api-key "$MAGIC_API_KEY"
            ;;
        *)
            print_warning "Manual installation required for $IDE"
            print_status "Please follow the manual setup instructions in docs/MCP_SETUP.md"
            ;;
    esac

    # Install ElevenLabs MCP
    install_elevenlabs_mcp

    # Install other MCP servers (these don't need special installation)
    print_status "Other MCP servers (Supabase, Sequential Thinking, Internet Search) will be auto-installed when first used."

    print_success "All MCP servers installation completed!"
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    case $IDE in
        "cursor")
            CONFIG_FILE="$HOME/.cursor/mcp.json"
            ;;
        "code")
            CONFIG_FILE="$HOME/.vscode/mcp.json"
            ;;
        "windsurf")
            CONFIG_FILE="$HOME/.codeium/windsurf/mcp_config.json"
            ;;
        "cline")
            CONFIG_FILE="$HOME/.cline/mcp_config.json"
            ;;
        *)
            print_warning "Cannot verify installation for $IDE"
            return
            ;;
    esac
    
    if [ -f "$CONFIG_FILE" ]; then
        print_success "Configuration file found: $CONFIG_FILE"
    else
        print_warning "Configuration file not found. Manual setup may be required."
    fi
}

# Show usage instructions
show_usage() {
    echo ""
    print_success "🎉 All MCP Servers Setup Completed!"
    echo ""
    echo "Next steps:"
    echo "1. Restart your IDE ($IDE)"
    echo "2. Open the Composer/Chat feature"
    echo "3. Try the following commands:"
    echo ""
    echo "🎨 UI Generation (21st.dev Magic):"
    echo "   /ui create a SwiftUI financial portfolio card"
    echo "   /ui design a SwiftUI quest completion animation"
    echo "   /ui build a SwiftUI investment simulator interface"
    echo ""
    if [ -n "$ELEVENLABS_API_KEY" ]; then
        echo "🔊 Text-to-Speech (ElevenLabs):"
        echo "   Generate voice narration for app tutorials"
        echo "   Create audio feedback for user actions"
        echo ""
    fi
    echo "🗄️ Database Operations (Supabase):"
    echo "   Query user data and analytics"
    echo "   Manage app database schema"
    echo ""
    if [ -n "$BRAVE_API_KEY" ]; then
        echo "🔍 Internet Search (Brave):"
        echo "   Search for financial news and data"
        echo "   Research investment information"
        echo ""
    fi
    echo "🧠 Enhanced Reasoning (Sequential Thinking):"
    echo "   Complex problem solving and analysis"
    echo "   Step-by-step feature planning"
    echo ""
    echo "📚 Documentation:"
    echo "   • docs/MCP_SETUP.md - Detailed setup guide"
    echo "   • docs/MOCK_AUTH_GUIDE.md - Mock authentication"
    echo ""
    print_status "Happy coding! 🚀"
}

# Main execution
main() {
    echo ""
    check_nodejs
    check_npx
    check_python
    get_api_keys
    detect_ide
    install_all_mcp_servers
    verify_installation
    show_usage
}

# Run main function
main
