#!/bin/bash

# ElevenLabs MCP Setup Script
# Sets up ElevenLabs MCP for text-to-speech capabilities

set -e

echo "🔊 Setting up ElevenLabs MCP for VibeFinance"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Python installation
check_python() {
    print_status "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python is installed: $PYTHON_VERSION"
    else
        print_error "Python 3 is not installed. Please install Python 3 first."
        echo "Visit: https://python.org/"
        exit 1
    fi
}

# Check pip installation
check_pip() {
    print_status "Checking pip installation..."
    
    if command -v pip3 &> /dev/null; then
        print_success "pip3 is available"
    else
        print_error "pip3 is not available. Please install pip first."
        exit 1
    fi
}

# Install ElevenLabs MCP
install_elevenlabs_mcp() {
    print_status "Installing ElevenLabs MCP..."
    
    # Install the package
    pip3 install elevenlabs-mcp
    
    print_success "ElevenLabs MCP installed successfully!"
}

# Test ElevenLabs MCP
test_elevenlabs_mcp() {
    print_status "Testing ElevenLabs MCP installation..."
    
    if [ -n "$ELEVENLABS_API_KEY" ]; then
        print_status "Testing with provided API key..."
        python3 -m elevenlabs_mcp --api-key="$ELEVENLABS_API_KEY" --print
        print_success "ElevenLabs MCP test completed!"
    else
        print_warning "No API key provided. Skipping test."
        print_status "To test later, run:"
        echo "python3 -m elevenlabs_mcp --api-key=YOUR_API_KEY --print"
    fi
}

# Get API key
get_api_key() {
    echo ""
    print_status "ElevenLabs API Key Setup"
    echo "========================"
    echo ""
    print_status "You need an ElevenLabs API key to use text-to-speech features."
    echo "Get one from: https://elevenlabs.io/app/settings/api-keys"
    echo ""
    
    read -p "Enter your ElevenLabs API key (or press Enter to skip): " ELEVENLABS_API_KEY
    
    if [ -n "$ELEVENLABS_API_KEY" ]; then
        print_success "API key received"
        
        # Save to environment file for future use
        echo "export ELEVENLABS_API_KEY=\"$ELEVENLABS_API_KEY\"" >> ~/.bashrc
        print_status "API key saved to ~/.bashrc"
    else
        print_warning "No API key provided. You can set it later."
    fi
}

# Show usage instructions
show_usage() {
    echo ""
    print_success "🎉 ElevenLabs MCP Setup Completed!"
    echo ""
    echo "Usage Examples:"
    echo ""
    echo "1. Generate welcome message:"
    echo "   'Generate voice: Welcome to VibeFinance!'"
    echo ""
    echo "2. Create tutorial narration:"
    echo "   'Generate voice: Let's learn about investing step by step.'"
    echo ""
    echo "3. Audio feedback:"
    echo "   'Generate voice: Congratulations! Quest completed successfully.'"
    echo ""
    echo "Configuration:"
    echo "• API Key: Set in your IDE's MCP configuration"
    echo "• Voice Models: Choose from ElevenLabs voice library"
    echo "• Audio Format: MP3, WAV supported"
    echo ""
    if [ -n "$ELEVENLABS_API_KEY" ]; then
        echo "✅ API Key: Configured"
    else
        echo "⚠️  API Key: Not configured (set in IDE)"
    fi
    echo ""
    print_status "Ready to generate amazing voice content! 🔊"
}

# Main execution
main() {
    echo ""
    check_python
    check_pip
    get_api_key
    install_elevenlabs_mcp
    test_elevenlabs_mcp
    show_usage
}

# Run main function
main
