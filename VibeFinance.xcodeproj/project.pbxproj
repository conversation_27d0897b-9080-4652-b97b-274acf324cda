// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		7CDCD1902DE21DA600693D4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7CDCD1732DE21DA100693D4C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7CDCD17A2DE21DA100693D4C;
			remoteInfo = VibeFinance;
		};
		7CDCD19A2DE21DA600693D4C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7CDCD1732DE21DA100693D4C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7CDCD17A2DE21DA100693D4C;
			remoteInfo = VibeFinance;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7CDCD17B2DE21DA100693D4C /* VibeFinance.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VibeFinance.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7CDCD18F2DE21DA600693D4C /* VibeFinanceTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VibeFinanceTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7CDCD1992DE21DA600693D4C /* VibeFinanceUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VibeFinanceUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7CDCD1A12DE21DA600693D4C /* Exceptions for "VibeFinance" folder in "VibeFinance" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7CDCD17A2DE21DA100693D4C /* VibeFinance */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7CDCD17D2DE21DA100693D4C /* VibeFinance */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7CDCD1A12DE21DA600693D4C /* Exceptions for "VibeFinance" folder in "VibeFinance" target */,
			);
			path = VibeFinance;
			sourceTree = "<group>";
		};
		7CDCD1922DE21DA600693D4C /* VibeFinanceTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = VibeFinanceTests;
			sourceTree = "<group>";
		};
		7CDCD19C2DE21DA600693D4C /* VibeFinanceUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = VibeFinanceUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7CDCD1782DE21DA100693D4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD18C2DE21DA600693D4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD1962DE21DA600693D4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7CDCD1722DE21DA100693D4C = {
			isa = PBXGroup;
			children = (
				7CDCD17D2DE21DA100693D4C /* VibeFinance */,
				7CDCD1922DE21DA600693D4C /* VibeFinanceTests */,
				7CDCD19C2DE21DA600693D4C /* VibeFinanceUITests */,
				7CDCD17C2DE21DA100693D4C /* Products */,
			);
			sourceTree = "<group>";
		};
		7CDCD17C2DE21DA100693D4C /* Products */ = {
			isa = PBXGroup;
			children = (
				7CDCD17B2DE21DA100693D4C /* VibeFinance.app */,
				7CDCD18F2DE21DA600693D4C /* VibeFinanceTests.xctest */,
				7CDCD1992DE21DA600693D4C /* VibeFinanceUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7CDCD17A2DE21DA100693D4C /* VibeFinance */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDCD1A22DE21DA600693D4C /* Build configuration list for PBXNativeTarget "VibeFinance" */;
			buildPhases = (
				7CDCD1772DE21DA100693D4C /* Sources */,
				7CDCD1782DE21DA100693D4C /* Frameworks */,
				7CDCD1792DE21DA100693D4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7CDCD17D2DE21DA100693D4C /* VibeFinance */,
			);
			name = VibeFinance;
			packageProductDependencies = (
			);
			productName = VibeFinance;
			productReference = 7CDCD17B2DE21DA100693D4C /* VibeFinance.app */;
			productType = "com.apple.product-type.application";
		};
		7CDCD18E2DE21DA600693D4C /* VibeFinanceTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDCD1A72DE21DA600693D4C /* Build configuration list for PBXNativeTarget "VibeFinanceTests" */;
			buildPhases = (
				7CDCD18B2DE21DA600693D4C /* Sources */,
				7CDCD18C2DE21DA600693D4C /* Frameworks */,
				7CDCD18D2DE21DA600693D4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7CDCD1912DE21DA600693D4C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7CDCD1922DE21DA600693D4C /* VibeFinanceTests */,
			);
			name = VibeFinanceTests;
			packageProductDependencies = (
			);
			productName = VibeFinanceTests;
			productReference = 7CDCD18F2DE21DA600693D4C /* VibeFinanceTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7CDCD1982DE21DA600693D4C /* VibeFinanceUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7CDCD1AA2DE21DA600693D4C /* Build configuration list for PBXNativeTarget "VibeFinanceUITests" */;
			buildPhases = (
				7CDCD1952DE21DA600693D4C /* Sources */,
				7CDCD1962DE21DA600693D4C /* Frameworks */,
				7CDCD1972DE21DA600693D4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7CDCD19B2DE21DA600693D4C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7CDCD19C2DE21DA600693D4C /* VibeFinanceUITests */,
			);
			name = VibeFinanceUITests;
			packageProductDependencies = (
			);
			productName = VibeFinanceUITests;
			productReference = 7CDCD1992DE21DA600693D4C /* VibeFinanceUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7CDCD1732DE21DA100693D4C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					7CDCD17A2DE21DA100693D4C = {
						CreatedOnToolsVersion = 16.2;
					};
					7CDCD18E2DE21DA600693D4C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7CDCD17A2DE21DA100693D4C;
					};
					7CDCD1982DE21DA600693D4C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 7CDCD17A2DE21DA100693D4C;
					};
				};
			};
			buildConfigurationList = 7CDCD1762DE21DA100693D4C /* Build configuration list for PBXProject "VibeFinance" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7CDCD1722DE21DA100693D4C;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 7CDCD17C2DE21DA100693D4C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7CDCD17A2DE21DA100693D4C /* VibeFinance */,
				7CDCD18E2DE21DA600693D4C /* VibeFinanceTests */,
				7CDCD1982DE21DA600693D4C /* VibeFinanceUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7CDCD1792DE21DA100693D4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD18D2DE21DA600693D4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD1972DE21DA600693D4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7CDCD1772DE21DA100693D4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD18B2DE21DA600693D4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7CDCD1952DE21DA600693D4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7CDCD1912DE21DA600693D4C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7CDCD17A2DE21DA100693D4C /* VibeFinance */;
			targetProxy = 7CDCD1902DE21DA600693D4C /* PBXContainerItemProxy */;
		};
		7CDCD19B2DE21DA600693D4C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7CDCD17A2DE21DA100693D4C /* VibeFinance */;
			targetProxy = 7CDCD19A2DE21DA600693D4C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7CDCD1A32DE21DA600693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = VibeFinance/VibeFinance.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"VibeFinance/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VibeFinance/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.VibeFinance;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7CDCD1A42DE21DA600693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = VibeFinance/VibeFinance.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"VibeFinance/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VibeFinance/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.VibeFinance;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7CDCD1A52DE21DA600693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7CDCD1A62DE21DA600693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = QNW477Q52S;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7CDCD1A82DE21DA600693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.VibeFinanceTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VibeFinance.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeFinance";
			};
			name = Debug;
		};
		7CDCD1A92DE21DA600693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.VibeFinanceTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VibeFinance.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeFinance";
			};
			name = Release;
		};
		7CDCD1AB2DE21DA600693D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.VibeFinanceUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = VibeFinance;
			};
			name = Debug;
		};
		7CDCD1AC2DE21DA600693D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.md.VibeFinanceUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = VibeFinance;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7CDCD1762DE21DA100693D4C /* Build configuration list for PBXProject "VibeFinance" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD1A52DE21DA600693D4C /* Debug */,
				7CDCD1A62DE21DA600693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDCD1A22DE21DA600693D4C /* Build configuration list for PBXNativeTarget "VibeFinance" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD1A32DE21DA600693D4C /* Debug */,
				7CDCD1A42DE21DA600693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDCD1A72DE21DA600693D4C /* Build configuration list for PBXNativeTarget "VibeFinanceTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD1A82DE21DA600693D4C /* Debug */,
				7CDCD1A92DE21DA600693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7CDCD1AA2DE21DA600693D4C /* Build configuration list for PBXNativeTarget "VibeFinanceUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7CDCD1AB2DE21DA600693D4C /* Debug */,
				7CDCD1AC2DE21DA600693D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7CDCD1732DE21DA100693D4C /* Project object */;
}
