# VibeFinance - Development Setup Guide

## 🚀 Quick Start

This guide will help you set up VibeFinance for development and testing. The app includes comprehensive mock authentication and development tools to make testing all features easy.

## 📋 Prerequisites

### **Required Software**
- **Xcode 15.0+** - Latest version recommended
- **iOS 17.0+** - Target deployment version
- **macOS 14.0+** - For Xcode compatibility
- **Git** - For version control

### **Optional Tools**
- **Simulator** - For testing without physical device
- **TestFlight** - For beta testing
- **Instruments** - For performance profiling

## 🔧 Installation Steps

### **1. Clone the Repository**
```bash
git clone https://github.com/mdha81/VibeFinance.git
cd VibeFinance
```

### **2. Open in Xcode**
```bash
open VibeFinance.xcodeproj
```

### **3. Configure Development Settings**
The app is pre-configured for development with mock authentication enabled by default.

### **4. Build and Run**
- Select your target device or simulator
- Press `Cmd + R` to build and run
- The app will automatically log you in with mock authentication

## 🔐 Mock Authentication System

### **Automatic Login**
The app includes an automatic mock authentication system for development:

```swift
// In AuthManager.swift
init() {
    #if DEBUG
    isDevelopmentMode = DevelopmentConfig.isDevelopmentMode
    useMockAuth = UserDefaults.standard.bool(forKey: "useMockAuth") || DevelopmentConfig.enableMockAuth
    
    // Auto-login in development mode for easier testing
    if isDevelopmentMode && DevelopmentConfig.enableMockAuth {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.quickDevLogin()
        }
    }
    #endif
}
```

### **Mock User Profiles**
The app includes several pre-configured mock users:

```swift
// Development mock users
static let mockUsers: [MockUser] = [
    MockUser(
        id: UUID(),
        email: "<EMAIL>",
        username: "DevUser",
        subscriptionTier: .pro,
        xp: 2500,
        level: 15
    ),
    MockUser(
        id: UUID(),
        email: "<EMAIL>", 
        username: "BasicUser",
        subscriptionTier: .basic,
        xp: 1200,
        level: 8
    ),
    MockUser(
        id: UUID(),
        email: "<EMAIL>",
        username: "FreeUser", 
        subscriptionTier: .free,
        xp: 500,
        level: 3
    )
]
```

### **Quick Dev Login**
You can manually trigger mock authentication using the Quick Dev Login button in the DevelopmentDemoView or by calling:

```swift
authManager.quickDevLogin()
```

## 🛠️ Development Configuration

### **DevelopmentConfig.swift**
The app includes a comprehensive development configuration:

```swift
struct DevelopmentConfig {
    // Core development settings
    static let isDevelopmentMode = true
    static let enableMockAuth = true
    static let enableMockAPI = false
    static let showDeveloperTools = true
    static let enablePerformanceMonitoring = true
    
    // API testing settings
    static let useLocalAPI = false
    static let enableAPILogging = true
    static let mockNetworkDelay = 0.5
    
    // UI testing settings
    static let enableUITestMode = false
    static let showDebugInfo = true
    static let enableAnimations = true
}
```

### **Environment Variables (Optional)**
For full API functionality, you can set up environment variables:

```bash
# AI Services
export GEMINI_API_KEY="your_gemini_api_key_here"

# Market Data
export POLYGON_API_KEY="your_polygon_api_key_here"

# Trading Platform
export ALPACA_API_KEY="your_alpaca_api_key_here"
export ALPACA_SECRET_KEY="your_alpaca_secret_key_here"

# Backend Services
export SUPABASE_URL="your_supabase_project_url"
export SUPABASE_ANON_KEY="your_supabase_anon_key"

# News Services
export NEWS_API_KEY="your_news_api_key_here"
```

**Note**: The app works fully without these API keys using mock data for development.

## 🧪 Testing Features

### **Testing Tab**
The app includes a dedicated Testing tab (visible in debug builds) with:

- **API Testing**: Test all external API integrations
- **Performance Monitoring**: Real-time performance metrics
- **Mock Data Controls**: Toggle mock authentication and data
- **Developer Settings**: Access development configurations
- **Build Information**: View app version and build details

### **API Test Service**
```swift
// Test all APIs
let apiTestService = APITestService()
await apiTestService.runAllTests()

// Test specific services
await apiTestService.testGeminiAI()
await apiTestService.testPolygonAPI()
await apiTestService.testAlpacaAPI()
```

### **Performance Monitoring**
```swift
// Monitor app performance
let performanceManager = PerformanceManager.shared
performanceManager.startMonitoring()

// View metrics
print("CPU Usage: \(performanceManager.cpuUsage)%")
print("Memory Usage: \(performanceManager.memoryUsage)MB")
```

## 🎮 Exploring Features

### **All Features Available**
With mock authentication enabled, you have access to all features:

1. **🏠 Feed Tab** - AI-powered financial content
2. **🎯 Quests Tab** - Daily quests and gamification
3. **👥 Squads Tab** - Social investing features (Pro)
4. **📈 Trading Tab** - Investment simulator and real trading
5. **📊 Analytics Tab** - Portfolio analytics (Pro)
6. **💬 Chat Tab** - AI financial advisor
7. **🧪 Testing Tab** - Development tools
8. **👤 Profile Tab** - User profile and settings

### **Mock Data Available**
- **Portfolio Data**: Pre-populated investment portfolio
- **Quest Data**: Daily quests with varying difficulty
- **Squad Data**: Sample investment squads
- **Chat History**: Previous AI conversations
- **Market Data**: Real-time stock prices (via Polygon.io)
- **User Stats**: XP, level, achievements

## 🔄 Development Workflow

### **1. Feature Development**
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes
# Test with mock authentication
# Commit changes
git commit -m "Add new feature"

# Push and create PR
git push origin feature/new-feature
```

### **2. Testing Workflow**
1. **Launch app** - Auto-login with mock authentication
2. **Navigate tabs** - Test all implemented features
3. **Use Testing tab** - Run API tests and monitor performance
4. **Check different user types** - Switch between Free/Basic/Pro users
5. **Test edge cases** - Use mock data to test various scenarios

### **3. Debugging**
- **Console Logs**: Comprehensive logging for all operations
- **Performance Monitor**: Real-time performance metrics
- **API Testing**: Verify all external integrations
- **Mock Data**: Test with consistent, predictable data

## 🚀 Building for Different Environments

### **Debug Build (Default)**
- Mock authentication enabled
- All development tools visible
- Comprehensive logging
- Performance monitoring

### **Release Build**
```bash
# Build for release
xcodebuild -scheme VibeFinance -configuration Release
```

- Mock authentication disabled
- Production API endpoints
- Optimized performance
- Minimal logging

### **TestFlight Build**
```bash
# Archive for TestFlight
xcodebuild archive -scheme VibeFinance -archivePath VibeFinance.xcarchive
```

## 🔍 Troubleshooting

### **Common Issues**

#### **App Not Loading**
- Check internet connection
- Verify Xcode version compatibility
- Clean build folder (`Cmd + Shift + K`)

#### **Mock Authentication Not Working**
- Verify `DevelopmentConfig.enableMockAuth = true`
- Check console for authentication logs
- Try manual `quickDevLogin()` call

#### **Missing Features**
- Ensure you're using the correct user tier (Free/Basic/Pro)
- Check if feature requires specific subscription level
- Verify mock data is properly loaded

#### **Performance Issues**
- Use Performance Monitor in Testing tab
- Check memory usage and CPU utilization
- Review network request patterns

### **Debug Console Commands**
```swift
// Force mock login
authManager.quickDevLogin()

// Switch user tier
userManager.updateSubscriptionTier(.pro)

// Reset mock data
MockDataManager.shared.resetAllData()

// Enable API logging
APIConfiguration.enableLogging = true
```

## 📱 Device Testing

### **Simulator Testing**
- **iPhone 16**: Primary test device
- **iPhone 15**: Compatibility testing
- **iPad**: Tablet layout testing

### **Physical Device Testing**
1. Connect device via USB
2. Select device in Xcode
3. Build and run (`Cmd + R`)
4. Test with real touch interactions

## 🎯 Next Steps

### **For New Developers**
1. **Read the documentation** in `/docs` folder
2. **Explore the codebase** starting with `VibeFinanceApp.swift`
3. **Test all features** using mock authentication
4. **Review the architecture** in `TECHNICAL_IMPLEMENTATION.md`

### **For Feature Development**
1. **Understand the MVVM pattern** used throughout the app
2. **Follow the manager-based architecture** for business logic
3. **Use environment objects** for dependency injection
4. **Test with mock data** before implementing real APIs

### **For API Integration**
1. **Review `API_INTEGRATIONS.md`** for detailed API documentation
2. **Use the Testing tab** to verify API connections
3. **Implement proper error handling** for network requests
4. **Follow rate limiting** best practices

## 🔒 Security Considerations

### **Development Security**
- Never commit API keys to version control
- Use environment variables for sensitive data
- Test with mock data to avoid API costs
- Implement proper error handling

### **Production Security**
- Validate all user inputs
- Implement proper authentication
- Use HTTPS for all API communications
- Follow iOS security best practices

---

This development setup guide provides everything you need to start developing and testing VibeFinance. The mock authentication system makes it easy to explore all features without complex setup requirements.
