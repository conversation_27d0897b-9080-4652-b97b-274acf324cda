# VibeFinance - API Integrations Documentation

## 🌐 Overview

VibeFinance integrates with multiple external APIs to provide comprehensive financial services, AI-powered insights, and real-time market data.

## 🔑 API Configuration

### **Environment Variables**
```bash
# Required API Keys
GEMINI_API_KEY=your_gemini_api_key_here
POLYGON_API_KEY=your_polygon_api_key_here
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
NEWS_API_KEY=your_news_api_key_here
```

### **API Configuration File**
```swift
// VibeFinance/Config/APIConfiguration.swift
struct APIConfiguration {
    // AI Services
    static let geminiAPIKey = ProcessInfo.processInfo.environment["GEMINI_API_KEY"] ?? ""
    
    // Market Data
    static let polygonAPIKey = ProcessInfo.processInfo.environment["POLYGON_API_KEY"] ?? ""
    
    // Trading Platform
    static let alpacaAPIKey = ProcessInfo.processInfo.environment["ALPACA_API_KEY"] ?? ""
    static let alpacaSecretKey = ProcessInfo.processInfo.environment["ALPACA_SECRET_KEY"] ?? ""
    
    // Backend Services
    static let supabaseURL = ProcessInfo.processInfo.environment["SUPABASE_URL"] ?? ""
    static let supabaseAnonKey = ProcessInfo.processInfo.environment["SUPABASE_ANON_KEY"] ?? ""
    
    // News Services
    static let newsAPIKey = ProcessInfo.processInfo.environment["NEWS_API_KEY"] ?? ""
}
```

---

## 🤖 Gemini AI Integration

### **Service Implementation**
```swift
// VibeFinance/Services/GeminiAIService.swift
class GeminiAIService {
    static let shared = GeminiAIService()
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta"
    
    func generateResponse(prompt: String) async throws -> String
    func getChatResponse(message: String) async -> String
    func analyzePortfolio(_ portfolio: Portfolio) async -> AnalysisResult
}
```

### **API Endpoints Used**
- **Chat Completion**: `/models/gemini-pro:generateContent`
- **Text Generation**: `/models/gemini-pro:generateContent`

### **Features Powered by Gemini AI**
- **AI Financial Advisor**: Natural language financial advice
- **Portfolio Analysis**: Intelligent portfolio insights
- **Market Commentary**: AI-generated market analysis
- **Investment Recommendations**: Personalized investment suggestions
- **Educational Content**: AI-curated learning materials

### **Request Example**
```swift
let prompt = """
Analyze this portfolio and provide investment advice:
- AAPL: 50 shares at $150
- GOOGL: 25 shares at $120
- TSLA: 30 shares at $200
User risk tolerance: Moderate
Investment timeline: 5 years
"""

let response = try await GeminiAIService.shared.generateResponse(prompt: prompt)
```

---

## 📈 Polygon.io Market Data

### **Service Implementation**
```swift
// VibeFinance/Services/PolygonStockService.swift
class PolygonStockService {
    private let baseURL = "https://api.polygon.io"
    private let apiKey = APIConfiguration.polygonAPIKey
    
    func getStockPrice(symbol: String) async throws -> StockPrice
    func getStockDetails(symbol: String) async throws -> StockDetails
    func getMarketNews() async throws -> [NewsItem]
}
```

### **API Endpoints Used**
- **Real-time Quotes**: `/v2/aggs/ticker/{symbol}/prev`
- **Stock Details**: `/v3/reference/tickers/{symbol}`
- **Market News**: `/v2/reference/news`
- **Historical Data**: `/v2/aggs/ticker/{symbol}/range/{multiplier}/{timespan}/{from}/{to}`

### **Features Powered by Polygon**
- **Real-time Stock Prices**: Live market data for trading
- **Historical Charts**: Price history and technical analysis
- **Market News**: Financial news and market updates
- **Company Information**: Detailed company profiles
- **Market Indices**: S&P 500, NASDAQ, Dow Jones data

### **Request Examples**
```swift
// Get current stock price
let stockPrice = try await PolygonStockService.shared.getStockPrice(symbol: "AAPL")

// Get company details
let stockDetails = try await PolygonStockService.shared.getStockDetails(symbol: "AAPL")

// Get market news
let news = try await PolygonStockService.shared.getMarketNews()
```

---

## 🏦 Alpaca Markets Trading

### **Service Implementation**
```swift
// VibeFinance/Services/AlpacaService.swift
class AlpacaService {
    private let baseURL = "https://paper-api.alpaca.markets"  // Paper trading
    private let liveURL = "https://api.alpaca.markets"        // Live trading
    
    func getAccount() async throws -> TradingAccount
    func getPositions() async throws -> [Position]
    func placeOrder(_ order: OrderRequest) async throws -> Order
    func getOrders() async throws -> [Order]
}
```

### **API Endpoints Used**
- **Account Info**: `/v2/account`
- **Positions**: `/v2/positions`
- **Orders**: `/v2/orders`
- **Portfolio History**: `/v2/account/portfolio/history`
- **Market Data**: `/v2/stocks/{symbol}/quotes/latest`

### **Features Powered by Alpaca**
- **Live Trading**: Real money stock trading
- **Paper Trading**: Risk-free practice trading
- **Portfolio Management**: Track real investments
- **Order Management**: Buy/sell orders with various types
- **Account Analytics**: Performance tracking and reporting

### **Order Types Supported**
- **Market Orders**: Execute immediately at current price
- **Limit Orders**: Execute at specific price or better
- **Stop Orders**: Trigger when price reaches stop level
- **Stop-Limit Orders**: Combination of stop and limit orders

### **Request Examples**
```swift
// Get account information
let account = try await AlpacaService.shared.getAccount()

// Place a buy order
let orderRequest = OrderRequest(
    symbol: "AAPL",
    qty: 10,
    side: .buy,
    type: .market,
    timeInForce: .day
)
let order = try await AlpacaService.shared.placeOrder(orderRequest)

// Get current positions
let positions = try await AlpacaService.shared.getPositions()
```

---

## 🗄️ Supabase Backend

### **Service Implementation**
```swift
// VibeFinance/Services/SupabaseService.swift
class SupabaseService {
    static let shared = SupabaseService()
    private let client: SupabaseClient
    
    func signUp(email: String, password: String) async throws -> AuthResponse
    func signIn(email: String, password: String) async throws -> AuthResponse
    func signOut() async throws
    func getCurrentUser() async throws -> User?
}
```

### **Database Tables**
- **users**: User profiles and preferences
- **portfolios**: User investment portfolios
- **quests**: Quest data and progress
- **squads**: Investment squad information
- **chat_messages**: AI chat history
- **achievements**: User achievements and badges

### **Features Powered by Supabase**
- **User Authentication**: Secure login and registration
- **Data Storage**: User profiles, portfolios, and progress
- **Real-time Updates**: Live data synchronization
- **File Storage**: Profile pictures and documents
- **Row Level Security**: Data privacy and security

### **Database Schema Examples**
```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    subscription_tier TEXT DEFAULT 'free',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Portfolios table
CREATE TABLE portfolios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    symbol TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    purchase_price DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT NOW()
);
```

---

## 📰 News API Integration

### **Service Implementation**
```swift
// VibeFinance/Services/NewsService.swift
class NewsService {
    private let baseURL = "https://newsapi.org/v2"
    private let apiKey = APIConfiguration.newsAPIKey
    
    func getFinancialNews() async throws -> [NewsArticle]
    func getStockNews(symbol: String) async throws -> [NewsArticle]
    func getMarketNews() async throws -> [NewsArticle]
}
```

### **API Endpoints Used**
- **Top Headlines**: `/top-headlines?category=business`
- **Everything**: `/everything?q=finance`
- **Sources**: `/sources?category=business`

### **Features Powered by News API**
- **Financial News Feed**: Latest financial news and updates
- **Stock-Specific News**: News related to specific companies
- **Market Analysis**: Broader market news and trends
- **Breaking News**: Real-time important financial updates

---

## 🔄 API Response Handling

### **Generic API Client**
```swift
protocol APIService {
    var baseURL: String { get }
    var apiKey: String { get }
    
    func makeRequest<T: Codable>(_ endpoint: APIEndpoint) async throws -> T
}

extension APIService {
    func makeRequest<T: Codable>(_ endpoint: APIEndpoint) async throws -> T {
        let url = URL(string: baseURL + endpoint.path)!
        var request = URLRequest(url: url)
        request.httpMethod = endpoint.method.rawValue
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw APIError.invalidResponse
        }
        
        return try JSONDecoder().decode(T.self, from: data)
    }
}
```

### **Error Handling**
```swift
enum APIError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case decodingError
    case networkError(Error)
    case rateLimitExceeded
    case unauthorized
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .invalidResponse:
            return "Invalid response from server"
        case .decodingError:
            return "Failed to decode response"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .rateLimitExceeded:
            return "Rate limit exceeded"
        case .unauthorized:
            return "Unauthorized access"
        }
    }
}
```

---

## 📊 Rate Limiting & Optimization

### **Rate Limiting Strategy**
```swift
class RateLimiter {
    private var requestCounts: [String: Int] = [:]
    private var resetTimes: [String: Date] = [:]
    
    func canMakeRequest(for service: String) -> Bool {
        let now = Date()
        let key = service
        
        // Reset count if time window has passed
        if let resetTime = resetTimes[key], now > resetTime {
            requestCounts[key] = 0
            resetTimes[key] = Calendar.current.date(byAdding: .minute, value: 1, to: now)
        }
        
        let currentCount = requestCounts[key] ?? 0
        let limit = getRateLimit(for: service)
        
        return currentCount < limit
    }
    
    private func getRateLimit(for service: String) -> Int {
        switch service {
        case "polygon": return 5  // 5 requests per minute
        case "gemini": return 60  // 60 requests per minute
        case "alpaca": return 200 // 200 requests per minute
        default: return 10
        }
    }
}
```

### **Request Batching**
```swift
class RequestBatcher {
    private var pendingRequests: [APIRequest] = []
    private let batchSize = 10
    private let batchInterval: TimeInterval = 1.0
    
    func addRequest(_ request: APIRequest) {
        pendingRequests.append(request)
        
        if pendingRequests.count >= batchSize {
            processBatch()
        }
    }
    
    private func processBatch() {
        let batch = Array(pendingRequests.prefix(batchSize))
        pendingRequests.removeFirst(min(batchSize, pendingRequests.count))
        
        Task {
            await executeBatch(batch)
        }
    }
}
```

---

## 🧪 Testing API Integrations

### **API Test Service**
```swift
// VibeFinance/Services/APITestService.swift
class APITestService: ObservableObject {
    @Published var testResults: [APITestResult] = []
    @Published var isRunningTests = false
    
    func runAllTests() async {
        isRunningTests = true
        
        await testGeminiAI()
        await testPolygonAPI()
        await testAlpacaAPI()
        await testSupabaseConnection()
        await testNewsAPI()
        
        isRunningTests = false
    }
    
    private func testGeminiAI() async {
        do {
            let response = try await GeminiAIService.shared.generateResponse(prompt: "Test prompt")
            addTestResult(.success("Gemini AI", "Connection successful"))
        } catch {
            addTestResult(.failure("Gemini AI", error.localizedDescription))
        }
    }
}
```

### **Mock API Responses**
```swift
class MockAPIService: APIService {
    var baseURL: String = "https://mock.api.com"
    var apiKey: String = "mock-key"
    
    func makeRequest<T: Codable>(_ endpoint: APIEndpoint) async throws -> T {
        // Return mock data for testing
        switch endpoint.path {
        case "/stocks/AAPL":
            return MockData.appleStock as! T
        case "/chat/completion":
            return MockData.aiResponse as! T
        default:
            throw APIError.invalidURL
        }
    }
}
```

---

## 🔒 Security Best Practices

### **API Key Security**
- Store API keys in environment variables
- Never commit API keys to version control
- Use different keys for development and production
- Rotate keys regularly
- Monitor API usage for unusual activity

### **Request Security**
- Use HTTPS for all API communications
- Implement request signing where required
- Validate all API responses
- Handle sensitive data appropriately
- Log security events for monitoring

### **Error Handling**
- Don't expose internal errors to users
- Log detailed errors for debugging
- Implement retry logic for transient failures
- Gracefully handle rate limiting
- Provide meaningful user feedback

---

This documentation provides a comprehensive overview of all API integrations in VibeFinance, including implementation details, security considerations, and testing strategies.
