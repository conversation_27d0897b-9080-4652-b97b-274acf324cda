# Complete MCP Setup Guide for VibeFinance

This guide will help you set up all MCP (Model Context Protocol) servers to supercharge your VibeFinance development experience with AI-powered tools.

## What are MCP Servers?

MCP servers provide specialized AI capabilities that integrate directly into your IDE. VibeFinance uses multiple MCP servers for different purposes:

### 🎨 21st.dev Magic MCP
AI-driven UI component generation through natural language descriptions.

### 🔊 ElevenLabs MCP
High-quality text-to-speech generation for app audio features.

### 🗄️ Supabase MCP
Direct database operations and queries from your IDE.

### 🧠 Sequential Thinking MCP
Enhanced reasoning and step-by-step problem solving.

### 🔍 Internet Search MCP
Real-time web search capabilities for research and data gathering.

### Features:
- **AI-Powered UI Generation**: Create UI components by describing them in natural language
- **Modern Component Library**: Access to a vast collection of pre-built, customizable components
- **Real-time Preview**: Instantly see your components as you create them
- **TypeScript Support**: Full TypeScript support for type-safe development
- **SwiftUI Integration**: Generate SwiftUI components for iOS development

## Prerequisites

- Node.js (Latest LTS version recommended)
- Cursor IDE (recommended) or other supported IDEs
- 21st.dev Magic API Key

## Setup Instructions

### Step 1: Get Your API Key

1. Visit [21st.dev Magic Console](https://21st.dev/magic/console)
2. Sign up or log in to your account
3. Generate a new API key
4. Copy and save the API key securely

### Step 2: Install Magic MCP

#### Option A: Automatic Installation (Recommended)

Run this command in your terminal:

```bash
npx @21st-dev/cli@latest install cursor --api-key YOUR_API_KEY
```

Replace `YOUR_API_KEY` with your actual API key from Step 1.

#### Option B: Manual Configuration

The MCP configuration file has already been created at `.cursor/mcp.json`. When you first use Magic MCP in Cursor, you'll be prompted to enter your API key.

### Step 3: Verify Installation

1. Open Cursor IDE
2. Open the Composer (Cmd/Ctrl + I)
3. Type `/ui` followed by a component description
4. Example: `/ui create a modern navigation bar with responsive design`

## Usage Examples

### Creating SwiftUI Components

```
/ui create a SwiftUI card component with:
- Rounded corners and shadow
- Title and subtitle text
- Action button at the bottom
- Support for dark mode
```

### Creating Complex Layouts

```
/ui design a SwiftUI onboarding flow with:
- 3 pages with smooth transitions
- Progress indicator
- Skip and continue buttons
- Gradient background
```

### Enhancing Existing Components

```
/ui improve the existing AuthView by adding:
- Better visual feedback for loading states
- Smooth animations for form validation
- Enhanced accessibility support
```

## Integration with VibeFinance

The Magic MCP is particularly useful for enhancing the VibeFinance app's UI components:

### Authentication Views
- Enhance login/signup forms with modern designs
- Add smooth transitions and animations
- Improve accessibility and user feedback

### Financial Dashboard Components
- Create beautiful chart components
- Design modern card layouts for financial data
- Build interactive portfolio views

### Onboarding Experience
- Design engaging welcome screens
- Create interactive tutorials
- Build smooth page transitions

## Best Practices

1. **Be Specific**: Provide detailed descriptions of what you want
2. **Mention Platform**: Specify "SwiftUI" for iOS components
3. **Include Styling**: Mention colors, animations, and layout preferences
4. **Consider Accessibility**: Ask for accessibility features when needed

## Troubleshooting

### Common Issues

1. **API Key Not Working**
   - Verify your API key is correct
   - Check if you have sufficient credits
   - Ensure the key hasn't expired

2. **Components Not Generating**
   - Make sure you're using the `/ui` prefix
   - Check your internet connection
   - Verify MCP server is running

3. **SwiftUI Compatibility**
   - Ensure generated components are compatible with your iOS version
   - Check for any deprecated APIs
   - Test components in your target environment

### Getting Help

- Join the [Discord community](https://discord.gg/Qx4rFunHfm)
- Check the [GitHub repository](https://github.com/21st-dev/magic-mcp)
- Visit [21st.dev documentation](https://21st.dev/magic)

## Example Prompts for VibeFinance

Here are some specific prompts you can use to enhance the VibeFinance app:

```
/ui create a SwiftUI financial portfolio card showing:
- Portfolio value with percentage change
- Color-coded gains/losses (green/red)
- Small chart preview
- Tap gesture for details

/ui design a SwiftUI quest completion animation with:
- Confetti effect
- XP points counter animation
- Achievement badge reveal
- Smooth fade transitions

/ui build a SwiftUI investment simulator interface with:
- Stock search bar with suggestions
- Buy/sell buttons with confirmation
- Portfolio balance display
- Transaction history list
```

## Next Steps

1. Get your API key from 21st.dev
2. Test the installation with a simple component
3. Start enhancing your VibeFinance UI components
4. Explore advanced features and customizations

Happy coding! 🚀
