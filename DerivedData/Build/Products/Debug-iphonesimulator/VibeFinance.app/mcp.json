{"mcpServers": {"@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest"], "env": {"API_KEY": "${input:magicApiKey}"}}, "ElevenLabs": {"command": "uvx", "args": ["elevenlabs-mcp"], "env": {"ELEVENLABS_API_KEY": "${input:elevenLabsApiKey}"}}, "supabase": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-supabase@latest"], "env": {"SUPABASE_URL": "${input:supabaseUrl}", "SUPABASE_ANON_KEY": "${input:supabaseAnonKey}"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}, "internet-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search@latest"], "env": {"BRAVE_API_KEY": "${input:braveApi<PERSON>ey}"}}}, "inputs": [{"type": "promptString", "id": "magicApi<PERSON>ey", "description": "21st.dev Magic API Key", "password": true}, {"type": "promptString", "id": "elevenLabsApiKey", "description": "ElevenLabs API Key", "password": true}, {"type": "promptString", "id": "supabaseUrl", "description": "Supabase Project URL", "password": false}, {"type": "promptString", "id": "supabaseAnonKey", "description": "Supabase Anonymous Key", "password": true}, {"type": "promptString", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Brave Search API Key", "password": true}]}