# MCP Quick Reference for VibeFinance

This is a quick reference guide for all MCP (Model Context Protocol) servers available in VibeFinance development.

## 🚀 Quick Setup Commands

```bash
# Setup all MCP servers at once
./scripts/setup-magic-mcp.sh

# Individual setups
npm run install-magic-cursor      # 21st.dev Magic for Cursor
npm run install-elevenlabs        # ElevenLabs TTS
npm run setup-elevenlabs          # ElevenLabs with guided setup
```

## 🎨 21st.dev Magic MCP (UI Generation)

### Basic UI Components
```
/ui create a SwiftUI button with rounded corners and gradient background
/ui design a SwiftUI card with shadow and border
/ui build a SwiftUI navigation bar with custom styling
```

### VibeFinance Specific Components
```
/ui create a SwiftUI financial portfolio card with:
- Portfolio value display
- Percentage change indicator (green/red)
- Small chart preview
- Tap gesture for details

/ui design a SwiftUI quest card with:
- Quest title and description
- Progress bar with XP points
- Completion status
- Reward preview

/ui build a SwiftUI investment simulator interface with:
- Stock search bar
- Buy/sell buttons
- Portfolio balance
- Transaction history

/ui create a SwiftUI social squad card with:
- Member profile pictures
- Squad performance stats
- Join/leave buttons
- Investment goals
```

### Advanced Animations
```
/ui create a SwiftUI quest completion animation with:
- Confetti effect
- XP counter animation
- Achievement badge reveal
- Smooth transitions

/ui design a SwiftUI portfolio growth animation with:
- Number counting animation
- Chart line drawing
- Color transitions
- Haptic feedback
```

## 🔊 ElevenLabs MCP (Text-to-Speech)

### Welcome Messages
```
Generate voice: "Welcome to VibeFinance! Let's start building your financial future together."

Generate voice: "Hi there! Ready to learn about investing? Let's begin with your first quest."
```

### Quest Feedback
```
Generate voice: "Congratulations! You've completed the 'First Investment' quest and earned 100 XP points!"

Generate voice: "Great job! You've unlocked the Investment Simulator. Time to practice with virtual money!"

Generate voice: "Amazing! You've joined your first squad. Let's invest together!"
```

### Educational Content
```
Generate voice: "Let's learn about compound interest. When you invest money, you earn returns not just on your initial investment, but also on the returns you've already earned."

Generate voice: "Diversification means spreading your investments across different types of assets to reduce risk."

Generate voice: "Dollar-cost averaging is an investment strategy where you invest a fixed amount regularly, regardless of market conditions."
```

### Error and Success Messages
```
Generate voice: "Oops! Something went wrong. Please try again."

Generate voice: "Your investment has been successfully executed!"

Generate voice: "Portfolio updated! Your balance is now $1,250."
```

## 🗄️ Supabase MCP (Database Operations)

### User Queries
```
-- Get user profile
SELECT * FROM users WHERE email = '<EMAIL>';

-- Get user's portfolio
SELECT * FROM portfolios WHERE user_id = 'user-uuid';

-- Get user's quest progress
SELECT q.title, qc.completed_at, qc.xp_earned 
FROM quest_completions qc 
JOIN quests q ON qc.quest_id = q.id 
WHERE qc.user_id = 'user-uuid';
```

### Analytics Queries
```
-- Top performing users
SELECT u.username, p.total_value, p.percentage_gain
FROM portfolios p
JOIN users u ON p.user_id = u.id
ORDER BY p.percentage_gain DESC
LIMIT 10;

-- Most popular quests
SELECT q.title, COUNT(qc.id) as completions
FROM quests q
LEFT JOIN quest_completions qc ON q.id = qc.quest_id
GROUP BY q.id, q.title
ORDER BY completions DESC;

-- Squad performance
SELECT s.name, AVG(p.percentage_gain) as avg_performance
FROM squads s
JOIN squad_members sm ON s.id = sm.squad_id
JOIN portfolios p ON sm.user_id = p.user_id
GROUP BY s.id, s.name
ORDER BY avg_performance DESC;
```

### Data Management
```
-- Create new quest
INSERT INTO quests (title, description, xp_reward, difficulty)
VALUES ('Learn About ETFs', 'Complete the ETF basics tutorial', 50, 'beginner');

-- Update user portfolio
UPDATE portfolios 
SET total_value = 1250.00, percentage_gain = 25.0 
WHERE user_id = 'user-uuid';

-- Add quest completion
INSERT INTO quest_completions (user_id, quest_id, completed_at, xp_earned)
VALUES ('user-uuid', 'quest-uuid', NOW(), 50);
```

## 🧠 Sequential Thinking MCP (Enhanced Reasoning)

### Feature Planning
```
Plan the implementation of a new social investment feature:
1. What are the core requirements?
2. How should the user flow work?
3. What database schema changes are needed?
4. What security considerations are important?
5. How do we ensure user privacy?

Analyze the best approach for gamifying financial education:
- What game mechanics work best for learning?
- How do we balance fun with educational value?
- What rewards system would motivate users?
- How do we track learning progress?
```

### Problem Solving
```
How should we handle real-time portfolio updates efficiently?
- What are the performance considerations?
- How do we minimize API calls?
- What caching strategies should we use?
- How do we handle network failures?

Design a secure authentication system for financial data:
- What authentication methods are most secure?
- How do we protect sensitive financial information?
- What compliance requirements must we meet?
- How do we handle session management?
```

### Architecture Decisions
```
Evaluate different approaches for implementing the investment simulator:
- Real-time data vs. delayed data
- Local calculation vs. server-side
- Offline capability requirements
- Scalability considerations

Compare options for implementing social features:
- Privacy vs. social engagement
- Moderation requirements
- Data sharing policies
- Community guidelines
```

## 🔍 Internet Search MCP (Web Research)

### Market Research
```
Search for "latest fintech app features 2024"
Search for "best practices financial education apps"
Search for "gamification in investment apps"
Search for "Gen Z investment preferences"
```

### Technical Research
```
Search for "SwiftUI best practices 2024"
Search for "iOS app performance optimization"
Search for "real-time data streaming iOS"
Search for "financial app security requirements"
```

### Competitive Analysis
```
Search for "top investment apps for beginners"
Search for "Robinhood app features"
Search for "Acorns app user experience"
Search for "investment education platforms"
```

### Financial Data
```
Search for "current cryptocurrency market trends"
Search for "stock market news today"
Search for "investment education resources"
Search for "financial literacy statistics"
```

## 🔧 Configuration Files

### Cursor IDE
- **Main Config**: `.cursor/mcp.json` (with prompts for API keys)
- **Pre-configured**: `.cursor/mcp-with-keys.json` (keys included)

### API Keys Required
- **21st.dev Magic**: `d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019`
- **ElevenLabs**: `***************************************************`
- **Supabase URL**: `https://mcrbwwkltigjawnlunlh.supabase.co`
- **Supabase Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **Brave Search**: Get from [Brave API](https://api.search.brave.com/app/keys)

## 🚨 Troubleshooting

### Common Issues
```bash
# MCP servers not loading
1. Restart your IDE
2. Check API keys are correct
3. Verify internet connection
4. Check IDE console for errors

# ElevenLabs not working
pip install elevenlabs-mcp
python -m elevenlabs_mcp --api-key=YOUR_KEY --print

# Magic MCP not generating
npx @21st-dev/magic@latest --version
```

### Quick Fixes
```bash
# Reinstall Magic MCP
npm run install-magic-cursor

# Test ElevenLabs
npm run test-elevenlabs

# Check all installations
npm run check-magic
```

## 📚 Documentation Links

- **Complete Setup Guide**: [docs/MCP_SETUP.md](MCP_SETUP.md)
- **Mock Auth Guide**: [docs/MOCK_AUTH_GUIDE.md](MOCK_AUTH_GUIDE.md)
- **Main README**: [README.md](../README.md)

---

**Pro Tip**: Use multiple MCP servers together for powerful workflows:
1. Use Sequential Thinking to plan a feature
2. Use Magic MCP to generate the UI
3. Use Supabase MCP to set up the database
4. Use ElevenLabs to add voice feedback
5. Use Internet Search for research and validation

Happy coding! 🚀
