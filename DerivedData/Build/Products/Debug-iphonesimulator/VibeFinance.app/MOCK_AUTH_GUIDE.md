# Mock Authentication System

This guide explains how to use the mock authentication system in VibeFinance for development purposes.

## Overview

The mock authentication system allows developers to bypass real authentication during development, making it easier to test features without needing to sign up or sign in with real credentials every time.

## Features

- **Quick Login**: Instantly log in with predefined mock users
- **No Network Calls**: Skip API calls for faster development
- **Multiple Test Users**: Switch between different user profiles
- **Development Mode Toggle**: Easy on/off switch for mock authentication
- **Persistent Settings**: Your mock auth preferences are saved

## How to Enable Mock Authentication

### Method 1: Developer Settings UI

1. Launch the app in debug mode
2. On the authentication screen, look for the "Developer Settings" button at the bottom
3. Tap "Developer Settings"
4. Toggle "Mock Authentication" to ON
5. Use "Quick Login" or "Instant Login (Dev User)" to sign in

### Method 2: Configuration File

Edit `VibeFinance/Config/DevelopmentConfig.swift`:

```swift
static let enableMockAuth = true
```

### Method 3: Quick Dev Login Button

When mock authentication is enabled, you'll see a yellow "Quick Dev Login" button on the auth screen that instantly logs you in.

## Available Mock Users

The system comes with several predefined mock users:

1. **Dev User**
   - Email: `<EMAIL>`
   - Username: `DevUser`
   - Use case: General development testing

2. **Test User**
   - Email: `<EMAIL>`
   - Username: `TestUser`
   - Use case: Feature testing

3. **Demo User**
   - Email: `<EMAIL>`
   - Username: `DemoUser`
   - Use case: Demo presentations

4. **Investor Pro**
   - Email: `<EMAIL>`
   - Username: `InvestorPro`
   - Use case: Advanced feature testing

## Using Mock Authentication

### Quick Login (Recommended)

1. Enable mock authentication in Developer Settings
2. Tap the "Quick Dev Login" button on the auth screen
3. You'll be instantly logged in as the default dev user

### Manual Mock Login

1. Enable mock authentication in Developer Settings
2. Enter any email and password in the login form
3. Tap "Sign In" - the system will create a mock user with your email
4. You'll be logged in without any network calls

### Switching Users

1. Open Developer Settings
2. Tap "Quick Login"
3. Select from the list of available mock users
4. You'll be instantly switched to that user

## Development Configuration

### DevelopmentConfig.swift

The `DevelopmentConfig.swift` file contains all development-related settings:

```swift
// Enable/disable mock authentication
static let enableMockAuth = true

// Default mock user for quick login
static let defaultMockUser = MockUser(...)

// All available mock users
static let mockUsers: [MockUser] = [...]
```

### AuthManager Integration

The `AuthManager` automatically detects development mode and enables mock authentication features:

- Checks `DevelopmentConfig.enableMockAuth`
- Provides mock login methods
- Simulates network delays for realistic testing
- Maintains the same API as real authentication

## Best Practices

### When to Use Mock Authentication

✅ **Good for:**
- UI development and testing
- Feature development
- Quick iterations
- Demo preparations
- Testing user flows

❌ **Avoid for:**
- Testing actual authentication logic
- Network error handling
- Production builds
- Security testing

### Development Workflow

1. **Start Development**: Enable mock auth for quick testing
2. **Feature Testing**: Use different mock users to test various scenarios
3. **Integration Testing**: Disable mock auth to test real authentication
4. **Production**: Mock auth is automatically disabled in release builds

### Security Considerations

- Mock authentication is only available in DEBUG builds
- No real credentials are stored or transmitted
- Mock users have fake UUIDs and data
- Settings are stored locally and don't affect production

## Troubleshooting

### Mock Authentication Not Working

1. **Check Build Configuration**: Ensure you're running a DEBUG build
2. **Verify Settings**: Check that mock auth is enabled in Developer Settings
3. **Clear Settings**: Use "Reset All Settings" in Developer Settings
4. **Restart App**: Sometimes a restart is needed after changing settings

### Can't Access Developer Settings

1. **Debug Build**: Developer Settings only appear in DEBUG builds
2. **Development Mode**: Ensure `isDevelopmentMode` is true
3. **UI Location**: Look for the gear icon button at the bottom of the auth screen

### Mock User Data Issues

1. **Reset Users**: Use "Reset All Settings" to clear mock user data
2. **Check Config**: Verify mock users are properly defined in `DevelopmentConfig.swift`
3. **UUID Conflicts**: Each mock user should have a unique UUID

## Advanced Usage

### Custom Mock Users

Add custom mock users in `DevelopmentConfig.swift`:

```swift
static let mockUsers: [MockUser] = [
    // Existing users...
    MockUser(
        id: UUID(),
        email: "<EMAIL>",
        username: "CustomUser",
        firstName: "Custom",
        lastName: "User"
    )
]
```

### Conditional Features

Use mock authentication state for conditional features:

```swift
if authManager.useMockAuth {
    // Show development-only features
    // Skip certain validations
    // Use mock data
}
```

### Testing Different Scenarios

```swift
// Test new user flow
authManager.mockSignIn(userIndex: 0)

// Test existing user flow  
authManager.mockSignIn(userIndex: 1)

// Test user with specific data
let customUser = MockUser(...)
authManager.currentUser = customUser.toUser()
```

## Integration with Real Authentication

The mock authentication system is designed to seamlessly integrate with real authentication:

- Same `AuthManager` interface
- Same published properties (`isAuthenticated`, `currentUser`, etc.)
- Same error handling patterns
- Easy toggle between mock and real auth

When you're ready to test real authentication, simply disable mock auth in Developer Settings, and the app will use the real Supabase authentication system.

## Conclusion

The mock authentication system significantly speeds up development by eliminating the need for real authentication during feature development. Use it liberally during development, but remember to test with real authentication before releasing features.

Happy coding! 🚀
