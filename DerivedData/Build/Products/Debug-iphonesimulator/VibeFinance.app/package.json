{"name": "vibefi-development", "version": "1.0.0", "description": "Development tools and scripts for VibeFinance iOS app", "scripts": {"setup-all-mcp": "./scripts/setup-magic-mcp.sh", "setup-elevenlabs": "./scripts/setup-elevenlabs-mcp.sh", "install-magic-cursor": "npx @21st-dev/cli@latest install cursor --api-key d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019", "install-magic-vscode": "npx @21st-dev/cli@latest install code --api-key d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019", "install-magic-windsurf": "npx @21st-dev/cli@latest install windsurf --api-key d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019", "install-magic-cline": "npx @21st-dev/cli@latest install cline --api-key d3c18f0df32d547b2f2d036443c4bab0b240fd94b0e6ce505953c6a6d88ab019", "install-elevenlabs": "pip install elevenlabs-mcp", "test-elevenlabs": "python -m elevenlabs_mcp --api-key=*************************************************** --print", "check-magic": "npx @21st-dev/magic@latest --version", "docs": "echo 'See docs/ folder for setup guides'"}, "keywords": ["ios", "<PERSON><PERSON>", "development", "magic-mcp", "ui-generation", "vibefi"], "author": "MAGESH DHANASEKARAN", "license": "MIT", "devDependencies": {"@21st-dev/magic": "latest", "@21st-dev/cli": "latest"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/vibefi.git"}, "bugs": {"url": "https://github.com/your-username/vibefi/issues"}, "homepage": "https://github.com/your-username/vibefi#readme"}