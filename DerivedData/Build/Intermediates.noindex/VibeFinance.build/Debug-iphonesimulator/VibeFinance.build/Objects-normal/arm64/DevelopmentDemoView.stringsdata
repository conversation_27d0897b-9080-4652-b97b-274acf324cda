{"source": "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift", "tables": {"Localizable": [{"comment": "", "key": "Development Tools", "location": {"startingColumn": 30, "startingLine": 136}}, {"comment": "", "key": "🚀", "location": {"startingColumn": 30, "startingLine": 21}}, {"comment": "", "key": "Enhanced Development", "location": {"startingColumn": 30, "startingLine": 24}}, {"comment": "", "key": "Supercharged development tools for VibeFinance", "location": {"startingColumn": 30, "startingLine": 28}}, {"comment": "", "key": "Development Status", "location": {"startingColumn": 34, "startingLine": 62}}, {"comment": "", "key": "Quick Actions", "location": {"startingColumn": 30, "startingLine": 99}}, {"comment": "", "key": "Learn More", "location": {"startingColumn": 26, "startingLine": 193}}, {"comment": "", "key": "<PERSON><PERSON> Authentication", "location": {"startingColumn": 30, "startingLine": 248}}, {"comment": "", "key": "Mock Authentication allows you to bypass real authentication during development, making it faster to test features and iterate on your code.", "location": {"startingColumn": 26, "startingLine": 224}}, {"comment": "", "key": "Features:", "location": {"startingColumn": 26, "startingLine": 226}}, {"comment": "", "key": "Available Mock Users:", "location": {"startingColumn": 26, "startingLine": 236}}, {"comment": "", "key": "• Instant login without network calls", "location": {"startingColumn": 30, "startingLine": 230}}, {"comment": "", "key": "• Multiple test user profiles", "location": {"startingColumn": 30, "startingLine": 231}}, {"comment": "", "key": "• Persistent settings between launches", "location": {"startingColumn": 30, "startingLine": 232}}, {"comment": "", "key": "• Easy toggle on/off", "location": {"startingColumn": 30, "startingLine": 233}}, {"comment": "", "key": "• Dev User (<EMAIL>)", "location": {"startingColumn": 30, "startingLine": 240}}, {"comment": "", "key": "• Test User (<EMAIL>)", "location": {"startingColumn": 30, "startingLine": 241}}, {"comment": "", "key": "• Demo User (<EMAIL>)", "location": {"startingColumn": 30, "startingLine": 242}}, {"comment": "", "key": "• Investor Pro (<EMAIL>)", "location": {"startingColumn": 30, "startingLine": 243}}, {"comment": "", "key": "Done", "location": {"startingColumn": 28, "startingLine": 252}}, {"comment": "", "key": "Magic MCP", "location": {"startingColumn": 30, "startingLine": 294}}, {"comment": "", "key": "Magic MCP (Model Context Protocol) enables AI-powered UI component generation directly in your IDE using natural language descriptions.", "location": {"startingColumn": 26, "startingLine": 266}}, {"comment": "", "key": "How to Use:", "location": {"startingColumn": 26, "startingLine": 268}}, {"comment": "", "key": "Example Prompts:", "location": {"startingColumn": 26, "startingLine": 278}}, {"comment": "", "key": "Setup Required:", "location": {"startingColumn": 26, "startingLine": 287}}, {"comment": "", "key": "Run ./scripts/setup-magic-mcp.sh to configure Magic MCP with your API key.", "location": {"startingColumn": 26, "startingLine": 290}}, {"comment": "", "key": "1. <PERSON> Cursor Composer (Cmd+I)", "location": {"startingColumn": 30, "startingLine": 272}}, {"comment": "", "key": "2. Type '/ui' followed by your description", "location": {"startingColumn": 30, "startingLine": 273}}, {"comment": "", "key": "3. Magic generates SwiftUI components", "location": {"startingColumn": 30, "startingLine": 274}}, {"comment": "", "key": "4. Components are added to your project", "location": {"startingColumn": 30, "startingLine": 275}}, {"comment": "", "key": "• /ui create a SwiftUI portfolio card", "location": {"startingColumn": 30, "startingLine": 282}}, {"comment": "", "key": "• /ui design a quest completion animation", "location": {"startingColumn": 30, "startingLine": 283}}, {"comment": "", "key": "• /ui build an investment simulator interface", "location": {"startingColumn": 30, "startingLine": 284}}, {"comment": "", "key": "Done", "location": {"startingColumn": 28, "startingLine": 298}}]}, "version": 1}