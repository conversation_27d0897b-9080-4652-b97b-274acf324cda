{"source": "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift", "tables": {"Localizable": [{"comment": "", "key": "Portfolio Summary", "location": {"startingColumn": 22, "startingLine": 47}}, {"comment": "", "key": "Performance Chart", "location": {"startingColumn": 22, "startingLine": 101}}, {"comment": "", "key": "$%@", "location": {"startingColumn": 30, "startingLine": 107}}, {"comment": "", "key": "%@%@%%", "location": {"startingColumn": 30, "startingLine": 149}}, {"comment": "", "key": "Key Metrics", "location": {"startingColumn": 18, "startingLine": 248}}, {"comment": "", "key": "Quick Insights", "location": {"startingColumn": 18, "startingLine": 301}}, {"comment": "", "key": "Benchmark Comparison", "location": {"startingColumn": 22, "startingLine": 395}}, {"comment": "", "key": "Alpha: %@%%", "location": {"startingColumn": 22, "startingLine": 428}}, {"comment": "", "key": "Best Match: %@", "location": {"startingColumn": 22, "startingLine": 432}}, {"comment": "", "key": "%@%@%%", "location": {"startingColumn": 18, "startingLine": 460}}, {"comment": "", "key": "Risk Overview", "location": {"startingColumn": 22, "startingLine": 480}}, {"comment": "", "key": "%@", "location": {"startingColumn": 22, "startingLine": 484}}, {"comment": "", "key": "Risk Level", "location": {"startingColumn": 26, "startingLine": 499}}, {"comment": "", "key": "Risk Metrics", "location": {"startingColumn": 18, "startingLine": 533}}, {"comment": "", "key": "Sector Allocation", "location": {"startingColumn": 18, "startingLine": 614}}, {"comment": "", "key": "Diversification Score", "location": {"startingColumn": 22, "startingLine": 627}}, {"comment": "", "key": "%@/100", "location": {"startingColumn": 22, "startingLine": 631}}, {"comment": "", "key": "%@%%", "location": {"startingColumn": 22, "startingLine": 664}}, {"comment": "", "key": "Market Sentiment", "location": {"startingColumn": 22, "startingLine": 695}}, {"comment": "", "key": "Trend: %@", "location": {"startingColumn": 22, "startingLine": 736}}, {"comment": "", "key": "%lld", "location": {"startingColumn": 30, "startingLine": 726}}, {"comment": "", "key": "Score", "location": {"startingColumn": 30, "startingLine": 730}}, {"comment": "", "key": "Performance Metrics", "location": {"startingColumn": 18, "startingLine": 760}}, {"comment": "", "key": "Concentration Risk", "location": {"startingColumn": 18, "startingLine": 830}}, {"comment": "", "key": "No position data available", "location": {"startingColumn": 22, "startingLine": 872}}, {"comment": "", "key": "High concentration risk - consider diversifying", "location": {"startingColumn": 44, "startingLine": 866}}, {"comment": "", "key": "Moderate concentration - monitor position size", "location": {"startingColumn": 114, "startingLine": 866}}, {"comment": "", "key": "Good diversification", "location": {"startingColumn": 165, "startingLine": 866}}, {"comment": "", "key": "Largest Position", "location": {"startingColumn": 30, "startingLine": 838}}, {"comment": "", "key": "Portfolio Weight", "location": {"startingColumn": 34, "startingLine": 851}}, {"comment": "", "key": "%@%%", "location": {"startingColumn": 34, "startingLine": 855}}, {"comment": "", "key": "Risk Recommendations", "location": {"startingColumn": 18, "startingLine": 918}}, {"comment": "", "key": "Sector Performance", "location": {"startingColumn": 18, "startingLine": 955}}, {"comment": "", "key": "%@%@%%", "location": {"startingColumn": 30, "startingLine": 975}}, {"comment": "", "key": "Diversification Analysis", "location": {"startingColumn": 18, "startingLine": 998}}, {"comment": "", "key": "Diversification Score", "location": {"startingColumn": 26, "startingLine": 1005}}, {"comment": "", "key": "%@/100", "location": {"startingColumn": 26, "startingLine": 1009}}, {"comment": "", "key": "Most Allocated", "location": {"startingColumn": 30, "startingLine": 1021}}, {"comment": "", "key": "Least Allocated", "location": {"startingColumn": 30, "startingLine": 1031}}, {"comment": "", "key": "Sector Recommendations", "location": {"startingColumn": 18, "startingLine": 1061}}, {"comment": "", "key": "AI-Generated Insights", "location": {"startingColumn": 18, "startingLine": 1102}}, {"comment": "", "key": "AI is analyzing your portfolio...", "location": {"startingColumn": 26, "startingLine": 1113}}, {"comment": "", "key": "Recommended Actions", "location": {"startingColumn": 18, "startingLine": 1169}}, {"comment": "", "key": "Recent News", "location": {"startingColumn": 18, "startingLine": 1194}}, {"comment": "", "key": "No recent news available", "location": {"startingColumn": 22, "startingLine": 1200}}, {"comment": "", "key": "Returns Distribution", "location": {"startingColumn": 18, "startingLine": 1226}}, {"comment": "", "key": "Positive Returns", "location": {"startingColumn": 26, "startingLine": 1234}}, {"comment": "", "key": "%@%%", "location": {"startingColumn": 26, "startingLine": 1238}}, {"comment": "", "key": "Average Return", "location": {"startingColumn": 26, "startingLine": 1249}}, {"comment": "", "key": "%@%%", "location": {"startingColumn": 26, "startingLine": 1253}}]}, "version": 1}