{"source": "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift", "tables": {"Localizable": [{"comment": "", "key": "POPULAR", "location": {"startingColumn": 14, "startingLine": 88}}, {"comment": "", "key": "one-time", "location": {"startingColumn": 22, "startingLine": 106}}, {"comment": "", "key": "per month", "location": {"startingColumn": 22, "startingLine": 113}}, {"comment": "", "key": "Complete Purchase", "location": {"startingColumn": 30, "startingLine": 214}}, {"comment": "", "key": "Cancel", "location": {"startingColumn": 28, "startingLine": 218}}, {"comment": "", "key": "Order Summary", "location": {"startingColumn": 18, "startingLine": 236}}, {"comment": "", "key": "%@ Plan", "location": {"startingColumn": 26, "startingLine": 243}}, {"comment": "", "key": "Yearly Discount (20%)", "location": {"startingColumn": 30, "startingLine": 254}}, {"comment": "", "key": "Promo Code (%@)", "location": {"startingColumn": 30, "startingLine": 267}}, {"comment": "", "key": "-%@", "location": {"startingColumn": 30, "startingLine": 271}}, {"comment": "", "key": "Total", "location": {"startingColumn": 26, "startingLine": 281}}, {"comment": "", "key": "Payment Method", "location": {"startingColumn": 22, "startingLine": 302}}, {"comment": "", "key": "Add New", "location": {"startingColumn": 24, "startingLine": 306}}, {"comment": "", "key": "No payment methods added", "location": {"startingColumn": 26, "startingLine": 319}}, {"comment": "", "key": "Add Payment Method", "location": {"startingColumn": 28, "startingLine": 323}}, {"comment": "", "key": "By subscribing, you agree to our Terms of Service and Privacy Policy. Your subscription will automatically renew unless cancelled at least 24 hours before the end of the current period.", "location": {"startingColumn": 18, "startingLine": 352}}, {"comment": "", "key": "Terms of Service", "location": {"startingColumn": 24, "startingLine": 358}}, {"comment": "", "key": "Privacy Policy", "location": {"startingColumn": 24, "startingLine": 364}}, {"comment": "", "key": "Subscribe Now", "location": {"startingColumn": 22, "startingLine": 385}}, {"comment": "", "key": "DEFAULT", "location": {"startingColumn": 26, "startingLine": 460}}, {"comment": "", "key": "•••• %@", "location": {"startingColumn": 30, "startingLine": 451}}, {"comment": "", "key": "Add Payment Method", "location": {"startingColumn": 30, "startingLine": 523}}, {"comment": "", "key": "Cancel", "location": {"startingColumn": 28, "startingLine": 527}}, {"comment": "", "key": "Payment Method Type", "location": {"startingColumn": 18, "startingLine": 537}}, {"comment": "", "key": "Set as default payment method", "location": {"startingColumn": 20, "startingLine": 612}}, {"comment": "", "key": "Cardholder Name", "location": {"startingColumn": 22, "startingLine": 572}}, {"comment": "", "key": "<PERSON>", "location": {"startingColumn": 27, "startingLine": 576}}, {"comment": "", "key": "Card Number", "location": {"startingColumn": 22, "startingLine": 581}}, {"comment": "", "key": "1234 5678 9012 3456", "location": {"startingColumn": 27, "startingLine": 585}}, {"comment": "", "key": "Expiry Date", "location": {"startingColumn": 26, "startingLine": 592}}, {"comment": "", "key": "MM/YY", "location": {"startingColumn": 31, "startingLine": 596}}, {"comment": "", "key": "CVV", "location": {"startingColumn": 26, "startingLine": 602}}, {"comment": "", "key": "123", "location": {"startingColumn": 31, "startingLine": 606}}, {"comment": "", "key": "Apple Pay", "location": {"startingColumn": 18, "startingLine": 623}}, {"comment": "", "key": "Use Touch ID or Face ID to pay securely with Apple Pay", "location": {"startingColumn": 18, "startingLine": 627}}, {"comment": "", "key": "Bank Name", "location": {"startingColumn": 22, "startingLine": 638}}, {"comment": "", "key": "Chase Bank", "location": {"startingColumn": 27, "startingLine": 642}}, {"comment": "", "key": "Account Number", "location": {"startingColumn": 22, "startingLine": 647}}, {"comment": "", "key": "*********", "location": {"startingColumn": 27, "startingLine": 651}}, {"comment": "", "key": "Routing Number", "location": {"startingColumn": 22, "startingLine": 657}}, {"comment": "", "key": "*********", "location": {"startingColumn": 27, "startingLine": 661}}, {"comment": "", "key": "Add Payment Method", "location": {"startingColumn": 18, "startingLine": 674}}]}, "version": 1}