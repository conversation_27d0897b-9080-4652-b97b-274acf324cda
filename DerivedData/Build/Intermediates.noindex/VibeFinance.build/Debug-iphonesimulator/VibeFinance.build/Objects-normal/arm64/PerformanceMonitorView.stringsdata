{"source": "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift", "tables": {"Localizable": [{"comment": "", "key": "Performance Monitor", "location": {"startingColumn": 30, "startingLine": 39}}, {"comment": "", "key": "Stop", "location": {"startingColumn": 43, "startingLine": 43}}, {"comment": "", "key": "Start", "location": {"startingColumn": 52, "startingLine": 43}}, {"comment": "", "key": "Export Data", "location": {"startingColumn": 32, "startingLine": 51}}, {"comment": "", "key": "Clear Statistics", "location": {"startingColumn": 32, "startingLine": 55}}, {"comment": "", "key": "Enable Debug Logging", "location": {"startingColumn": 32, "startingLine": 59}}, {"comment": "", "key": "Performance Grade", "location": {"startingColumn": 18, "startingLine": 162}}, {"comment": "", "key": "System Status", "location": {"startingColumn": 18, "startingLine": 217}}, {"comment": "", "key": "Quick Actions", "location": {"startingColumn": 18, "startingLine": 257}}, {"comment": "", "key": "<PERSON>", "location": {"startingColumn": 18, "startingLine": 349}}, {"comment": "", "key": "Image Cache", "location": {"startingColumn": 18, "startingLine": 384}}, {"comment": "", "key": "Cache Actions", "location": {"startingColumn": 18, "startingLine": 419}}, {"comment": "", "key": "Network Status", "location": {"startingColumn": 18, "startingLine": 499}}, {"comment": "", "key": "Connection", "location": {"startingColumn": 26, "startingLine": 506}}, {"comment": "", "key": "Quality", "location": {"startingColumn": 26, "startingLine": 517}}, {"comment": "", "key": "Network Actions", "location": {"startingColumn": 18, "startingLine": 571}}, {"comment": "", "key": "Memory Usage", "location": {"startingColumn": 18, "startingLine": 650}}, {"comment": "", "key": "Memory monitoring implementation", "location": {"startingColumn": 18, "startingLine": 656}}, {"comment": "", "key": "Memory Actions", "location": {"startingColumn": 18, "startingLine": 668}}, {"comment": "", "key": "The system is running low on memory. Consider closing other apps or clearing caches.", "location": {"startingColumn": 18, "startingLine": 712}}, {"comment": "", "key": "Memory Warning Active", "location": {"startingColumn": 22, "startingLine": 706}}]}, "version": 1}