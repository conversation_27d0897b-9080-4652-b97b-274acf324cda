{"source": "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift", "tables": {"Localizable": [{"comment": "", "key": "Developer Settings", "location": {"startingColumn": 30, "startingLine": 133}}, {"comment": "", "key": "Authentication", "location": {"startingColumn": 25, "startingLine": 18}}, {"comment": "", "key": "Current State", "location": {"startingColumn": 25, "startingLine": 64}}, {"comment": "", "key": "Actions", "location": {"startingColumn": 25, "startingLine": 96}}, {"comment": "", "key": "", "location": {"startingColumn": 32, "startingLine": 28}}, {"comment": "", "key": "<PERSON><PERSON> Authentication", "location": {"startingColumn": 34, "startingLine": 21}}, {"comment": "", "key": "Skip real authentication during development", "location": {"startingColumn": 34, "startingLine": 23}}, {"comment": "", "key": "<PERSON>gin", "location": {"startingColumn": 38, "startingLine": 41}}, {"comment": "", "key": "Instant Login (Dev User)", "location": {"startingColumn": 38, "startingLine": 56}}, {"comment": "", "key": "Development Mode", "location": {"startingColumn": 30, "startingLine": 66}}, {"comment": "", "key": "Enabled", "location": {"startingColumn": 62, "startingLine": 68}}, {"comment": "", "key": "Disabled", "location": {"startingColumn": 74, "startingLine": 68}}, {"comment": "", "key": "Authentication Status", "location": {"startingColumn": 30, "startingLine": 73}}, {"comment": "", "key": "Authenticated", "location": {"startingColumn": 60, "startingLine": 75}}, {"comment": "", "key": "Not Authenticated", "location": {"startingColumn": 78, "startingLine": 75}}, {"comment": "", "key": "Current User", "location": {"startingColumn": 34, "startingLine": 81}}, {"comment": "", "key": "Email: %@", "location": {"startingColumn": 34, "startingLine": 83}}, {"comment": "", "key": "Username: %@", "location": {"startingColumn": 34, "startingLine": 86}}, {"comment": "", "key": "ID: %@", "location": {"startingColumn": 34, "startingLine": 89}}, {"comment": "", "key": "Sign Out", "location": {"startingColumn": 38, "startingLine": 106}}, {"comment": "", "key": "Reset All Settings", "location": {"startingColumn": 34, "startingLine": 127}}, {"comment": "", "key": "Done", "location": {"startingColumn": 28, "startingLine": 137}}, {"comment": "", "key": "Select Mock User", "location": {"startingColumn": 30, "startingLine": 179}}, {"comment": "", "key": "Cancel", "location": {"startingColumn": 28, "startingLine": 183}}]}, "version": 1}