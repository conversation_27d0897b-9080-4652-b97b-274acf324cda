{"source": "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift", "tables": {"Localizable": [{"comment": "", "key": "Testing", "location": {"startingColumn": 30, "startingLine": 183}}, {"comment": "", "key": "🧪 WealthVibe API Testing", "location": {"startingColumn": 30, "startingLine": 24}}, {"comment": "", "key": "Test all your API connections and content generation", "location": {"startingColumn": 30, "startingLine": 28}}, {"comment": "", "key": "🔌 API Connections", "location": {"startingColumn": 30, "startingLine": 36}}, {"comment": "", "key": "Testing APIs...", "location": {"startingColumn": 54, "startingLine": 43}}, {"comment": "", "key": "Test All APIs", "location": {"startingColumn": 74, "startingLine": 43}}, {"comment": "", "key": "🔐 Authentication & Database", "location": {"startingColumn": 30, "startingLine": 73}}, {"comment": "", "key": "Testing Auth...", "location": {"startingColumn": 54, "startingLine": 80}}, {"comment": "", "key": "Test Authentication Flow", "location": {"startingColumn": 74, "startingLine": 80}}, {"comment": "", "key": "🤖 Content Generation", "location": {"startingColumn": 30, "startingLine": 110}}, {"comment": "", "key": "Generating Content...", "location": {"startingColumn": 57, "startingLine": 117}}, {"comment": "", "key": "Test Content Generation", "location": {"startingColumn": 83, "startingLine": 117}}, {"comment": "", "key": "⚡ Quick Actions", "location": {"startingColumn": 30, "startingLine": 147}}, {"comment": "", "key": "📈 Test Market Data", "location": {"startingColumn": 36, "startingLine": 152}}, {"comment": "", "key": "📰 Test News", "location": {"startingColumn": 36, "startingLine": 157}}, {"comment": "", "key": "🎯 Test Quest Generation", "location": {"startingColumn": 36, "startingLine": 164}}, {"comment": "", "key": "💬 Test AI Chat", "location": {"startingColumn": 36, "startingLine": 169}}]}, "version": 1}