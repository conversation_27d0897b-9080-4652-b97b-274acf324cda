{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "328d9e880d321015876a82f6be8a672bcd43c4c5bf92c38e6a455c8649ddbfb5", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "328d9e880d321015876a82f6be8a672b8a87e839cd2d2d9fb3dae8fb321ce5bc", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b26f659ad03e6acc9edec982cd6d4d7ed", "path": "mcp.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b6233c5eb513cc2dfe3568d05e6e0cc2e", "path": "mcp-with-keys.json", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bce1fe9cd6817c8a774a6974bfe5e2b23", "name": ".cursor", "path": ".cursor", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9958931c4310e2fa43f0407d256ce389", "path": "DevelopmentConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8232d6ec6412d3ea88b4cc9d78188168", "name": "Config", "path": "Config", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b8fda2653c3935cb6d0b15c89c44a9734", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672b9473a4578678307c735e64e04a36883e", "path": "fix_auth_issues.sql", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672bcad661fc90cf92ae9d882de903cd7f8b", "path": "setup.sql", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bc8612aeb5b83651e3e67e24353d15922", "name": "Database", "path": "Database", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bf9cfbc263dbd63618589e6342fd7bf75", "path": "ADVANCED_ANALYTICS_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b5a48dee5ef4e3e6a9701f4e1de0bd8f4", "path": "API_INTEGRATIONS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b22f0ed44656206c81c9a6dbca19f0ecc", "path": "APPLE_INTELLIGENCE_REVAMP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b5e74b555e21a65d252031a78de03c19c", "path": "BUILD_SUCCESS_FINAL.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b2d3756484dbb19d9524c9bbc28f019ea", "path": "DEVELOPMENT_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b1c49ed493ecb4c22244f9a7d0dc27771", "path": "FEATURES_OVERVIEW.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672be7138c9a3211f4416d4284fbedad1f6e", "path": "FINAL_IMPLEMENTATION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bc6b5421fdc543a0f28be6eb5cf3a8d23", "path": "IMPLEMENTATION_COMPLETE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bc6249c46d6a87d31db025b846c552b79", "path": "MAGIC_MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b63b2e6a08742c41ddc919d80f309e6ac", "path": "MCP_QUICK_REFERENCE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bc15af9103579b5a1637ef6869d5d0e8d", "path": "MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672ba05e75d23666dc22bfadf271e53d282d", "path": "MOCK_AUTH_GUIDE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b11bf944c9435e73d1df668588dab02f5", "path": "PAYMENT_PROCESSING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b136f27ff26e5bc377f317489abbe5a94", "path": "PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b058cafea72c27bec5d3f3816a0713b89", "path": "PROJECT_STATUS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b59079c88bef256b2d99348f219ec41cc", "path": "REAL_TRADING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b449d7db0c01459e1aab1b29ef430c2a9", "path": "SOLUTION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b99aa0e64bc1b77911fdb6f6d4a6442c2", "path": "TECHNICAL_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bd3db7cda651a23d5eef523453a5195b1", "path": "USER_GUIDE.md", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b4d03ca50f7de7a34637d7ac0cd42443a", "name": "docs", "path": "docs", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672bc28ea38c96b0afe8b525af7690e05cce", "name": "Intents", "path": "Intents", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be12c6e5c9d011a6a2f0f473969e81521", "path": "AnalyticsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4589b147e84e5a0a1e706e10a7b4cb1d", "path": "AuthManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b10498771646e1905a12167e6dbe69f06", "path": "CacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b31a8581f2831468f948d5e93ad5bd4fd", "path": "ChatManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6028c0fdf3ede613a055ed6ea6aecdd4", "path": "FeedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bda74af87e5b4249e2aca8125ea32cfbd", "path": "ImageCacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b81a2cfa5084df157bf72293270844ce4", "path": "NetworkOptimizer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bef833f79e8e127f98fb7155dfea0e0fa", "path": "NotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b75d5ab85ae440ae0609069891cb7f1bb", "path": "PaymentManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b20060bcf4d46df015f4ea9476b7df400", "path": "PerformanceManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b45074b20ce89c2640ed54cd7ea438934", "path": "QuestManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5af9ad617d2b263d415e82299bb4d043", "path": "RealTradingManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba7ab48dd3d69478b52480ffd4a5dcd59", "path": "SimulatorManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672baae16d6768b2ffac9f174eb788fccaec", "path": "SquadManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b376a0b2dc992c5229654010f5c9af251", "path": "SubscriptionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc144ac02bcde59edb65745c496cfafc8", "path": "UserManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8e4d867f9eb36ad6e81808418a6b56a0", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b14835ac615962af9c45434cd5d93b696", "path": "AnalyticsModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd6461b99ccd22f01f665ba15139f5f94", "path": "Chat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf1cec2df1348243fa51484984f5b24dd", "path": "DataModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6cc26f63ee7bb9d7a1d86143172c07ee", "path": "Feed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba42655740b30fbff5be88d276a063727", "path": "PaymentModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc5a7a38f8f9c338b72cd6599ac0f72fc", "path": "<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9d6119a3379ce08479c5ef9aa3c7cc9f", "path": "RealTradingModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8ba5129e222d06033c2c1e9137e2c4e6", "path": "Simulator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4918b9b4287b07a5b540c659aa891bde", "path": "Squad.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b783a8d83f0fcaaf1755b673fda549f0e", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b839f9465dbadd5c298d099898d299433", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672b05b47ba6dbb489875a361dfc6705fd96", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bfd3cf229ae9fa088d524e27fcf614cb0", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672b13bf72536285d3b2b07ac68bf9c0ef2f", "path": "setup-elevenlabs-mcp.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672bdba03fd8f9648ca4a4060c1670122f9e", "path": "setup-magic-mcp.sh", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bbd8981e3dc472a96ea91b10e7bec8594", "name": "scripts", "path": "scripts", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba1e3f1a2d9743c690d2749d816e28dda", "path": "AlpacaService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bca0785f77704cc30404f1c820ec224bd", "path": "APIConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b920ed603917002ce51901b51b97fee07", "path": "APITester.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb9ec3cf73012a2f393ad0d46a394bc63", "path": "APITestService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b500644be6d946ae9167363480f5fe5a8", "path": "AuthTestHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b445d69e321a930dd9ebae659563e9e02", "path": "GeminiAIService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b087bfc1041f5635a367bd49895704f1f", "path": "MarketService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd83107646be7ae251357d7fd90d568cb", "path": "NewsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9c0d378627640e4861d54feb3bb43713", "path": "PolygonStockService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b304af594bb64b9ff16290d5f27595faa", "path": "SupabaseService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672ba02e3ea04ddcd4ba0b93cdec283fd93b", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b21364e18aa7a7a737197a62b84b8015a", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3da4b515e523bdd4306b14ee58b752de", "path": "AnalyticsComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b35fc1acf5c113183a362dc1c9a6c65f3", "path": "ChatComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcd75e7f665bc0cf6bf74e0bad674179d", "path": "FeedComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b750679f4820c3ae7295568cdb81b0025", "path": "OptimizedAsyncImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b032c4825fc0e669aba4c7bd4c2511dba", "path": "PaymentComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2ffb6c16bee4372a5f9c4093508c4872", "path": "PortfolioComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb9f47e7fc68f23177d05c5556fcca063", "path": "QuestComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be6995aca0e3bca19c81bc0005f5f978b", "path": "RealTradingComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbb686fbd0177e651d575a324ef9e5d23", "path": "SimulatorComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb9a8f5591b7f61933b98414c4f205974", "path": "SquadComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1fac8d99a143aa9880c90575b3aa154e", "path": "UIPolishComponents.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b747aedb04761bcca8e6939976eab3377", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb7d739dc6a48e7c35818af238b0f7711", "path": "AdvancedAnalyticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b24eb64202c7103db9eed409d4ca99a55", "path": "AuthView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba44fc3a17d4b120dddee5299bb91abf5", "path": "BillingHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b94080831d0c202dd16bd941a0f5db071", "path": "BrokerageConnectionSheet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8692a7c394e3e2b6506c6f16213ba3d3", "path": "BuffettChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2658259b0cf79975d9fb191d3f14e617", "path": "BuffettInspiredFeedView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf4b2eac4c0262749b38ff05751b255c6", "path": "BuffettQuestsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1b8fa6840ef59bca971744e8b83a3de5", "path": "BuffettSimulatorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba60cf33f8523687de032af1353ffbd6b", "path": "CreateInvestmentProposalView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bec199670139d82d2adad7b9d02f2203d", "path": "CreateSquadView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b53924192785c770f859357d3157c9c7f", "path": "DashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bafdb6cc70b96e944071b737b9e2f89bb", "path": "DeveloperSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b88a53b7b63128f8543bdcf95821494c6", "path": "DevelopmentDemoView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b56614514b21486338b4b11e435795b0f", "path": "EnhancedOnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4bdd992e9efb16a82add308a9eb8266a", "path": "EnhancedSubscriptionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b924d48ed7f147a44b6b44bf8cf3ffbac", "path": "FeedFiltersView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcfab8625957ba80d20a45d1fb757f92c", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b279b2ac1732ef78f6714c64e2e81839a", "path": "OnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b41b01252b4bc266a17946ff62b48ec9c", "path": "PerformanceMonitorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5a41c5e0a040b9ac39d856bbb058d52f", "path": "PreferencesSetupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3ace38a6e4149f2fdae19ab380715a87", "path": "QuestCompletionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2c438b2d7349a19915c038ca3a09d1a2", "path": "QuestDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b76182a111bb4d5bd815ec511242e3520", "path": "RealTradingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b66c77dad50475e994823534900907c01", "path": "RevenueDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b754a02ceaed982d7521aec4de4a4ab28", "path": "SimulatorTradeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b796c84013fb056b4bb7b419920a36a39", "path": "SimulatorViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7cb099b4258138ed97a5462aeebad6c7", "path": "SquadChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcf4c56f17800b45ffd84664e9c8244b2", "path": "SubscriptionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd0511197360a48efc3e452f8f1a9a225", "path": "TestingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bb2532fb8595e657bc7e3749bd1766201", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672be56e55f36b36523788f9025df8f9329b", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b47237e40b562ce9c95e7c347c5b3629d", "path": "BuildTest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1b83b1a1d875561ea9a52315727621a8", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "328d9e880d321015876a82f6be8a672be95fa7e6542483c192f432b0b8c8f717", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672bf7a856402e83b64c4cc73272a3be966b", "path": "package.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672ba860ecbda0452c329041529c190641e4", "path": "README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "328d9e880d321015876a82f6be8a672b494d38664a4c572c7eb62c16adbc7300", "path": "VibeFinance.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6a882200ae06ba0cb8183c5c92116dc4", "path": "VibeFinanceApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8d1c7c8a1e011d37bedab41300e35de6", "name": "VibeFinance", "path": "VibeFinance", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6854d05122b0c43976cd9a8d6dffa5c1", "path": "VibeFinanceTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b51c43799e3887e54f271bfadcd92f5c4", "name": "VibeFinanceTests", "path": "VibeFinanceTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4b65f8713da0db53b99ce8feefe3e7fe", "path": "VibeFinanceUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcb8fa39356c60fa5fe8399043dc9dfac", "path": "VibeFinanceUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b95439235a1d8a36bf0ce4ee32dd35c1b", "name": "VibeFinanceUITests", "path": "VibeFinanceUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b069c8f2583608d3cd9f06f1eea255401", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "328d9e880d321015876a82f6be8a672b30db233506c5381ba2ddc9729ffd1212", "name": "VibeFinance", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "328d9e880d321015876a82f6be8a672b", "path": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/VibeFinance", "targets": ["TARGET@v11_hash=63da9feee4d420a68129134477a5910e", "TARGET@v11_hash=0b41525b84f4a31fe968b7753621a3e2", "TARGET@v11_hash=b1c0a683e04e62b3151f00e1456fc096"]}