{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "328d9e880d321015876a82f6be8a672bcd43c4c5bf92c38e6a455c8649ddbfb5", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "328d9e880d321015876a82f6be8a672b8a87e839cd2d2d9fb3dae8fb321ce5bc", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b943b5fe3d05ee62d525abf39bfe8757a", "path": "mcp.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672bef8c5eb7fed14ea17f3770556d991a13", "path": "mcp-with-keys.json", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b317562dd4c3d65e52e3c0a93e5e1bea9", "name": ".cursor", "path": ".cursor", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2f1b6139d8d07d524fd6ad9f816bef44", "path": "DevelopmentConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b9792e0cb3961356837010972c4fdc897", "name": "Config", "path": "Config", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b83312dfc1c90a40ffe7fc691290ef374", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672b4a26f2732cb8c8317947202478ca6977", "path": "fix_auth_issues.sql", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672bbdb29bb5915d409e750f169c5da6077b", "path": "setup.sql", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b9466ef1215b1856a2d31c5bad60bac18", "name": "Database", "path": "Database", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b3e24825971c1483aa32fe316366b8431", "path": "ADVANCED_ANALYTICS_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b0001a1ecc341d78a66e5d434e23bcdf4", "path": "API_INTEGRATIONS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b7a14d9e01fb9ee2bd4abe62c2cfe34c0", "path": "APPLE_INTELLIGENCE_REVAMP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b0dcf50069c6bde814b9c1721b3d77775", "path": "BUILD_SUCCESS_FINAL.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b6464b1bc0f740d46d8110e41bd700352", "path": "DEVELOPMENT_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b8c956d3681a76ffa4e10036f984608f5", "path": "FEATURES_OVERVIEW.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b768cf1c662630e4b04ddd35d96425f60", "path": "FINAL_IMPLEMENTATION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b793a7cbbcefce9fe1c0d904dc4df8b9c", "path": "IMPLEMENTATION_COMPLETE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b498744b31000770e59e1064d976159dc", "path": "MAGIC_MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b4b8088d6c976752f723b6d7a018b5585", "path": "MCP_QUICK_REFERENCE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b681a20fcd66f16327fb58e82328494dc", "path": "MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672be674a577e678d4909fc492ea13dddf5d", "path": "MOCK_AUTH_GUIDE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b14ee3ef828c784a11a54b93b2b29419c", "path": "PAYMENT_PROCESSING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b47ebd02881cdec4fa2202e9ae7788400", "path": "PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b6ad61d74472a3b84c6297c4838fef317", "path": "PROJECT_STATUS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bb4a680b8ca6ef2ec48807e205cb7f710", "path": "REAL_TRADING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b61098b7c0d636513023e9c095ca19f9d", "path": "SOLUTION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bec9ed8bb13622ba8cc0fb8f369ec7c36", "path": "TECHNICAL_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b9bce084b4d5d9119668f20234aedb5b9", "path": "USER_GUIDE.md", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b44ebd9f30ebc4b8e8be6a973039ba0cd", "name": "docs", "path": "docs", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b6b62aca2c9bb9e29df1a7cd21de97ff4", "name": "Intents", "path": "Intents", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4f1dc442ee12407f4ac603116473f267", "path": "AnalyticsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1fadb48d4c18d36244193e201bf52661", "path": "AuthManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb84fdd23e18efc6c5bc522652c909153", "path": "CacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7c0a2cfbc2fe9d0b71a6ac5d4e2606f4", "path": "ChatManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b288e96d4346d3df0da079c4e2b47b13a", "path": "FeedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b56eb56ad818e41560a6480891cb6fe6e", "path": "ImageCacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b562cd6415608d926b15122f7540f05e3", "path": "NetworkOptimizer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b40b0a3907fe07b2ebda952956e8c6ba7", "path": "NotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfdf930e302f06f45652c51a67411498d", "path": "PaymentManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7c3d043a5fee09ff288298aee5500785", "path": "PerformanceManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb01254380eb071ec33771f3ef0bcd4b5", "path": "QuestManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3a4605bfdf816966fa91f58bcfb2cc37", "path": "RealTradingManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb7553dd3c4e39fe059b1e2af5d91e7a2", "path": "SimulatorManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1ac86a670188a9e32154b1b5bcfacad7", "path": "SquadManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b130628a9cc412a8fb6253a7bb23e6b93", "path": "SubscriptionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b773c0f33b19fd5ece2bafa6d8ad42a38", "path": "UserManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bfc574af254ae78d8a58688f3bea65120", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba7550d469374d9537676c690c8d08bea", "path": "AnalyticsModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd614c7baad703df5169d7ff7bc4591bb", "path": "Chat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b14f8792b591aba3954693aa37a991a3e", "path": "DataModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8669a9f436f6d582b010c181baceaa93", "path": "Feed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b680da14e36ad6237822dab80a1db0889", "path": "PaymentModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bea14f7c6dc15c8aec1050ca1c0da48d0", "path": "<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbbfa02b17194e1af01290788fce31af3", "path": "RealTradingModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd031bd3f11b51a171d97e465ca009134", "path": "Simulator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b43f399addae9d0f8d1a514f018f424e8", "path": "Squad.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b132af95605a97b0d93556152f7dc20f7", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b1620d04d003372dd6db279350fe970e8", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672bb9cecb16db8132da48c00ff020b0ea0b", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bf76953dee81c14dd6339ae576ae6c02e", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672b9a079908df5ea85e75a5e29c4bfa0d09", "path": "setup-elevenlabs-mcp.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672bd25eb6e9d390b7d307e44845578c5176", "path": "setup-magic-mcp.sh", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bcedf082d116775f2d39f939bf3601153", "name": "scripts", "path": "scripts", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5538ea2f2b7d01c1031d0bc725ba875f", "path": "AlpacaService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc0a95f8f87dfb77795221284ab2f7df5", "path": "APIConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4d1b920e9f43372b2f1dea34d0e3c8e6", "path": "APITester.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6a223b81a5b564b075c896fc014e0a9d", "path": "APITestService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0bcc9a7ff32cb2a5262dd5d28134bb82", "path": "AuthTestHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b209892d330771541f14e4bc2940ce1fb", "path": "GeminiAIService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be76a610dcf4560e2bc108ab508d9e6c0", "path": "MarketService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b79eb3f30494fd6096787da3c96f71528", "path": "NewsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b21b3d1d7e8fe9f3a7dc8dbf21d8418fa", "path": "PolygonStockService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6b0c3eba198a72a6b45b3be7c2dced85", "path": "SupabaseService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8520043f979775633a675a6e7bbee974", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672bb07f107443de201de133eb9f30d4abcf", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcb591b75380c0e8a679c5b31ad10dcbd", "path": "AnalyticsComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7664faa89d019e3630a01458e01d6755", "path": "ChatComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b68feb1950a0c9edcafd13dbcff572d45", "path": "FeedComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3807ecfe364457e692baacdcb1c55103", "path": "OptimizedAsyncImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be5405e61dcc8caebda91b5be8984a767", "path": "PaymentComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b44ad16e3be106669e1e798abf15c3947", "path": "PortfolioComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbd4b09317561c5aa2e4f5b11b16a91f8", "path": "QuestComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b42604bccb8f46c8781bd23676cfaf233", "path": "RealTradingComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be9ef172bb67a763ddd26503d18d1a468", "path": "SimulatorComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2322bfb57a00dd51dff9c8757b2de839", "path": "SquadComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba85864d4589a357c299af1333e4d8922", "path": "UIPolishComponents.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b6a6b82d00f1e84c4895e977481bab779", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bac55260d3f3d3e4b8ab1ac8b92fb26f8", "path": "AdvancedAnalyticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfa15283d38fb2d8c527c24ec09d742e8", "path": "AuthView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b418b74d354d8780eec111dc943b10603", "path": "BillingHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3273cdf7d8f2e74f4a7b5ac5ab6a7f3c", "path": "BrokerageConnectionSheet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf65b5fa561af765e7d3bef50a0757de8", "path": "BuffettChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7803bf423e72d84443dafa404fe1e859", "path": "BuffettInspiredFeedView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4ded0381d5d2930694a9d89c40f4d4a6", "path": "BuffettQuestsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be43f6c44074339efa5e0e0561debfedd", "path": "BuffettSimulatorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf88bad537f04ddd886378a4d7df46234", "path": "CreateInvestmentProposalView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba1ddd5094d89c3f705425b3ca5e5ad71", "path": "CreateSquadView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7226c3dc967b7707ad91ed433641d3bf", "path": "DashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5bbc4ff8a8e4408c37669d4589593192", "path": "DeveloperSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b470eb649445c3e74acc471013028d367", "path": "DevelopmentDemoView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b062ed9ffde594b05ae5377a3ffa06e69", "path": "EnhancedOnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b79eb869b29cfbaf35f355e7d3e7937cb", "path": "EnhancedSubscriptionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bde6e1e97d902c864f3856d85e57f89c2", "path": "FeedFiltersView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba119707af0bdd06b3cbede38a80da8e4", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfc77928018cd8d09cb4d8cc4e5f372eb", "path": "OnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b21ccd33d773190a752ed5e1404f6f807", "path": "PerformanceMonitorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5cbf5160abc9820b707bea2f761c0ddb", "path": "PreferencesSetupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc877734ef11b6c1f8bca8f94c6240420", "path": "QuestCompletionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfd8e88736e101809d1bb747a43700af4", "path": "QuestDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc84298eae40344f7d53d4be44bdc3b9f", "path": "RealTradingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba99dc7a64bac34eb91e1933b170ae40e", "path": "RevenueDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b285bab61c04edfa067705314cbd6e4d8", "path": "SimulatorTradeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b64339ea5c9b4cf2cf70c8b60bb515a14", "path": "SimulatorViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb016a4b4711b1582c36350ce257e473d", "path": "SquadChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6bd1864aef646e9d3eddc4e1615d9d9b", "path": "SubscriptionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b740eb9ef19c569e45a2dfba1418181ae", "path": "TestingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b5a83d35902cef38a6c3cfd229db112b2", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672b6fd41236aa5462cdad14c177fcd7b8d4", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b527aaa7fce24447d91c1de83014e91b0", "path": "BuildTest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b074925ccbf1b3548032369dc1915e551", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "328d9e880d321015876a82f6be8a672b7beb2770e7b3c0d51268d7f098e6db25", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b98784eaf43cf6cc30aa5768809a2e753", "path": "package.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672ba32723edec73442abcd2451d24710061", "path": "README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "328d9e880d321015876a82f6be8a672b06e47354ec035841da0ec31f6d8dee24", "path": "VibeFinance.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7a1ca71f8be9083822b6e17f70fcb083", "path": "VibeFinanceApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8d1c7c8a1e011d37bedab41300e35de6", "name": "VibeFinance", "path": "VibeFinance", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b544fb871912754ce5a8ca2e4fda0e3f1", "path": "VibeFinanceTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b51c43799e3887e54f271bfadcd92f5c4", "name": "VibeFinanceTests", "path": "VibeFinanceTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3fe23ec00a2748bc634e594231582e41", "path": "VibeFinanceUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb6dfed67d4adc6c9e0073285c6f5f69b", "path": "VibeFinanceUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b95439235a1d8a36bf0ce4ee32dd35c1b", "name": "VibeFinanceUITests", "path": "VibeFinanceUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b069c8f2583608d3cd9f06f1eea255401", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "328d9e880d321015876a82f6be8a672b30db233506c5381ba2ddc9729ffd1212", "name": "VibeFinance", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "328d9e880d321015876a82f6be8a672b", "path": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/VibeFinance", "targets": ["TARGET@v11_hash=f1aa9d41751a8c8f4d8360fcc3d92084", "TARGET@v11_hash=7c2a6823073ba95d33fa7f72af17af9c", "TARGET@v11_hash=1f2602c0ac3cfc9cd2af9a8a704e76c1"]}