{"buildConfigurations": [{"buildSettings": {"BUNDLE_LOADER": "$(TEST_HOST)", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.md.VibeFinanceTests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_HOST": "$(BUILT_PRODUCTS_DIR)/VibeFinance.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeFinance"}, "guid": "328d9e880d321015876a82f6be8a672b58bdd1c0b7defc65c4028abe6a32d777", "name": "Debug"}, {"buildSettings": {"BUNDLE_LOADER": "$(TEST_HOST)", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.md.VibeFinanceTests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_HOST": "$(BUILT_PRODUCTS_DIR)/VibeFinance.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VibeFinance"}, "guid": "328d9e880d321015876a82f6be8a672b9b401bd1088c8ed63b0b8c5417a89a18", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "328d9e880d321015876a82f6be8a672b6854d05122b0c43976cd9a8d6dffa5c1", "guid": "328d9e880d321015876a82f6be8a672b0ff751d6787e48598c8f2d55ddddbe8f"}], "guid": "328d9e880d321015876a82f6be8a672b9374e79c8fb53c0eff54bdaeaf84ac44", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "328d9e880d321015876a82f6be8a672b63b31d925009534a78c3ac58b780b0cc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "328d9e880d321015876a82f6be8a672bb31f5565b946fab6e300490cc00f3a2a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "328d9e880d321015876a82f6be8a672bc87c97ee76012153448311260fe6f91f", "name": "VibeFinance"}], "guid": "328d9e880d321015876a82f6be8a672b829943d3e06cf8b3e0c6add622a0fad4", "name": "VibeFinanceTests", "performanceTestsBaselinesPath": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj/xcshareddata/xcbaselines/7CDCD18E2DE21DA600693D4C.xcbaseline", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "328d9e880d321015876a82f6be8a672b8cac2587dc9cd9b124de32c398651d6e", "name": "VibeFinanceTests.xctest", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle.unit-test", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}