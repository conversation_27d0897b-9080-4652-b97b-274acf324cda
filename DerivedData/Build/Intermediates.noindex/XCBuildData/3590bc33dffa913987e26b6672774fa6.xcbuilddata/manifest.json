{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app": {"is-mutated": true}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance": {"is-mutated": true}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib": {"is-mutated": true}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/_CodeSignature", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib", "/Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib>", "<target-VibeFinance-****************************************************************--begin-scanning>", "<target-VibeFinance-****************************************************************--end>", "<target-VibeFinance-****************************************************************--linker-inputs-ready>", "<target-VibeFinance-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-VibeFinance-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/ssu/root.ssu.yaml", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/_CodeSignature", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/ADVANCED_ANALYTICS_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/API_INTEGRATIONS.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/APPLE_INTELLIGENCE_REVAMP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/BUILD_SUCCESS_FINAL.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/DEVELOPMENT_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FEATURES_OVERVIEW.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FINAL_IMPLEMENTATION_SUMMARY.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/IMPLEMENTATION_COMPLETE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MAGIC_MCP_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_QUICK_REFERENCE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MOCK_AUTH_GUIDE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PAYMENT_PROCESSING_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PROJECT_STATUS.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/README.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/REAL_TRADING_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/SOLUTION_SUMMARY.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/TECHNICAL_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/USER_GUIDE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/fix_auth_issues.sql", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp-with-keys.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/package.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-elevenlabs-mcp.sh", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-magic-mcp.sh", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup.sql", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Assets.car", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_signature", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PkgInfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_lto.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.hmap"], "roots": ["/tmp/VibeFinance.dst", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products"], "outputs": ["<target-VibeFinance-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance-328d9e880d321015876a82f6be8a672b-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.5-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache", "<ClangStatCache /Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-o", "/Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj", "signature": "63ad8172f07cafaee0a076cdd3caf745"}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance-328d9e880d321015876a82f6be8a672b-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-VibeFinance-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Metadata.appintents>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList"], "outputs": ["<target-VibeFinance-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-ChangePermissions>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-StripSymbols>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib>", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-GenerateStubAPI>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ProductPostprocessingTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-CodeSign>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-Validate>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-CopyAside>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-VibeFinance-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "outputs": ["<target-VibeFinance-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-VibeFinance-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--GeneratedFilesTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ProductStructureTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-VibeFinance-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.hmap"], "outputs": ["<target-VibeFinance-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PkgInfo"], "outputs": ["<target-VibeFinance-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--RealityAssetsTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--ModuleMapTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-VibeFinance-****************************************************************--InfoPlistTaskProducer>", "<target-VibeFinance-****************************************************************--SanitizerTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-VibeFinance-****************************************************************--TestTargetTaskProducer>", "<target-VibeFinance-****************************************************************--TestHostTaskProducer>", "<target-VibeFinance-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-VibeFinance-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-VibeFinance-****************************************************************--DocumentationTaskProducer>", "<target-VibeFinance-****************************************************************--CustomTaskProducer>", "<target-VibeFinance-****************************************************************--StubBinaryTaskProducer>", "<target-VibeFinance-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--start>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "outputs": ["<target-VibeFinance-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--HeadermapTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "outputs": ["<target-VibeFinance-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ProductPostprocessingTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-VibeFinance-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-VibeFinance-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/ADVANCED_ANALYTICS_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/API_INTEGRATIONS.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/APPLE_INTELLIGENCE_REVAMP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/BUILD_SUCCESS_FINAL.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/DEVELOPMENT_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FEATURES_OVERVIEW.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FINAL_IMPLEMENTATION_SUMMARY.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/IMPLEMENTATION_COMPLETE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MAGIC_MCP_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_QUICK_REFERENCE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MOCK_AUTH_GUIDE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PAYMENT_PROCESSING_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PROJECT_STATUS.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/README.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/REAL_TRADING_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/SOLUTION_SUMMARY.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/TECHNICAL_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/USER_GUIDE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/fix_auth_issues.sql", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp-with-keys.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/package.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-elevenlabs-mcp.sh", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-magic-mcp.sh", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup.sql", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Assets.car", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_lto.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-VibeFinance-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-VibeFinance-****************************************************************--generated-headers>"]}, "P0:::Gate target-VibeFinance-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h"], "outputs": ["<target-VibeFinance-****************************************************************--swift-generated-headers>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Metadata.appintents>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/ssu", "--bundle-id", "com.md.VibeFinance", "--product-path", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "--extracted-metadata-path", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Metadata.appintents", "--metadata-file-list", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "signature": "7c14d5ecbc884e5a4ad3d5dc25118c1a"}, "P0:target-VibeFinance-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp-with-keys.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/fix_auth_issues.sql/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/setup.sql/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/README.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettChatView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettInspiredFeedView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettQuestsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettSimulatorView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DashboardView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/ADVANCED_ANALYTICS_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/API_INTEGRATIONS.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/APPLE_INTELLIGENCE_REVAMP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/BUILD_SUCCESS_FINAL.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/DEVELOPMENT_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FEATURES_OVERVIEW.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FINAL_IMPLEMENTATION_SUMMARY.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/IMPLEMENTATION_COMPLETE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MAGIC_MCP_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_QUICK_REFERENCE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MOCK_AUTH_GUIDE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PAYMENT_PROCESSING_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PROJECT_STATUS.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/REAL_TRADING_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/SOLUTION_SUMMARY.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/TECHNICAL_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/USER_GUIDE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/package.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-elevenlabs-mcp.sh/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-magic-mcp.sh/", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib>", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>", "<target-VibeFinance-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance normal>", "<TRIGGER: MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/_CodeSignature", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "<TRIGGER: CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp-with-keys.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/fix_auth_issues.sql/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/setup.sql/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/README.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettChatView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettInspiredFeedView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettQuestsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettSimulatorView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DashboardView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/ADVANCED_ANALYTICS_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/API_INTEGRATIONS.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/APPLE_INTELLIGENCE_REVAMP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/BUILD_SUCCESS_FINAL.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/DEVELOPMENT_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FEATURES_OVERVIEW.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FINAL_IMPLEMENTATION_SUMMARY.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/IMPLEMENTATION_COMPLETE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MAGIC_MCP_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_QUICK_REFERENCE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MOCK_AUTH_GUIDE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PAYMENT_PROCESSING_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PROJECT_STATUS.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/REAL_TRADING_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/SOLUTION_SUMMARY.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/TECHNICAL_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/USER_GUIDE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/package.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-elevenlabs-mcp.sh/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-magic-mcp.sh/", "<target-VibeFinance-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp-with-keys.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/fix_auth_issues.sql/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/setup.sql/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/README.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettChatView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettInspiredFeedView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettQuestsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettSimulatorView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DashboardView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/ADVANCED_ANALYTICS_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/API_INTEGRATIONS.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/APPLE_INTELLIGENCE_REVAMP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/BUILD_SUCCESS_FINAL.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/DEVELOPMENT_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FEATURES_OVERVIEW.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FINAL_IMPLEMENTATION_SUMMARY.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/IMPLEMENTATION_COMPLETE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MAGIC_MCP_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_QUICK_REFERENCE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_SETUP.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MOCK_AUTH_GUIDE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PAYMENT_PROCESSING_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PROJECT_STATUS.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/REAL_TRADING_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/SOLUTION_SUMMARY.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/TECHNICAL_IMPLEMENTATION.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/USER_GUIDE.md/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/package.json/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-elevenlabs-mcp.sh/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-magic-mcp.sh/", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib>", "<target-VibeFinance-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets", "--compile", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone17,3", "--filter-for-device-os-version", "18.5", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "control-enabled": false, "deps": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "0a079c65142836e9e903d6868af2985a"}, "P0:target-VibeFinance-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets", "--compile", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "control-enabled": false, "deps": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "e5139d5607d6282a5a064c8cb63a8f00"}, "P0:target-VibeFinance-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "deps": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/ADVANCED_ANALYTICS_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/ADVANCED_ANALYTICS_IMPLEMENTATION.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/ADVANCED_ANALYTICS_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/ADVANCED_ANALYTICS_IMPLEMENTATION.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/ADVANCED_ANALYTICS_IMPLEMENTATION.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/ADVANCED_ANALYTICS_IMPLEMENTATION.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/API_INTEGRATIONS.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/API_INTEGRATIONS.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/API_INTEGRATIONS.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/API_INTEGRATIONS.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/API_INTEGRATIONS.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/API_INTEGRATIONS.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/APPLE_INTELLIGENCE_REVAMP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/APPLE_INTELLIGENCE_REVAMP.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/APPLE_INTELLIGENCE_REVAMP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/APPLE_INTELLIGENCE_REVAMP.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/APPLE_INTELLIGENCE_REVAMP.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/APPLE_INTELLIGENCE_REVAMP.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/BUILD_SUCCESS_FINAL.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/BUILD_SUCCESS_FINAL.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/BUILD_SUCCESS_FINAL.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/BUILD_SUCCESS_FINAL.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/BUILD_SUCCESS_FINAL.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/BUILD_SUCCESS_FINAL.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/DEVELOPMENT_SETUP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/DEVELOPMENT_SETUP.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/DEVELOPMENT_SETUP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/DEVELOPMENT_SETUP.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/DEVELOPMENT_SETUP.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/DEVELOPMENT_SETUP.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FEATURES_OVERVIEW.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FEATURES_OVERVIEW.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FEATURES_OVERVIEW.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FEATURES_OVERVIEW.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FEATURES_OVERVIEW.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FEATURES_OVERVIEW.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FINAL_IMPLEMENTATION_SUMMARY.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FINAL_IMPLEMENTATION_SUMMARY.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FINAL_IMPLEMENTATION_SUMMARY.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FINAL_IMPLEMENTATION_SUMMARY.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/FINAL_IMPLEMENTATION_SUMMARY.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FINAL_IMPLEMENTATION_SUMMARY.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/IMPLEMENTATION_COMPLETE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/IMPLEMENTATION_COMPLETE.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/IMPLEMENTATION_COMPLETE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/IMPLEMENTATION_COMPLETE.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/IMPLEMENTATION_COMPLETE.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/IMPLEMENTATION_COMPLETE.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MAGIC_MCP_SETUP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MAGIC_MCP_SETUP.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MAGIC_MCP_SETUP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MAGIC_MCP_SETUP.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MAGIC_MCP_SETUP.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MAGIC_MCP_SETUP.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_QUICK_REFERENCE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_QUICK_REFERENCE.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_QUICK_REFERENCE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_QUICK_REFERENCE.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_QUICK_REFERENCE.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_QUICK_REFERENCE.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_SETUP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_SETUP.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_SETUP.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_SETUP.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MCP_SETUP.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_SETUP.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MOCK_AUTH_GUIDE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MOCK_AUTH_GUIDE.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MOCK_AUTH_GUIDE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MOCK_AUTH_GUIDE.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/MOCK_AUTH_GUIDE.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MOCK_AUTH_GUIDE.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PAYMENT_PROCESSING_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PAYMENT_PROCESSING_IMPLEMENTATION.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PAYMENT_PROCESSING_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PAYMENT_PROCESSING_IMPLEMENTATION.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PAYMENT_PROCESSING_IMPLEMENTATION.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PAYMENT_PROCESSING_IMPLEMENTATION.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PROJECT_STATUS.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PROJECT_STATUS.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PROJECT_STATUS.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PROJECT_STATUS.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/PROJECT_STATUS.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PROJECT_STATUS.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/README.md /Users/<USER>/Documents/VibeFinance/VibeFinance/README.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/README.md /Users/<USER>/Documents/VibeFinance/VibeFinance/README.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/README.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/README.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/REAL_TRADING_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/REAL_TRADING_IMPLEMENTATION.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/REAL_TRADING_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/REAL_TRADING_IMPLEMENTATION.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/REAL_TRADING_IMPLEMENTATION.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/REAL_TRADING_IMPLEMENTATION.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/SOLUTION_SUMMARY.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/SOLUTION_SUMMARY.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/SOLUTION_SUMMARY.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/SOLUTION_SUMMARY.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/SOLUTION_SUMMARY.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/SOLUTION_SUMMARY.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/TECHNICAL_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/TECHNICAL_IMPLEMENTATION.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/TECHNICAL_IMPLEMENTATION.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/TECHNICAL_IMPLEMENTATION.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/TECHNICAL_IMPLEMENTATION.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/TECHNICAL_IMPLEMENTATION.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/USER_GUIDE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/USER_GUIDE.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/USER_GUIDE.md /Users/<USER>/Documents/VibeFinance/VibeFinance/docs/USER_GUIDE.md", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/docs/USER_GUIDE.md/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/USER_GUIDE.md"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/fix_auth_issues.sql /Users/<USER>/Documents/VibeFinance/VibeFinance/Database/fix_auth_issues.sql": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/fix_auth_issues.sql /Users/<USER>/Documents/VibeFinance/VibeFinance/Database/fix_auth_issues.sql", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/fix_auth_issues.sql/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/fix_auth_issues.sql"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp-with-keys.json /Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp-with-keys.json": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp-with-keys.json /Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp-with-keys.json", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp-with-keys.json/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp-with-keys.json"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp.json /Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp.json": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp.json /Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp.json", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/.cursor/mcp.json/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp.json"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/package.json /Users/<USER>/Documents/VibeFinance/VibeFinance/package.json": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/package.json /Users/<USER>/Documents/VibeFinance/VibeFinance/package.json", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/package.json/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/package.json"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-elevenlabs-mcp.sh /Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-elevenlabs-mcp.sh": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-elevenlabs-mcp.sh /Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-elevenlabs-mcp.sh", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-elevenlabs-mcp.sh/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-elevenlabs-mcp.sh"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-magic-mcp.sh /Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-magic-mcp.sh": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-magic-mcp.sh /Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-magic-mcp.sh", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/scripts/setup-magic-mcp.sh/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-magic-mcp.sh"]}, "P0:target-VibeFinance-****************************************************************-:Debug:CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup.sql /Users/<USER>/Documents/VibeFinance/VibeFinance/Database/setup.sql": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup.sql /Users/<USER>/Documents/VibeFinance/VibeFinance/Database/setup.sql", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Database/setup.sql/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup.sql"]}, "P0:target-VibeFinance-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettInspiredFeedView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettChatView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettSimulatorView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DashboardView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettQuestsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "VibeFinance", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "--xcode-version", "16F6", "--platform-family", "iOS", "--deployment-target", "18.2", "--bundle-identifier", "com.md.VibeFinance", "--output", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "--target-triple", "arm64-apple-ios18.2-simulator", "--binary-file", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance", "--dependency-file", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "signature": "ff420064803c8367cfbdd44a3be5b455"}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VibeFinance.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-VibeFinance-****************************************************************--begin-compiling>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VibeFinance.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-VibeFinance-****************************************************************--begin-linking>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VibeFinance.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--begin-scanning>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--end": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--entry>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib>", "<CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/ADVANCED_ANALYTICS_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/API_INTEGRATIONS.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/APPLE_INTELLIGENCE_REVAMP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/BUILD_SUCCESS_FINAL.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/DEVELOPMENT_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FEATURES_OVERVIEW.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/FINAL_IMPLEMENTATION_SUMMARY.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/IMPLEMENTATION_COMPLETE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MAGIC_MCP_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_QUICK_REFERENCE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MCP_SETUP.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/MOCK_AUTH_GUIDE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PAYMENT_PROCESSING_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PROJECT_STATUS.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/README.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/REAL_TRADING_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/SOLUTION_SUMMARY.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/TECHNICAL_IMPLEMENTATION.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/USER_GUIDE.md", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/fix_auth_issues.sql", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp-with-keys.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/mcp.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/package.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-elevenlabs-mcp.sh", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup-magic-mcp.sh", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/setup.sql", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Metadata.appintents>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Assets.car", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PkgInfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "<Validate /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_lto.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-non-framework-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.hmap", "<target-VibeFinance-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-VibeFinance-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-VibeFinance-****************************************************************--Barrier-ChangePermissions>", "<target-VibeFinance-****************************************************************--Barrier-CodeSign>", "<target-VibeFinance-****************************************************************--Barrier-CopyAside>", "<target-VibeFinance-****************************************************************--Barrier-GenerateStubAPI>", "<target-VibeFinance-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-VibeFinance-****************************************************************--Barrier-RegisterProduct>", "<target-VibeFinance-****************************************************************--Barrier-StripSymbols>", "<target-VibeFinance-****************************************************************--Barrier-Validate>", "<target-VibeFinance-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-VibeFinance-****************************************************************--CustomTaskProducer>", "<target-VibeFinance-****************************************************************--DocumentationTaskProducer>", "<target-VibeFinance-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-VibeFinance-****************************************************************--GeneratedFilesTaskProducer>", "<target-VibeFinance-****************************************************************--HeadermapTaskProducer>", "<target-VibeFinance-****************************************************************--InfoPlistTaskProducer>", "<target-VibeFinance-****************************************************************--ModuleMapTaskProducer>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--ProductPostprocessingTaskProducer>", "<target-VibeFinance-****************************************************************--ProductStructureTaskProducer>", "<target-VibeFinance-****************************************************************--RealityAssetsTaskProducer>", "<target-VibeFinance-****************************************************************--SanitizerTaskProducer>", "<target-VibeFinance-****************************************************************--StubBinaryTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-VibeFinance-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-VibeFinance-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-VibeFinance-****************************************************************--TestHostTaskProducer>", "<target-VibeFinance-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-VibeFinance-****************************************************************--TestTargetTaskProducer>", "<target-VibeFinance-****************************************************************--copy-headers-completion>", "<target-VibeFinance-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-VibeFinance-****************************************************************--generated-headers>", "<target-VibeFinance-****************************************************************--swift-generated-headers>"], "outputs": ["<target-VibeFinance-****************************************************************--end>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VibeFinance.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-VibeFinance-****************************************************************--begin-compiling>"], "outputs": ["<target-VibeFinance-****************************************************************--entry>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/VibeFinance.dst>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-VibeFinance-****************************************************************--immediate>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_lto.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt"], "outputs": ["<target-VibeFinance-****************************************************************--linker-inputs-ready>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h"], "outputs": ["<target-VibeFinance-****************************************************************--modules-ready>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--begin-compiling>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/ssu/root.ssu.yaml", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Metadata.appintents>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Assets.car", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_signature", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_lto.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList", "<target-VibeFinance-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-VibeFinance-****************************************************************--unsigned-product-ready>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Gate target-VibeFinance-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-VibeFinance-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-VibeFinance-****************************************************************--will-sign>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets/", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets", "--compile", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--bundle-identifier", "com.md.VibeFinance", "--generate-swift-asset-symbols", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "control-enabled": false, "signature": "4ca3c678973c4c441b78cd4c88fb745d"}, "P0:target-VibeFinance-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets /Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Assets.xcassets/", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned/", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_signature", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Assets.car"], "deps": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_dependencies"}, "P0:target-VibeFinance-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-VibeFinance-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/thinned>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_output/unthinned>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "inputs": ["<target-VibeFinance-****************************************************************--start>", "<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "<MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>", "<TRIGGER: MkDir /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist /Users/<USER>/Documents/VibeFinance/VibeFinance/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist /Users/<USER>/Documents/VibeFinance/VibeFinance/Info.plist", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/assetcatalog_generated_info.plist", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/PkgInfo"]}, "P0:target-VibeFinance-****************************************************************-:Debug:ProcessProductPackaging /Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinance.entitlements /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinance.entitlements /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinance.entitlements", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist", "<target-VibeFinance-****************************************************************--ProductStructureTaskProducer>", "<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent"]}, "P0:target-VibeFinance-****************************************************************-:Debug:ProcessProductPackaging /Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinance.entitlements /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinance.entitlements /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinance.entitlements", "<target-VibeFinance-****************************************************************--ProductStructureTaskProducer>", "<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent"]}, "P0:target-VibeFinance-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "<target-VibeFinance-****************************************************************--ProductStructureTaskProducer>", "<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "-o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "signature": "b63cad866878b2337fd7a93fa7984955"}, "P0:target-VibeFinance-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent", "<target-VibeFinance-****************************************************************--ProductStructureTaskProducer>", "<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent", "-o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "signature": "19297dd97d453ccb1ac46606e4f3ee23"}, "P0:target-VibeFinance-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "<target-VibeFinance-****************************************************************--Barrier-CodeSign>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:SwiftDriver Compilation VibeFinance normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation VibeFinance normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettInspiredFeedView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettChatView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettSimulatorView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DashboardView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettQuestsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-VibeFinance-****************************************************************--generated-headers>", "<target-VibeFinance-****************************************************************--copy-headers-completion>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftconstvalues", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-VibeFinance-****************************************************************-:Debug:Touch /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app": {"tool": "shell", "description": "Touch /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "<target-VibeFinance-****************************************************************--Barrier-Validate>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "signature": "9bc2a5b969862ffed05d2c2806164bb2"}, "P0:target-VibeFinance-****************************************************************-:Debug:Validate /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/Info.plist", "<target-VibeFinance-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-VibeFinance-****************************************************************--will-sign>", "<target-VibeFinance-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"], "outputs": ["<Validate /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app>"]}, "P0:target-VibeFinance-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Preview Content", "<target-VibeFinance-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance-328d9e880d321015876a82f6be8a672b-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance-328d9e880d321015876a82f6be8a672b-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance-328d9e880d321015876a82f6be8a672b-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-VibeFinance-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-VibeFinance-****************************************************************-:Debug:Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo/", "<target-VibeFinance-****************************************************************--copy-headers-completion>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-VibeFinance-****************************************************************-:Debug:Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json/", "<target-VibeFinance-****************************************************************--copy-headers-completion>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-VibeFinance-****************************************************************-:Debug:Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc/", "<target-VibeFinance-****************************************************************--copy-headers-completion>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-VibeFinance-****************************************************************-:Debug:Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule/", "<target-VibeFinance-****************************************************************--copy-headers-completion>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-VibeFinance-****************************************************************-:Debug:Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance normal", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance", "<Linked Binary /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance>", "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "signature": "957c1fc12380f2f1a77c860c237d6836"}, "P2:target-VibeFinance-****************************************************************-:Debug:Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib normal", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettInspiredFeedView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettSimulatorView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DashboardView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuffettQuestsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "<target-VibeFinance-****************************************************************--generated-headers>", "<target-VibeFinance-****************************************************************--swift-generated-headers>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_lto.o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "-install_name", "@rpath/VibeFinance.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "-fobjc-link-runtime", "-fprofile-instr-generate", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/VibeFinance.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "deps": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat"], "deps-style": "dependency-info", "signature": "f4c50a7d40d4f1b88b50ec9a99e85ca1"}, "P2:target-VibeFinance-****************************************************************-:Debug:Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib normal", "inputs": ["<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk", "-O0", "-L/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/VibeFinance.debug.dylib", "-dead_strip", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-debug_variant", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_dependency_info.dat", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products/Debug-iphonesimulator/VibeFinance.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Documents/VibeFinance", "signature": "3e600558b1d2735167591a4c0c04628e"}, "P2:target-VibeFinance-****************************************************************-:Debug:SwiftDriver Compilation Requirements VibeFinance normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements VibeFinance normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettInspiredFeedView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettChatView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettSimulatorView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DashboardView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettQuestsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift", "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap", "<ClangStatCache /Users/<USER>/Documents/VibeFinance/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache>", "<target-VibeFinance-****************************************************************--copy-headers-completion>", "<target-VibeFinance-****************************************************************--ModuleVerifierTaskProducer>", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance Swift Compilation Requirements Finished", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftmodule", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftsourceinfo", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.abi.json", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.swiftdoc"]}, "P2:target-VibeFinance-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "inputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-Swift.h", "<target-VibeFinance-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/VibeFinance-Swift.h"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-OutputFileMap.json"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.LinkFileList"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftConstValuesFileList"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance.SwiftFileList"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance_const_extract_protocols.json"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-DebugDylibPath-normal-arm64.txt"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-non-framework-target-headers.hmap", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-non-framework-target-headers.hmap"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-all-target-headers.hmap"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-generated-files.hmap"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-own-target-headers.hmap"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance-project-headers.hmap"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyMetadataFileList"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.DependencyStaticMetadataFileList"]}, "P2:target-VibeFinance-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.hmap", "inputs": ["<target-VibeFinance-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/VibeFinance.hmap"]}}}