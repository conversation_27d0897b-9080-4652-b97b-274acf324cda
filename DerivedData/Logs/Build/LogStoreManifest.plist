<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>697F8AB3-CA3E-471F-A846-C2828F8DB54B</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>697F8AB3-CA3E-471F-A846-C2828F8DB54B.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>VibeFinance project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>VibeFinance</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>timeStartedRecording</key>
			<real>772848692.01561904</real>
			<key>timeStoppedRecording</key>
			<real>772848692.28033805</real>
			<key>title</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>uniqueIdentifier</key>
			<string>697F8AB3-CA3E-471F-A846-C2828F8DB54B</string>
		</dict>
	</dict>
</dict>
</plist>
