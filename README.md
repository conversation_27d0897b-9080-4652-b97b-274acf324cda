# WealthVibe: Vibe Finance iOS App 💎

A Gen Z-friendly iOS app that makes finance fun and approachable through gamified learning, personalized feeds, and community investing. Built with SwiftUI and powered by AI.

## 🚀 Overview

WealthVibe targets everyday users (Gen Z, Gen Alpha, millennials) globally to generate $2 million monthly revenue through $9.99 and $19.99 subscription tiers. The app combines Gemini AI Flash 2.0, Supabase, and CrewAI to deliver personalized, gamified financial experiences.

## ✨ Core Features

### 📱 Personalized Financial Feed
- Daily curated content based on user preferences
- AI-powered summaries with Gen Z-friendly language
- Investment suggestions with micro-amounts ($1-$50)
- Content from NewsAPI and X (Twitter) with citations

### 🎯 Gamified Learning Quests
- Daily challenges to teach finance basics
- XP system with levels and rewards
- Multiple categories: stocks, crypto, budgeting, etc.
- Progress tracking and streak counters

### 👥 Community Investing Squads (Pro)
- Create or join investment groups
- Collaborative decision-making through voting
- Pool money for micro-investments
- Real-time performance tracking

### 🎮 Micro-Investment Simulator (Pro)
- Practice with $10K virtual money
- Real market data from Polygon.io
- Portfolio analytics and performance tracking
- Risk-free learning environment

### 💬 Vibe Chat
- AI-powered financial assistant
- Gen Z-friendly conversational tone
- Voice and text support
- Contextual quick replies and suggestions

## 🛠 Technical Stack

### Frontend
- **Framework**: SwiftUI (iOS 17+)
- **Design**: Colorful, Gen Z-friendly UI with neon gradients
- **Features**: Native iOS experience with smooth animations

### Backend
- **Database**: Supabase for user data, feeds, squads, and quests
- **Authentication**: Supabase Auth with email/password
- **Real-time**: Supabase real-time subscriptions

### AI & APIs
- **AI Model**: Gemini AI Flash 2.0 for content processing
- **Framework**: CrewAI for multi-agent orchestration
- **News**: NewsAPI for financial news
- **Social**: X API for social sentiment
- **Market Data**: Polygon.io for real-time quotes
- **Trading**: Alpaca for real investments (Pro tier)

### Monetization
- **Subscriptions**: StoreKit integration
- **Tiers**: Free (limited), Basic ($9.99), Pro ($19.99)
- **Target**: 175K paying users for $2M monthly revenue

## 📊 Revenue Model

| Tier | Price | Users | Monthly Revenue |
|------|-------|-------|-----------------|
| Basic | $9.99 | 150,000 | $1,498,500 |
| Pro | $19.99 | 25,000 | $499,750 |
| **Total** | | **175,000** | **$1,998,250** |

## 🏗 Project Structure

```
VibeFinance/
├── Models/
│   ├── User.swift              # User and preferences models
│   ├── Feed.swift              # Feed content and reactions
│   ├── Quest.swift             # Gamified learning system
│   ├── Squad.swift             # Community investing
│   ├── Simulator.swift         # Virtual trading
│   └── Chat.swift              # AI chat system
├── Managers/
│   ├── AuthManager.swift       # Authentication logic
│   ├── UserManager.swift       # User profile management
│   ├── FeedManager.swift       # Content curation
│   ├── QuestManager.swift      # Quest system
│   ├── SquadManager.swift      # Squad management
│   ├── SimulatorManager.swift  # Virtual trading
│   ├── ChatManager.swift       # AI chat handling
│   └── SubscriptionManager.swift # StoreKit integration
├── Services/
│   ├── SupabaseService.swift   # Database operations
│   ├── AIService.swift         # Gemini AI integration
│   ├── NewsService.swift       # News and social media
│   └── MarketService.swift     # Market data and trading
├── Views/
│   ├── OnboardingView.swift    # App introduction
│   ├── AuthView.swift          # Login/signup
│   ├── MainTabView.swift       # Main navigation
│   └── PreferencesSetupView.swift # User onboarding
└── VibeFinanceApp.swift        # App entry point
```

## 🎨 Design Philosophy

- **Gen Z-Friendly**: Vibrant colors, emojis, casual language
- **Gamification**: XP, levels, streaks, achievements
- **Community**: Social features and collaborative investing
- **Education**: Learn through doing, not just reading
- **Accessibility**: Simple UI with clear navigation

## 🔧 Setup Instructions

### Prerequisites
- Xcode 15.0+
- iOS 17.0+
- Swift 5.9+

### API Keys Required
1. **Supabase**: Database and authentication
2. **Gemini AI**: Content processing and chat
3. **NewsAPI**: Financial news content
4. **X API**: Social media sentiment
5. **Polygon.io**: Real-time market data
6. **Alpaca**: Real trading (Pro tier)

### Installation
1. Clone the repository
2. Open `VibeFinance.xcodeproj` in Xcode
3. Add your API keys to the respective service files
4. Configure Supabase database schema
5. Set up StoreKit products in App Store Connect
6. Build and run on iOS Simulator or device

### Database Schema
```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE,
  username TEXT UNIQUE,
  preferences JSONB,
  subscription_status TEXT,
  xp INTEGER,
  level INTEGER,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- Additional tables for feeds, quests, squads, etc.
```

## 🎯 Target Audience

- **Primary**: Gen Z (18-26 years old)
- **Secondary**: Gen Alpha (10-17) and Millennials (27-42)
- **Geography**: US, UK, India, Brazil
- **Characteristics**: Digital natives, social-first, value experiences

## 📈 Growth Strategy

1. **Viral Features**: Squad investing and social sharing
2. **Gamification**: Daily quests and achievement systems
3. **Influencer Partnerships**: FinTok and finance YouTubers
4. **Referral Program**: Invite friends for bonus XP
5. **Content Marketing**: Educational TikToks and Instagram Reels

## 🔒 Security & Compliance

- **Data Encryption**: All user data encrypted at rest and in transit
- **Privacy**: GDPR and CCPA compliant
- **Financial**: Educational content only, not financial advice
- **Authentication**: Secure OAuth with Supabase
- **API Security**: Rate limiting and request validation

## 🚀 Future Roadmap

### Phase 1 (MVP)
- [x] Core app structure
- [x] User authentication
- [x] Basic feed and quests
- [x] Subscription system

### Phase 2 (Community)
- [ ] Squad investing features
- [ ] Real-time chat in squads
- [ ] Social sharing and referrals
- [ ] Advanced analytics

### Phase 3 (Scale)
- [ ] Real trading integration
- [ ] International expansion
- [ ] Advanced AI features
- [ ] Web platform

## 📱 App Store Information

- **Name**: WealthVibe
- **Category**: Finance
- **Age Rating**: 12+ (Financial content)
- **Supported Devices**: iPhone, iPad
- **Minimum iOS**: 17.0

## 🤝 Contributing

This is a proprietary project. For questions or collaboration opportunities, please contact the development team.

## 📄 License

Copyright © 2025 WealthVibe. All rights reserved.

---

**Built with ❤️ for the next generation of investors** 🚀💎
